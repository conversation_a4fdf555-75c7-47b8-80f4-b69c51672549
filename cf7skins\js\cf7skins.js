/******/ (function(modules) { // webpackBootstrap
/******/ 	// The module cache
/******/ 	var installedModules = {};
/******/
/******/ 	// The require function
/******/ 	function __webpack_require__(moduleId) {
/******/
/******/ 		// Check if module is in cache
/******/ 		if(installedModules[moduleId]) {
/******/ 			return installedModules[moduleId].exports;
/******/ 		}
/******/ 		// Create a new module (and put it into the cache)
/******/ 		var module = installedModules[moduleId] = {
/******/ 			i: moduleId,
/******/ 			l: false,
/******/ 			exports: {}
/******/ 		};
/******/
/******/ 		// Execute the module function
/******/ 		modules[moduleId].call(module.exports, module, module.exports, __webpack_require__);
/******/
/******/ 		// Flag the module as loaded
/******/ 		module.l = true;
/******/
/******/ 		// Return the exports of the module
/******/ 		return module.exports;
/******/ 	}
/******/
/******/
/******/ 	// expose the modules object (__webpack_modules__)
/******/ 	__webpack_require__.m = modules;
/******/
/******/ 	// expose the module cache
/******/ 	__webpack_require__.c = installedModules;
/******/
/******/ 	// define getter function for harmony exports
/******/ 	__webpack_require__.d = function(exports, name, getter) {
/******/ 		if(!__webpack_require__.o(exports, name)) {
/******/ 			Object.defineProperty(exports, name, { enumerable: true, get: getter });
/******/ 		}
/******/ 	};
/******/
/******/ 	// define __esModule on exports
/******/ 	__webpack_require__.r = function(exports) {
/******/ 		if(typeof Symbol !== 'undefined' && Symbol.toStringTag) {
/******/ 			Object.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });
/******/ 		}
/******/ 		Object.defineProperty(exports, '__esModule', { value: true });
/******/ 	};
/******/
/******/ 	// create a fake namespace object
/******/ 	// mode & 1: value is a module id, require it
/******/ 	// mode & 2: merge all properties of value into the ns
/******/ 	// mode & 4: return value when already ns object
/******/ 	// mode & 8|1: behave like require
/******/ 	__webpack_require__.t = function(value, mode) {
/******/ 		if(mode & 1) value = __webpack_require__(value);
/******/ 		if(mode & 8) return value;
/******/ 		if((mode & 4) && typeof value === 'object' && value && value.__esModule) return value;
/******/ 		var ns = Object.create(null);
/******/ 		__webpack_require__.r(ns);
/******/ 		Object.defineProperty(ns, 'default', { enumerable: true, value: value });
/******/ 		if(mode & 2 && typeof value != 'string') for(var key in value) __webpack_require__.d(ns, key, function(key) { return value[key]; }.bind(null, key));
/******/ 		return ns;
/******/ 	};
/******/
/******/ 	// getDefaultExport function for compatibility with non-harmony modules
/******/ 	__webpack_require__.n = function(module) {
/******/ 		var getter = module && module.__esModule ?
/******/ 			function getDefault() { return module['default']; } :
/******/ 			function getModuleExports() { return module; };
/******/ 		__webpack_require__.d(getter, 'a', getter);
/******/ 		return getter;
/******/ 	};
/******/
/******/ 	// Object.prototype.hasOwnProperty.call
/******/ 	__webpack_require__.o = function(object, property) { return Object.prototype.hasOwnProperty.call(object, property); };
/******/
/******/ 	// __webpack_public_path__
/******/ 	__webpack_require__.p = "";
/******/
/******/
/******/ 	// Load entry module and return exports
/******/ 	return __webpack_require__(__webpack_require__.s = "./src/cf7skins/index.js");
/******/ })
/************************************************************************/
/******/ ({

/***/ "./node_modules/@babel/runtime/helpers/defineProperty.js":
/*!***************************************************************!*\
  !*** ./node_modules/@babel/runtime/helpers/defineProperty.js ***!
  \***************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

var toPropertyKey = __webpack_require__(/*! ./toPropertyKey.js */ "./node_modules/@babel/runtime/helpers/toPropertyKey.js");
function _defineProperty(e, r, t) {
  return (r = toPropertyKey(r)) in e ? Object.defineProperty(e, r, {
    value: t,
    enumerable: !0,
    configurable: !0,
    writable: !0
  }) : e[r] = t, e;
}
module.exports = _defineProperty, module.exports.__esModule = true, module.exports["default"] = module.exports;

/***/ }),

/***/ "./node_modules/@babel/runtime/helpers/toPrimitive.js":
/*!************************************************************!*\
  !*** ./node_modules/@babel/runtime/helpers/toPrimitive.js ***!
  \************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

var _typeof = __webpack_require__(/*! ./typeof.js */ "./node_modules/@babel/runtime/helpers/typeof.js")["default"];
function toPrimitive(t, r) {
  if ("object" != _typeof(t) || !t) return t;
  var e = t[Symbol.toPrimitive];
  if (void 0 !== e) {
    var i = e.call(t, r || "default");
    if ("object" != _typeof(i)) return i;
    throw new TypeError("@@toPrimitive must return a primitive value.");
  }
  return ("string" === r ? String : Number)(t);
}
module.exports = toPrimitive, module.exports.__esModule = true, module.exports["default"] = module.exports;

/***/ }),

/***/ "./node_modules/@babel/runtime/helpers/toPropertyKey.js":
/*!**************************************************************!*\
  !*** ./node_modules/@babel/runtime/helpers/toPropertyKey.js ***!
  \**************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

var _typeof = __webpack_require__(/*! ./typeof.js */ "./node_modules/@babel/runtime/helpers/typeof.js")["default"];
var toPrimitive = __webpack_require__(/*! ./toPrimitive.js */ "./node_modules/@babel/runtime/helpers/toPrimitive.js");
function toPropertyKey(t) {
  var i = toPrimitive(t, "string");
  return "symbol" == _typeof(i) ? i : i + "";
}
module.exports = toPropertyKey, module.exports.__esModule = true, module.exports["default"] = module.exports;

/***/ }),

/***/ "./node_modules/@babel/runtime/helpers/typeof.js":
/*!*******************************************************!*\
  !*** ./node_modules/@babel/runtime/helpers/typeof.js ***!
  \*******************************************************/
/*! no static exports found */
/***/ (function(module, exports) {

function _typeof(o) {
  "@babel/helpers - typeof";

  return (module.exports = _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (o) {
    return typeof o;
  } : function (o) {
    return o && "function" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? "symbol" : typeof o;
  }, module.exports.__esModule = true, module.exports["default"] = module.exports), _typeof(o);
}
module.exports = _typeof, module.exports.__esModule = true, module.exports["default"] = module.exports;

/***/ }),

/***/ "./node_modules/balloon-css/balloon.min.css":
/*!**************************************************!*\
  !*** ./node_modules/balloon-css/balloon.min.css ***!
  \**************************************************/
/*! exports provided: default */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
// extracted by mini-css-extract-plugin
/* harmony default export */ __webpack_exports__["default"] = ({"font-awesome":"font-awesome___0v5p5"});

/***/ }),

/***/ "./src/cf7skins/index.js":
/*!*******************************!*\
  !*** ./src/cf7skins/index.js ***!
  \*******************************/
/*! no exports provided */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var balloon_css__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! balloon-css */ "./node_modules/balloon-css/balloon.min.css");

var util = __webpack_require__(/*! ../../src/visual/util/index.js */ "./src/visual/util/index.js");

// Follows webpack approach to expose global using library and libraryTarget
window["cf7svisual"] = window["cf7svisual"] || {};
window["cf7svisual"]["util"] = util;

// Get all tooltip/help elements
var elements = document.getElementsByClassName('balloon');
for (var i = 0; i < elements.length; i++) {
  // Get initial tooltip text
  var title = elements[i].getAttribute('title');
  var element = elements[i];

  // Empty/remove title attribute to disable default browser tooltip
  element.setAttribute('title', '');

  // Set balloon-css attributes
  element.setAttribute('data-balloon', title);
  if (!element.hasAttribute('data-balloon-pos')) {
    element.setAttribute('data-balloon-pos', 'right');
  }
  if (!element.hasAttribute('data-balloon-length')) {
    element.setAttribute('data-balloon-length', 'large');
  }
}

/***/ }),

/***/ "./src/visual/util/api.js":
/*!********************************!*\
  !*** ./src/visual/util/api.js ***!
  \********************************/
/*! exports provided: defaultTreeData, cf7sRequest */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "cf7sRequest", function() { return cf7sRequest; });
/* harmony import */ var _functions__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./functions */ "./src/visual/util/functions.js");
/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "defaultTreeData", function() { return _functions__WEBPACK_IMPORTED_MODULE_0__["defaultTreeData"]; });

/**
 * CF7 Skins Visual API
 * 
 * API util for handling various AJAX requests to the PHP backend
 *
 */



/**
 * AJAX post request function.
 * 
 * Replace previous functions visualSelectTemplate()and visualFormPOST()
 *
 * @link https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise (check supports for IE/Edge), replaced by 'whatwg-fetch'
 * @link https://developer.mozilla.org/en-US/docs/Web/API/Fetch_API (check supports for IE/Edge)
 * 
 * @param	{object}	postData		AJAX post data
 * @param	{string}	dataType		returned data type
 * 
 * @returns (void)
 * 
 * @since 0.5.0
 */
function cf7sRequest(postData) {
  var dataType = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 'text';
  // Allow addons to use their own nonce for security purpose during AJAX,
  // set to cf7svisual nonce for default @since 0.6.8
  if (!postData.nonce) {
    postData.nonce = window.cf7svisual.nonce; // add nonce for WordPress security check
  }
  var postBody = Object.keys(postData).map(function (k) {
    return encodeURIComponent(k) + "=" + encodeURIComponent(postData[k]);
  }).join('&');
  var postOptions = {
    method: 'POST',
    credentials: 'same-origin',
    headers: {
      'Content-Type': "application/x-www-form-urlencoded; charset=UTF-8"
    },
    body: postBody
  };

  // Start fetch for AJAX request
  var savedData = fetch(window.cf7svisual.ajaxurl, postOptions).then(function (response) {
    return dataType === 'json' ? response.json() : response.text();
  }).then(function (data) {
    if (data) {
      return data;
    } else {
      throw new Error("Error. Returned data: ".concat(data));
    }
  })["catch"](function (error) {
    console.error("Error: ".concat(error));
    return;
  });
  return Promise.resolve(savedData);
}


/***/ }),

/***/ "./src/visual/util/cf7sItems.js":
/*!**************************************!*\
  !*** ./src/visual/util/cf7sItems.js ***!
  \**************************************/
/*! exports provided: cf7sItems */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "cf7sItems", function() { return cf7sItems; });
/* harmony import */ var _babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/defineProperty */ "./node_modules/@babel/runtime/helpers/defineProperty.js");
/* harmony import */ var _babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var _functions__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./functions */ "./src/visual/util/functions.js");

function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0___default()(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }
 // @since 2.3.0

var _window$wp$i18n = _objectSpread({}, window.wp.i18n),
  __ = _window$wp$i18n.__; // @since 0.7.2
var applyFilters = wp.hooks.applyFilters;

/**
 * Define all CF7 Tags & CF7 Skins Items.
 * 
 * @since 0.4.0
 */
var cf7sItemsDefault = [/** CF7 Tags **/
{
  cf7sType: 'acceptance',
  cf7sSelectLabel: __('Acceptance (confirm)', 'contact-form-7-skins'),
  cf7sSelectGroup: 'cf7Tag',
  cf7Name: 'acceptance',
  cf7sLabel: '',
  cf7Required: false,
  cf7TagOptions: [{
    cf7Option: 'default_on',
    isChecked: false,
    optionLabel: __('Make this checkbox checked by default', 'contact-form-7-skins'),
    optionType: 'checkbox'
  }, {
    cf7Option: 'invert',
    isChecked: false,
    optionLabel: __('Make this work inversely', 'contact-form-7-skins'),
    optionType: 'checkbox'
  }, {
    cf7Option: 'optional',
    isChecked: true,
    optionLabel: __('Make this checkbox optional', 'contact-form-7-skins'),
    optionType: 'checkbox'
  }],
  cf7DefaultOn: false,
  cf7Invert: false,
  cf7Optional: true,
  cf7Content: '',
  // for condition field, not the same as paragraph cf7sContent @since 0.6.0
  cf7IdAttribute: '',
  cf7ClassAttribute: '',
  cf7sIcon: 'yes-alt',
  noChildren: true
}, {
  cf7sType: 'checkbox',
  cf7sSelectLabel: __('Checkbox (option)', 'contact-form-7-skins'),
  cf7sSelectGroup: 'cf7Tag',
  cf7Name: 'checkbox',
  cf7sLabel: '',
  cf7Required: false,
  cf7TagOptions: [{
    cf7Option: 'label_first',
    isChecked: false,
    optionLabel: __('Put a label first, a checkbox last', 'contact-form-7-skins'),
    optionType: 'checkbox'
  }, {
    cf7Option: 'use_label_element',
    isChecked: false,
    optionLabel: __('Wrap each item with label element', 'contact-form-7-skins'),
    optionType: 'checkbox'
  }, {
    cf7Option: 'exclusive',
    isChecked: false,
    optionLabel: __('Make checkboxes exclusive', 'contact-form-7-skins'),
    optionType: 'checkbox'
  }],
  cf7Options: [
  // Option Checked +
  {
    value: 'Option 1',
    isChecked: true
  }, {
    value: 'Option 2',
    isChecked: false
  }],
  cf7LabelFirst: false,
  cf7UseLabelElement: false,
  cf7Exclusive: false,
  cf7IdAttribute: '',
  cf7ClassAttribute: '',
  cf7sIcon: 'forms',
  noChildren: true
}, {
  cf7sType: 'date',
  cf7sSelectLabel: __('Date', 'contact-form-7-skins'),
  cf7sSelectGroup: 'cf7Tag',
  cf7Name: 'date',
  cf7sLabel: '',
  cf7Required: false,
  cf7TagOptions: [{
    cf7Option: '',
    optionLabel: __('Default value', 'contact-form-7-skins'),
    optionType: 'input'
  }, {
    cf7Option: 'placeholder',
    isChecked: false,
    optionLabel: __('Use this text as the placeholder of the field', 'contact-form-7-skins'),
    optionType: 'checkbox'
  }, {
    cf7Option: '',
    optionLabel: __('Range - min', 'contact-form-7-skins'),
    optionType: 'input'
  }, {
    cf7Option: '',
    optionLabel: __('Range - max', 'contact-form-7-skins'),
    optionType: 'input'
  }],
  cf7Values: '',
  cf7Placeholder: false,
  cf7Min: '',
  cf7Max: '',
  cf7IdAttribute: '',
  cf7ClassAttribute: '',
  cf7sIcon: 'calendar',
  noChildren: true
}, {
  cf7sType: 'email',
  cf7sSelectLabel: __('Email', 'contact-form-7-skins'),
  cf7sSelectGroup: 'cf7Tag',
  cf7Name: 'email',
  cf7sLabel: '',
  cf7Required: false,
  cf7TagOptions: [{
    cf7Option: '',
    optionLabel: 'Default value',
    optionType: 'input'
  }, {
    cf7Option: 'placeholder',
    isChecked: false,
    optionLabel: __('Use this text as the placeholder of the field', 'contact-form-7-skins'),
    optionType: 'checkbox'
  }, {
    cf7Option: 'akismet_author_email',
    isChecked: false,
    optionLabel: __("Akismet - this field requires author's email address", 'contact-form-7-skins'),
    optionType: 'checkbox'
  }],
  cf7Values: '',
  cf7Placeholder: false,
  cf7AkismetAuthorEmail: false,
  cf7IdAttribute: '',
  cf7ClassAttribute: '',
  cf7sIcon: 'email-alt',
  noChildren: true
}, {
  cf7sType: 'file',
  cf7sSelectLabel: __('File (upload)', 'contact-form-7-skins'),
  cf7sSelectGroup: 'cf7Tag',
  cf7Name: 'file',
  cf7sLabel: '',
  cf7Required: false,
  cf7TagOptions: [{
    cf7Option: 'limit',
    optionLabel: __('File size limit (bytes)', 'contact-form-7-skins'),
    optionType: 'input'
  }, {
    cf7Option: '',
    optionLabel: __('Acceptable file types', 'contact-form-7-skins'),
    optionType: 'select'
  }],
  cf7Limit: '',
  cf7FileTypes: '',
  cf7IdAttribute: '',
  cf7ClassAttribute: '',
  cf7sIcon: 'paperclip',
  noChildren: true
}, {
  cf7sType: 'number',
  cf7sSelectLabel: __('Number', 'contact-form-7-skins'),
  cf7sSelectGroup: 'cf7Tag',
  cf7Name: 'number',
  cf7sLabel: '',
  cf7Required: false,
  cf7TagOptions: [{
    cf7Option: '',
    optionLabel: 'Field Type',
    // Spinbox, Slider
    optionType: 'select'
  }, {
    cf7Option: '',
    optionLabel: __('Default value', 'contact-form-7-skins'),
    optionType: 'input'
  }, {
    cf7Option: 'placeholder',
    isChecked: false,
    optionLabel: __('Use this text as the placeholder of the field', 'contact-form-7-skins'),
    optionType: 'checkbox'
  }, {
    cf7Option: '',
    optionLabel: __('Range - min', 'contact-form-7-skins'),
    optionType: 'input'
  }, {
    cf7Option: '',
    optionLabel: __('Range - max', 'contact-form-7-skins'),
    optionType: 'input'
  }],
  cf7TagType: 'number',
  // number: Spinbox, range: Slider
  cf7Values: '',
  cf7Placeholder: '',
  cf7Min: '',
  cf7Max: '',
  cf7IdAttribute: '',
  cf7ClassAttribute: '',
  cf7sIcon: 'plus-alt2',
  noChildren: true
}, {
  cf7sType: 'quiz',
  cf7sSelectLabel: __('Quiz', 'contact-form-7-skins'),
  cf7sSelectGroup: 'cf7Tag',
  cf7Name: 'quiz',
  cf7sLabel: '',
  cf7Required: false,
  cf7Options: [
  // Question Answer +
  {
    question: __('Question 1', 'contact-form-7-skins'),
    answer: __('Answer 1', 'contact-form-7-skins')
  }, {
    question: __('Question 2', 'contact-form-7-skins'),
    answer: __('Answer 2', 'contact-form-7-skins')
  }],
  cf7IdAttribute: '',
  cf7ClassAttribute: '',
  cf7sIcon: 'editor-help',
  noChildren: true
}, {
  cf7sType: 'radio',
  cf7sSelectLabel: __('Radio Button (option)', 'contact-form-7-skins'),
  cf7sSelectGroup: 'cf7Tag',
  cf7Name: 'radio',
  cf7sLabel: '',
  cf7Required: false,
  cf7TagOptions: [{
    cf7Option: 'label_first',
    isChecked: false,
    optionLabel: __('Put a label first, a checkbox last', 'contact-form-7-skins'),
    optionType: 'checkbox'
  }, {
    cf7Option: 'use_label_element',
    isChecked: false,
    optionLabel: __('Wrap each item with label element', 'contact-form-7-skins'),
    optionType: 'checkbox'
  }],
  cf7Options: [
  // Option Default +
  {
    value: __('Option 1', 'contact-form-7-skins'),
    // default: 1
    isChecked: true // radio-button
  }, {
    value: __('Option 2', 'contact-form-7-skins'),
    isChecked: false
  }],
  cf7LabelFirst: false,
  cf7UseLabelElement: false,
  cf7IdAttribute: '',
  cf7ClassAttribute: '',
  cf7sIcon: 'marker',
  noChildren: true
}, {
  cf7sType: 'select',
  cf7sSelectLabel: __('Select (dropdown)', 'contact-form-7-skins'),
  cf7sSelectGroup: 'cf7Tag',
  cf7Name: 'select',
  cf7sLabel: '',
  cf7Required: false,
  cf7TagOptions: [{
    cf7Option: '',
    isChecked: false,
    optionLabel: __('Allow multiple selections', 'contact-form-7-skins'),
    optionType: 'checkbox'
  }, {
    cf7Option: '',
    isChecked: false,
    optionLabel: __('Insert a blank item as the first option', 'contact-form-7-skins'),
    optionType: 'checkbox'
  }],
  cf7Options: [
  // Option Checked +
  {
    value: __('Option 1', 'contact-form-7-skins'),
    isChecked: true
  }, {
    value: __('Option 2', 'contact-form-7-skins'),
    isChecked: false
  }],
  cf7Multiple: false,
  cf7IncludeBlank: false,
  cf7IdAttribute: '',
  cf7ClassAttribute: '',
  cf7sIcon: 'list-view',
  noChildren: true
}, {
  cf7sType: 'submit',
  cf7sSelectLabel: __('Submit', 'contact-form-7-skins'),
  cf7sSelectGroup: 'cf7Tag',
  cf7Name: 'submit',
  cf7sLabel: 'Submit',
  // used as submit value
  cf7Values: '',
  cf7Required: false,
  cf7IdAttribute: '',
  cf7ClassAttribute: '',
  cf7sIcon: 'button',
  noChildren: true
}, {
  cf7sType: 'tel',
  cf7sSelectLabel: __('Telephone', 'contact-form-7-skins'),
  cf7sSelectGroup: 'cf7Tag',
  cf7Name: 'tel',
  cf7sLabel: '',
  cf7Required: false,
  cf7TagOptions: [{
    cf7Option: '',
    optionLabel: __('Default value', 'contact-form-7-skins'),
    optionType: 'input'
  }, {
    cf7Option: 'placeholder',
    isChecked: false,
    optionLabel: __('Use this text as the placeholder of the field', 'contact-form-7-skins'),
    optionType: 'checkbox'
  }],
  cf7Values: '',
  cf7Placeholder: false,
  cf7IdAttribute: '',
  cf7ClassAttribute: '',
  cf7sIcon: 'phone',
  noChildren: true
}, {
  cf7sType: 'text',
  cf7sSelectLabel: __('Text (short text)', 'contact-form-7-skins'),
  cf7sSelectGroup: 'cf7Tag',
  cf7Name: 'text',
  cf7sLabel: '',
  cf7Required: false,
  cf7TagOptions: [{
    cf7Option: '',
    optionLabel: __('Default value', 'contact-form-7-skins'),
    optionType: 'input'
  }, {
    cf7Option: 'placeholder',
    isChecked: false,
    optionLabel: __('Use this text as the placeholder of the field', 'contact-form-7-skins'),
    optionType: 'checkbox'
  }, {
    cf7Option: 'akismet_author_email',
    isChecked: false,
    optionLabel: __("Akismet - this field requires author's email address", 'contact-form-7-skins'),
    optionType: 'checkbox'
  }],
  cf7Values: '',
  cf7Placeholder: false,
  cf7AkismetAuthor: false,
  cf7IdAttribute: '',
  cf7ClassAttribute: '',
  cf7sIcon: 'editor-textcolor',
  noChildren: true
}, {
  cf7sType: 'textarea',
  cf7sSelectLabel: __('Textarea (long text)', 'contact-form-7-skins'),
  cf7sSelectGroup: 'cf7Tag',
  cf7Name: 'textarea',
  cf7sLabel: '',
  cf7Required: false,
  cf7TagOptions: [{
    cf7Option: '',
    optionLabel: __('Default value', 'contact-form-7-skins'),
    optionType: 'input'
  }, {
    cf7Option: 'placeholder',
    isChecked: false,
    optionLabel: __('Use this text as the placeholder of the field', 'contact-form-7-skins'),
    optionType: 'checkbox'
  }],
  cf7Values: '',
  cf7Placeholder: false,
  cf7IdAttribute: '',
  cf7ClassAttribute: '',
  cf7sIcon: 'format-aside',
  noChildren: true
}, {
  cf7sType: 'url',
  cf7sSelectLabel: __('URL (website link)', 'contact-form-7-skins'),
  cf7sSelectGroup: 'cf7Tag',
  cf7Name: 'url',
  cf7sLabel: '',
  cf7Required: false,
  cf7TagOptions: [{
    cf7Option: '',
    optionLabel: __('Default value', 'contact-form-7-skins'),
    optionType: 'input'
  }, {
    cf7Option: 'placeholder',
    isChecked: false,
    optionLabel: __('Use this text as the placeholder of the field', 'contact-form-7-skins'),
    optionType: 'checkbox'
  }, {
    cf7Option: 'akismet_author_email',
    isChecked: false,
    optionLabel: __("Akismet - this field requires author's email address", 'contact-form-7-skins'),
    optionType: 'checkbox'
  }],
  cf7Values: '',
  cf7Placeholder: false,
  cf7AkismetAuthorUrl: false,
  cf7IdAttribute: '',
  cf7ClassAttribute: '',
  cf7sIcon: 'admin-links',
  noChildren: true
}, {
  cf7sType: 'recaptcha',
  // @since 0.5.4
  cf7sSelectLabel: 'reCAPTCHA',
  cf7sSelectGroup: 'cf7Tag',
  cf7Name: 'recaptcha',
  cf7sLabel: '',
  cf7IdAttribute: '',
  cf7Size: '',
  cf7Theme: '',
  cf7ClassAttribute: '',
  cf7sIcon: 'update',
  noChildren: true
}, /** CF7 Skins Items **/
{
  cf7sType: 'fieldset',
  cf7sSelectLabel: __('Fieldset (with legend)', 'contact-form-7-skins'),
  cf7sSelectGroup: 'cf7sItem',
  cf7Name: 'fieldset',
  cf7sLabel: __('Legend ..', 'contact-form-7-skins'),
  // used as legend - has default value
  cf7sIcon: 'category'
  // SK - why aren't we setting 'noChildren' key here
}, {
  cf7sType: 'list-ol',
  cf7sSelectLabel: __('List - ol', 'contact-form-7-skins'),
  cf7sSelectGroup: 'cf7sItem',
  cf7Name: 'listol',
  cf7sLabel: '',
  // not displayed at this stage - consider allowing user to add this
  cf7sIcon: 'editor-ol',
  noChildren: false
}, {
  cf7sType: 'list-li',
  cf7sSelectLabel: __('List Item - li', 'contact-form-7-skins'),
  cf7sSelectGroup: 'cf7sItem',
  cf7Name: 'listli',
  cf7sLabel: '',
  // not displayed at this stage - consider allowing user to add this
  cf7sIcon: 'editor-ul',
  noChildren: false
}, {
  cf7sType: 'paragraph',
  cf7sSelectLabel: __('Paragraph - p', 'contact-form-7-skins'),
  cf7sSelectGroup: 'cf7sItem',
  cf7Name: 'paragraph',
  cf7sContent: '',
  // @previous: paragraph. Permitted content: https://developer.mozilla.org/en-US/docs/Web/HTML/Content_categories#Phrasing_content
  cf7sIcon: 'editor-paragraph',
  noChildren: true
}];
var cf7sItems = applyFilters('cf7sItems', [].concat(cf7sItemsDefault));


/***/ }),

/***/ "./src/visual/util/cf7sRules.js":
/*!**************************************!*\
  !*** ./src/visual/util/cf7sRules.js ***!
  \**************************************/
/*! exports provided: cf7sDropRules, cf7sSurroundingRules, cf7sDuplicateRules */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "cf7sDropRules", function() { return cf7sDropRules; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "cf7sSurroundingRules", function() { return cf7sSurroundingRules; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "cf7sDuplicateRules", function() { return cf7sDuplicateRules; });
/* harmony import */ var _babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/defineProperty */ "./node_modules/@babel/runtime/helpers/defineProperty.js");
/* harmony import */ var _babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var _cf7sItems__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./cf7sItems */ "./src/visual/util/cf7sItems.js");
/* harmony import */ var lodash__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! lodash */ "lodash");
/* harmony import */ var lodash__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(lodash__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var _functions__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./functions */ "./src/visual/util/functions.js");

function _createForOfIteratorHelper(r, e) { var t = "undefined" != typeof Symbol && r[Symbol.iterator] || r["@@iterator"]; if (!t) { if (Array.isArray(r) || (t = _unsupportedIterableToArray(r)) || e && r && "number" == typeof r.length) { t && (r = t); var _n = 0, F = function F() {}; return { s: F, n: function n() { return _n >= r.length ? { done: !0 } : { done: !1, value: r[_n++] }; }, e: function e(r) { throw r; }, f: F }; } throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method."); } var o, a = !0, u = !1; return { s: function s() { t = t.call(r); }, n: function n() { var r = t.next(); return a = r.done, r; }, e: function e(r) { u = !0, o = r; }, f: function f() { try { a || null == t["return"] || t["return"](); } finally { if (u) throw o; } } }; }
function _unsupportedIterableToArray(r, a) { if (r) { if ("string" == typeof r) return _arrayLikeToArray(r, a); var t = {}.toString.call(r).slice(8, -1); return "Object" === t && r.constructor && (t = r.constructor.name), "Map" === t || "Set" === t ? Array.from(r) : "Arguments" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray(r, a) : void 0; } }
function _arrayLikeToArray(r, a) { (null == a || a > r.length) && (a = r.length); for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e]; return n; }
function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0___default()(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }
/**
 * CF7 Skins Visual Rules.
 * 
 * Field/node event rules for RST
 *   - drop a node from Select or elsewhere in Form
 *   - click a node from Select
 *   - copy/duplicate within Form
 * 
 * Functions:
 *   cf7sAddonRules -
 *   cf7sDropRules - 
 *   cf7sSurroundingRules - 
 *   cf7sDuplicateRules -
 *   cf7sCountType - 
 *   cf7sHasChildren - 
 * 
 * @since 0.4.0
 */

 // @since 0.6.1

var _window$wp$i18n = _objectSpread({}, window.wp.i18n),
  sprintf = _window$wp$i18n.sprintf,
  __ = _window$wp$i18n.__; // @since 0.7.2
var applyFilters = wp.hooks.applyFilters;

/**
 * Get list-ol default property from cf7sItems.
 * 
 * OL node from cf7sItems includes LI as default children
 * Need to clone cf7sItems to avoid modification by CF7 Skins rules.
 * 
 * @since 0.6.1
 */
var nodeOL = Object(lodash__WEBPACK_IMPORTED_MODULE_2__["cloneDeep"])(_cf7sItems__WEBPACK_IMPORTED_MODULE_1__["cf7sItems"]).filter(function (obj) {
  return obj.cf7sType === 'list-ol';
})[0];

/**
 * Get list-li default property from cf7sItems.
 * 
 * Need to clone cf7sItems to avoid modification by CF7 Skins rules.
 * 
 * @since 0.6.1
 */
var nodeLI = Object(lodash__WEBPACK_IMPORTED_MODULE_2__["cloneDeep"])(_cf7sItems__WEBPACK_IMPORTED_MODULE_1__["cf7sItems"]).filter(function (obj) {
  return obj.cf7sType === 'list-li';
})[0];

/**
 * Get addon rules for drop, surrounding, and duplicate.
 *
 * @param {String}		type		rule type, drop, surround, or duplicate
 *									See each rule below for each addon function parameter
 *
 * @return {Array} 	all registered addon rules for specific type
 * 
 * @since 0.7.2
 */
function cf7sAddonRules() {
  var type = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 'drop';
  var rules = {};

  /**
   * Apply addons rules. Addon can register their own rule functions using object 
   * with a key and function pairs. The function will have parameters as listed below.
   * Function should return boolean value true to enable or false to disable drop, surround,
   * or duplicate. For example:
   * addFilter( 'cf7svisual.cf7sRules', 'cf7svisual', ( filter ) => {
   *    filter.push( { drop: myDropFunc, surround: mySurroundFunc } );
   *    return filter;
   * });	 
   * 
   * @since 0.7.0
   */
  var cf7sRules = applyFilters('cf7svisual.cf7sRules', []);
  if (!!cf7sRules) {
    // only if not empty
    for (var i = 0; i < cf7sRules.length; ++i) {
      // loop for each registered addon

      for (var _type in cf7sRules[i]) {
        // loop for each addon rule object

        if (!rules[_type]) {
          // set array if type rule does not exist
          rules[_type] = [];
        }
        rules[_type].push(cf7sRules[i][_type]); // add addon rule to array
      }
    }
  }
  if (!!rules && !!rules[type]) {
    // requested rule type
    return rules[type];
  }
}

/**
 * Drop rules for current node into other parent.
 * 
 * Return false to prevent node from dropping in the given location.
 * @link https://github.com/fritz-c/react-sortable-tree
 *
 * @params 	node			(object) 		current dropped node
 *	  		prevPath		(number/string)	previous path
 *	  		prevParent		(object)		previous parent object
 *	  		prevTreeIndex	(number) 		previous tree index
 *	  		nextPath		(number/string)	next path
 *	  		nextParent		(object)		next parent object
 *	  		nextTreeIndex	(number)		next tree index
 *	  		treeData		(object)		current tree data object
 * 
 * @return (Boolean)
 * 
 * @since 0.4.0
 */
function cf7sDropRules(object) {
  var _object = _objectSpread({}, object),
    node = _object.node,
    prevTreeIndex = _object.prevTreeIndex,
    nextParent = _object.nextParent;

  // Read global treeData if is set by addons, or use object treeData
  // @since 0.6.8
  var treeData = Object(_functions__WEBPACK_IMPORTED_MODULE_3__["getVisualVar"])('treeData') || object.treeData;

  /**
   * Rule: CF7 Tag
   * Only one CF7 tag inside list-li
   * Check while moving tag into another LI parent, and has children children
   */
  if (nextParent && nextParent.cf7sType === 'list-li' && nextParent.children) {
    // Loop all parent's children.
    // Not sure why nextParent.children contains dropped node candidate, even if it has not been dropped yet. Bug?
    // Count total CF7 tags inside parent.
    var countCF7Tags = 0;
    var _iterator = _createForOfIteratorHelper(nextParent.children),
      _step;
    try {
      for (_iterator.s(); !(_step = _iterator.n()).done;) {
        var child = _step.value;
        var _iterator2 = _createForOfIteratorHelper(_cf7sItems__WEBPACK_IMPORTED_MODULE_1__["cf7sItems"]),
          _step2;
        try {
          for (_iterator2.s(); !(_step2 = _iterator2.n()).done;) {
            var tag = _step2.value;
            // loop cf7sItems
            if (child.cf7sType === tag.cf7sType && 'cf7Tag' === tag.cf7sSelectGroup) {
              countCF7Tags++;
            }
          }
        } catch (err) {
          _iterator2.e(err);
        } finally {
          _iterator2.f();
        }
      }

      // Prevent if list-li already has CF7 tag.
    } catch (err) {
      _iterator.e(err);
    } finally {
      _iterator.f();
    }
    if (countCF7Tags > 1) {
      console.warn('Only one CF7 tag for each list');
      return false;
    }
  }

  /**
   * Rule: reCAPTCHA 
   * Prevent drop/click if CF7 reCAPTCHA integration is not activated 
   * using window.cf7svisual.integration.reCAPTCHA (on hold)
   * Only one reCAPTCHA tag per form
   * New dragged reCAPTCHA does not have prevTreeIndex
   * Disable duplication if node has recaptcha children
   */
  if (!!node && null == prevTreeIndex && 'recaptcha' === node.cf7sType) {
    // Count and prevent if form already has a reCAPTCHA
    // 0 = no reCAPTCHA found in tree
    if (cf7sCountType(node.cf7sType, treeData) > 0 && null == prevTreeIndex) {
      console.warn(__('Only one reCAPTCHA per form allowed.', 'contact-form-7-skins'));
      return false;
    }
  }

  /**
   * Rule: only one submit per form
   * For duplicate event, current node is empty
   * New dragged submit has undefined prevTreeIndex
   */
  if (!!node && null == prevTreeIndex && 'submit' === node.cf7sType) {
    // Count and prevent if form already has a submit tag
    // 0 = no submit found in tree
    if (cf7sCountType(node.cf7sType, treeData) > 0) {
      console.warn(__('Only one submit for each form.', 'contact-form-7-skins'));
      return false;
    }
  }

  /**
   * Rule: disable drop for cf7sRoot to any node
   * Return if is cf7sRoot and next parent target is not null
   * @since 2.1.2
   */
  if (!!node.cf7sRoot && !!nextParent) {
    console.warn(__('Root node can not be dragged to other node.', 'contact-form-7-skins'));
    return false;
  }

  // Apply addons rules @since 0.7.0
  var addonRules = cf7sAddonRules('drop');
  if (!!addonRules && !!addonRules.length) {
    // requested rule type
    for (var i = 0; i < addonRules.length; ++i) {
      // loop addon rule
      if (!addonRules[i](object)) return false; // bail early, no need to call subsequent rules
    }
  }

  // Return nextParent if no rules applied to prevent drop
  // or return message
  return !nextParent || !nextParent.noChildren;
}

/**
 * OL/LI surrounding rules for tag, based on parent node.
 * 
 * @param {Object}	node			current dropped/clicked node
 * @param {Object}	parentNode		next parent node to move
 *
 * @return {Object} initial node or node with surrounding element
 * 
 * @since 0.4.0
 */
function cf7sSurroundingRules(node, parentNode) {
  // Rule: Add LI as default children of OL for new node
  // New node has no children, and no need to return.
  // nodeLI can be rewritten by other rules, LI should not contains any children, 
  // @since 0.6.1	
  if (node.cf7sType === 'list-ol' && !node.children) {
    var LI = Object(_functions__WEBPACK_IMPORTED_MODULE_3__["randomizeName"])(Object(lodash__WEBPACK_IMPORTED_MODULE_2__["cloneDeep"])(nodeLI)); // clone to avoid have same name as others @since 0.6.3
    LI.children = []; // empty LI children
    node.children = [_objectSpread({}, LI)]; // add LI as OL children
    node.expanded = true; // make it expanded
  }

  // Rule: Add enclosed OL & LI to Fieldsets
  // @since 0.6.1
  if (!parentNode || parentNode.cf7sType === 'fieldset') {
    // Draged/clicked Fielset from SelectItem has no children. See cf7sItems for Fieldset.
    // Only for new Fieldset, not moving it from other parent in tree
    if (!node.children && 'fieldset' === node.cf7sType) {
      // Clone to avoid have same name as others @since 0.6.3
      var OL = Object(_functions__WEBPACK_IMPORTED_MODULE_3__["randomizeName"])(Object(lodash__WEBPACK_IMPORTED_MODULE_2__["cloneDeep"])(nodeOL));
      var _LI = Object(_functions__WEBPACK_IMPORTED_MODULE_3__["randomizeName"])(Object(lodash__WEBPACK_IMPORTED_MODULE_2__["cloneDeep"])(nodeLI));
      OL.children = [_objectSpread({}, _LI)]; // add LI as OL children, LI as default OL children might be removed
      OL.expanded = true; // make it expanded
      node.children = [_objectSpread({}, OL)]; // add OL as Fieldset children
      node.expanded = true; // make it expanded
      return node;
    }
  }

  // Rule: CF7 Tag (if not reCAPTCHA or Submit) added into Form or Fieldset should be wrapped with OL > LI
  // @since 0.6.1

  if (!parentNode || parentNode.cf7sType === 'fieldset' || false === parentNode.noChildren && ['list-ol', 'list-li'].indexOf(parentNode.cf7sType) < 0) {
    if ('cf7Tag' === node.cf7sSelectGroup) {
      if (['recaptcha', 'submit'].indexOf(node.cf7sType) < 0) {
        // Clone to avoid have same name as others @since 0.6.3
        var _OL = Object(_functions__WEBPACK_IMPORTED_MODULE_3__["randomizeName"])(Object(lodash__WEBPACK_IMPORTED_MODULE_2__["cloneDeep"])(nodeOL));
        var _LI2 = Object(_functions__WEBPACK_IMPORTED_MODULE_3__["randomizeName"])(Object(lodash__WEBPACK_IMPORTED_MODULE_2__["cloneDeep"])(nodeLI));
        _LI2.children = [_objectSpread({}, node)];
        _LI2.expanded = true; // make it expanded

        // Default list-ol contains list-li as a children, 
        // Overwrite dragged list-li node as a children to prevent lost
        _OL.children = [_objectSpread({}, _LI2)];
        _OL.expanded = true; // make it expanded

        return _OL;
      }
    }
  }

  // Rule: Add surrounding li to node inside an ol
  // If parent is a list-ol and selected node is not a list-li
  if (parentNode && parentNode.cf7sType === 'list-ol' && node.cf7sType !== 'list-li') {
    var _LI3 = Object(_functions__WEBPACK_IMPORTED_MODULE_3__["randomizeName"])(Object(lodash__WEBPACK_IMPORTED_MODULE_2__["cloneDeep"])(nodeLI)); // clone to avoid have same name as others @since 0.6.3
    _LI3.expanded = true; // expand after added
    _LI3.children = [_objectSpread({}, node)]; // add current selected node
    return _LI3;

    // Rule: Add surrounding ol for list-li if parent is not a list-ol
    // Only if current node type is a list-li ( with children if available ) and
    // without target parentNode or not a list-ol target parentNode.
  } else if (node.cf7sType === 'list-li' && parentNode === null || node.cf7sType === 'list-li' && parentNode.cf7sType !== 'list-ol') {
    var _OL2 = Object(_functions__WEBPACK_IMPORTED_MODULE_3__["randomizeName"])(Object(lodash__WEBPACK_IMPORTED_MODULE_2__["cloneDeep"])(nodeOL)); // clone to avoid have same name as others @since 0.6.3
    _OL2.children = [_objectSpread({}, node)]; // overwrite dragged list-li node as a children to prevent lost
    _OL2.expanded = true; // expand after added
    return _OL2;
  }

  // Apply addons rules @since 0.7.2
  var addonRules = cf7sAddonRules('surround');
  if (!!addonRules && !!addonRules.length) {
    // requested rule type
    for (var i = 0; i < addonRules.length; ++i) {
      // loop addon rule
      node = addonRules[i]({
        node: node,
        parentNode: parentNode,
        nodeOL: nodeOL,
        nodeLI: nodeLI,
        randomizeName: _functions__WEBPACK_IMPORTED_MODULE_3__["randomizeName"]
      }); // run addon function and return it's value
    }
  }
  return node;
}

/**
 * Duplicating rules.
 *
 * @param {Object}	rowInfo		current tree data
 *
 * @return {Boolean/String} true to enable duplicating or string message to disable
 * 
 * @since 0.6.3
 */
function cf7sDuplicateRules(rowInfo) {
  var _rowInfo = _objectSpread({}, rowInfo),
    node = _rowInfo.node,
    parentNode = _rowInfo.parentNode;

  // Rule: disable duplicate submit or recaptcha
  if ('recaptcha' === node.cf7sType || 'submit' === node.cf7sType) {
    return sprintf(__('Only one %s allowed in a form.', 'contact-form-7-skins'), node.cf7sType);
  }

  // Rule: disable if node contains children of submit or recaptcha
  if (cf7sHasChildren(node, 'recaptcha')) {
    return __('Node has recaptcha children. Only one recaptcha allowed in a form.', 'contact-form-7-skins');
  }
  if (cf7sHasChildren(node, 'submit')) {
    return __('Node has submit children. Only one submit allowed in a form.', 'contact-form-7-skins');
  }

  // Rule: only one CF7 tag inside list-li
  if (!!parentNode && 'list-li' === parentNode.cf7sType && 'cf7Tag' === node.cf7sSelectGroup) {
    return "surrounding";
  }

  // Apply addons rules @since 0.7.2
  var addonRules = cf7sAddonRules('duplicate');
  if (!!addonRules && !!addonRules.length) {
    // requested rule type
    for (var i = 0; i < addonRules.length; ++i) {
      // loop addon rule
      addonRules[i](rowInfo); // run addon function and return it's value
    }
  }
  return true;
}

/**
 * Count nodes by specific type in tree.
 *
 * @param {String}	type		node type to check and count
 * @param {Object}	treeData	current tree data
 *
 * @return {TYPE} the number of found nodes
 * 
 * @since 0.6.1
 */
function cf7sCountType(type, treeData) {
  var count = 0;
  var countNodes = function countNodes(nodes) {
    var _iterator3 = _createForOfIteratorHelper(nodes),
      _step3;
    try {
      for (_iterator3.s(); !(_step3 = _iterator3.n()).done;) {
        var node = _step3.value;
        if (type === node.cf7sType) {
          count++;
        }
        if (!!node.children) {
          // check if have children
          countNodes(node.children); // recursive loop for children
        }
      }
    } catch (err) {
      _iterator3.e(err);
    } finally {
      _iterator3.f();
    }
  };
  countNodes(treeData);
  return count;
}

/**
 * Check if given node has specific node type in children, grand-children.
 *
 * @param {Object}	node	current tree data
 * @param {String}	type	the children node type to find
 *
 * @return {Boolean}
 * 
 * @since 0.6.3
 */
function cf7sHasChildren(node, type) {
  if (!node.children) {
    // bail early if has no children
    return false;
  }
  var _iterator4 = _createForOfIteratorHelper(node.children),
    _step4;
  try {
    for (_iterator4.s(); !(_step4 = _iterator4.n()).done;) {
      var child = _step4.value;
      if (type === child.cf7sType) {
        // child type exists, bail return true
        return true;
      }
      return cf7sHasChildren(child, type); // recursive loop for children
    }
  } catch (err) {
    _iterator4.e(err);
  } finally {
    _iterator4.f();
  }
}

/***/ }),

/***/ "./src/visual/util/functions.js":
/*!**************************************!*\
  !*** ./src/visual/util/functions.js ***!
  \**************************************/
/*! exports provided: defaultTreeData, saveVisualForm, randomizeName, mergeDefault, versionCompare, getVisualVar, isDevelopment */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "defaultTreeData", function() { return defaultTreeData; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "saveVisualForm", function() { return saveVisualForm; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "randomizeName", function() { return randomizeName; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "mergeDefault", function() { return mergeDefault; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "versionCompare", function() { return versionCompare; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "getVisualVar", function() { return getVisualVar; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "isDevelopment", function() { return isDevelopment; });
/* harmony import */ var _babel_runtime_helpers_typeof__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/typeof */ "./node_modules/@babel/runtime/helpers/typeof.js");
/* harmony import */ var _babel_runtime_helpers_typeof__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_babel_runtime_helpers_typeof__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var _babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/defineProperty */ "./node_modules/@babel/runtime/helpers/defineProperty.js");
/* harmony import */ var _babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ "react");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-dom */ "react-dom");
/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react_dom__WEBPACK_IMPORTED_MODULE_3__);
/* harmony import */ var lodash__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! lodash */ "lodash");
/* harmony import */ var lodash__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(lodash__WEBPACK_IMPORTED_MODULE_4__);
/* harmony import */ var ___WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./ */ "./src/visual/util/index.js");
/* harmony import */ var _api__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./api */ "./src/visual/util/api.js");


function _createForOfIteratorHelper(r, e) { var t = "undefined" != typeof Symbol && r[Symbol.iterator] || r["@@iterator"]; if (!t) { if (Array.isArray(r) || (t = _unsupportedIterableToArray(r)) || e && r && "number" == typeof r.length) { t && (r = t); var _n = 0, F = function F() {}; return { s: F, n: function n() { return _n >= r.length ? { done: !0 } : { done: !1, value: r[_n++] }; }, e: function e(r) { throw r; }, f: F }; } throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method."); } var o, a = !0, u = !1; return { s: function s() { t = t.call(r); }, n: function n() { var r = t.next(); return a = r.done, r; }, e: function e(r) { u = !0, o = r; }, f: function f() { try { a || null == t["return"] || t["return"](); } finally { if (u) throw o; } } }; }
function _unsupportedIterableToArray(r, a) { if (r) { if ("string" == typeof r) return _arrayLikeToArray(r, a); var t = {}.toString.call(r).slice(8, -1); return "Object" === t && r.constructor && (t = r.constructor.name), "Map" === t || "Set" === t ? Array.from(r) : "Arguments" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray(r, a) : void 0; } }
function _arrayLikeToArray(r, a) { (null == a || a > r.length) && (a = r.length); for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e]; return n; }
function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_1___default()(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }
/**
 * CF7 Skins Visual Utility Functions.
 * 
 * @since 0.6.1
 */






var _window$wp$i18n = _objectSpread({}, window.wp.i18n),
  sprintf = _window$wp$i18n.sprintf,
  __ = _window$wp$i18n.__;
var applyFilters = wp.hooks.applyFilters;
var _window$wp$data = _objectSpread({}, window.wp.data),
  dispatch = _window$wp$data.dispatch,
  select = _window$wp$data.select; // @since 2.5.0

/**
 * Setup default treeData for RST and add-ons.
 * 
 * @return {Object}		treeData		default RST treeData
 * 
 * @since 0.7.0
 */
function defaultTreeData() {
  var tags = [],
    treeData = [];

  // Arrange cf7sItems based on cf7sType
  var _iterator = _createForOfIteratorHelper(___WEBPACK_IMPORTED_MODULE_5__["cf7sItems"]),
    _step;
  try {
    for (_iterator.s(); !(_step = _iterator.n()).done;) {
      var item = _step.value;
      tags[item.cf7sType] = randomizeName(item);
    }

    // Set default treeData
  } catch (err) {
    _iterator.e(err);
  } finally {
    _iterator.f();
  }
  tags['fieldset'].expanded = true; // make fieldset children expanded
  tags['fieldset'].cf7sLabel = __('Legend', 'contact-form-7-skins'); // add label
  treeData.push(tags['fieldset']); // insert fieldset

  tags['list-ol'].expanded = true;
  treeData[0].children = [_objectSpread({}, tags['list-ol'])]; // insert list-ol as fieldset children

  tags['text'].cf7sLabel = __('Your Name (required)', 'contact-form-7-skins');
  tags['text'].cf7Required = true;
  tags['list-li'].expanded = true;
  tags['list-li'].children = [_objectSpread({}, tags['text'])];
  treeData[0].children[0].children = [_objectSpread({}, tags['list-li'])]; // insert list-li to list-ol

  tags['email'].cf7sLabel = __('Email Address (required)', 'contact-form-7-skins');
  tags['email'].cf7Required = true;
  tags['list-li'] = randomizeName(tags['list-li']); // randomize list-li
  tags['list-li'].children = [_objectSpread({}, tags['email'])]; // insert email as list-li children
  treeData[0].children[0].children[1] = _objectSpread({}, tags['list-li']); // insert list-li to list-ol

  tags['textarea'].cf7sLabel = __('Your Message', 'contact-form-7-skins');
  tags['list-li'] = randomizeName(tags['list-li']); // randomize list-li
  tags['list-li'].children = [_objectSpread({}, tags['textarea'])]; // insert textarea as list-li children
  treeData[0].children[0].children[2] = _objectSpread({}, tags['list-li']); // insert list-li to list-ol

  tags['paragraph'].cf7sContent = __('* Required', 'contact-form-7-skins');
  treeData[0].children[1] = _objectSpread({}, tags['paragraph']); // insert paragraph as fieldset children

  tags['submit'].cf7sLabel = __('Send', 'contact-form-7-skins');
  treeData.push(tags['submit']); // insert submit button

  return treeData;
}

/**
 * Save Visual Form.
 * 
 * @param {Array}		tabIds		Registered Tabs
 * 
 * @since 0.4.0
 */
function saveVisualForm() {
  var _dispatch = dispatch('cf7svisual'),
    setNotice = _dispatch.setNotice;
  var state = _objectSpread({}, select('cf7svisual').getStates());

  // Read global treeData if is set by addons, or use Visual treeData
  var treeData = window.cf7svisual.treeData /* @since 0.7.0 */ || state.treeData.slice();
  var _window = window,
    cf7svisual = _window.cf7svisual; // window.cf7svisual

  if ('development' === cf7svisual.environment) {
    // logging @since 0.7.0
    console.log('on save:', treeData);
    console.log(JSON.stringify(treeData));
  }

  // Return if window.cf7svisual does not exist in Production, or if saved in Dev mode.
  if (!cf7svisual || !document.getElementById('post_ID')) {
    setNotice(__('Can not save! window.cf7svisual or post ID does not exist.', 'contact-form-7-skins'), 'error');
    return;
  }

  // Get form id and <form/> element
  var form_id = document.getElementById('post_ID').getAttribute('value');
  var CF7Form = document.getElementById('wpcf7-admin-form-element');

  // Input to add to CF7 form before CF7 form submit
  var appendedInput = document.createElement('input');

  // If there isn't a form already, then set the critical input element
  // attributes first before submitting the form
  if (!form_id || form_id < 0) {
    appendedInput.setAttribute('type', 'hidden');
    appendedInput.setAttribute('name', 'cf7s-visual');
    appendedInput.setAttribute('value', JSON.stringify(treeData));
    CF7Form.appendChild(appendedInput);
    // submit CF7 form itself
    CF7Form.submit();
  } else {
    // Show spinner
    // const spinner = document.getElementsByClassName('cf7sSpinner')[0];
    // spinner.style.visibility = 'visible';

    // Object data to send during AJAX request
    var postData = applyFilters('cf7svisual.postData', {
      action: 'cf7skins_visual_update',
      // callback function
      form_id: form_id,
      visual: JSON.stringify(treeData),
      template: document.getElementById('cf7s-template').value,
      // since 0.6.2
      style: document.getElementById('cf7s-style').value // since 0.6.2
    });

    // If title has changed, window.cf7svisual.title is not the same as DOM #title value.
    // Add title property to postData for AJAX post
    // @since 0.6.3
    var title = document.getElementById('title').value;
    if (cf7svisual.title !== title) {
      postData.title = title;
    }

    // Save visual data into database using AJAX
    Object(_api__WEBPACK_IMPORTED_MODULE_6__["cf7sRequest"])(postData, 'json').then(function (data) {
      // object
      window.cf7sAdmin.getTextarea().value = data.form; // update active textarea

      window.onbeforeunload = null; // remove leave warning @since 0.6.5

      // Add Internet Explorer checks for no support of Event constructor @since 0.6.8
      var event;
      if (typeof Event === 'function') {
        event = new Event('change', {
          'bubbles': true
        });
      } else {
        event = document.createEvent('Event');
        event.initEvent('change', true, true);
      }

      // Trigger change to active textarea @since 0.6.5
      window.cf7sAdmin.getTextarea().dispatchEvent(new Event('change', {
        'bubbles': true
      }));

      // spinner.style.visibility = 'hidden'; // hide spinner

      // Show admin notification after successful Save
      // Follow WordPress HTML structure example for admin notice with dismissable click button
      // @link https://codex.wordpress.org/Plugin_API/Action_Reference/admin_notices
      // @link https://reactjs.org/docs/react-dom.html#unmountcomponentatnode
      // @since 0.5.2
      var adminNotice = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2___default.a.createElement("div", {
        className: "notice notice-success is-dismissible"
      }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2___default.a.createElement("p", null, "Visual saved!"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2___default.a.createElement("button", {
        type: "button",
        className: "notice-dismiss"
        /* Remove admin notification section on click dismiss */,
        onClick: function onClick() {
          return react_dom__WEBPACK_IMPORTED_MODULE_3___default.a.unmountComponentAtNode(document.getElementById('cf7s-visual-notice'));
        }
      }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2___default.a.createElement("span", {
        className: "screen-reader-text"
      }, __('Dismiss this notice.', 'contact-form-7-skins'))));

      // Render notice into <div id="cf7s-visual-notice"></div> DOM
      react_dom__WEBPACK_IMPORTED_MODULE_3___default.a.render(adminNotice, document.getElementById('cf7s-visual-notice'));
      setNotice(__('Visual saved!', 'contact-form-7-skins'), 'success');

      // Run addon callback JavaScript functions after save
      // Callbacks can contains Object or String i.e. [ {"MyNameSpace":"myFunction"}, "anotherFunction" ];
      // @since 0.6.4
      if (!!data.callbacks) {
        var _iterator2 = _createForOfIteratorHelper(data.callbacks),
          _step2;
        try {
          for (_iterator2.s(); !(_step2 = _iterator2.n()).done;) {
            var callback = _step2.value;
            if ("object" === _babel_runtime_helpers_typeof__WEBPACK_IMPORTED_MODULE_0___default()(callback)) {
              // object type, namespace function
              var namespace = Object.keys(callback)[0]; // namespace
              if (typeof window[namespace] === "undefined") {
                // namespace is not defined
                console.warn(sprintf(__('Namespace %s is undefined!', 'contact-form-7-skins'), namespace));
                continue; // continue to next loop
              }
              var func = callback[namespace]; // namespace function
              if (typeof window[namespace][func] !== "undefined") {
                // function is defined
                window[namespace][func].apply();
              } else {
                console.warn(sprintf(__('Function %1$s.%2$s is undefined!', 'contact-form-7-skins'), namespace, "".concat(func, "()")));
              }
            } else {
              // function type
              if (typeof window[callback] !== "undefined") {
                // function is defined
                window[callback].apply();
              } else {
                console.warn(sprintf(__('Function %s is undefined!', 'contact-form-7-skins'), "".concat(callback, "()")));
              }
            }
          }
        } catch (err) {
          _iterator2.e(err);
        } finally {
          _iterator2.f();
        }
      }
    })["catch"](function (err) {
      return console.error(err);
    });
  }
}

/**
 * Add tag name numbering using random number.
 * 
 * Adds random number to cf7sType e.g. text-777
 * Generates unique CF7 Form-tag Name when field is added to form
 * Can be used while dragging, clicking, duplicating (single or nested) item
 * 
 * @param  {Object}	node	tag << NEEDS BETTER DESCRIPTION
 * @return {Object}	node
 * 
 * @since 0.4.0
 */
function randomizeName(node) {
  var min = 100,
    max = 999;
  var rand = Math.floor(Math.random() * (max - min) + min);
  node.cf7Name = "".concat(node.cf7sType, "-").concat(rand);
  if (!!node.children) {
    // loop children
    var _iterator3 = _createForOfIteratorHelper(node.children),
      _step3;
    try {
      for (_iterator3.s(); !(_step3 = _iterator3.n()).done;) {
        var child = _step3.value;
        randomizeName(child);
      }
    } catch (err) {
      _iterator3.e(err);
    } finally {
      _iterator3.f();
    }
  }
  return node;
}

/**
 * Merge node with default properties from cf7sItems.js such as cf7sIcon, cf7sSelectGroup etc.
 * 
 * This can be used to merge with cf7sItems after selecting template
 * 
 * @param  {Array}	node	node item with minimal property
 * 
 * @return {Array}	merging nodes with full cf7sItems property
 * 
 * @since 0.4.0
 */
function mergeDefault(node) {
  var _loop = function _loop(i) {
    // plain object is not iterable using 'for ...of' loop
    var item = ___WEBPACK_IMPORTED_MODULE_5__["cf7sItems"].filter(function (obj) {
      // get default item properties
      return obj.cf7sType === node[i].cf7sType;
    })[0];
    node[i] = _objectSpread(_objectSpread({}, item), node[i]); // merge objects with spread operator

    if (!!node[i].children) {
      // loop children
      mergeDefault(node[i].children); // recursive
    }
  };
  for (var i in node) {
    _loop(i);
  }
  return node;
}

/**
 * Compares two software version numbers (e.g. "1.7.1" or "1.2b").
 *
 * This function was born in http://stackoverflow.com/a/6832721.
 *
 * @param {string} v1 The first version to be compared.
 * @param {string} v2 The second version to be compared.
 * @param {object} [options] Optional flags that affect comparison behavior:
 * lexicographical: (true/[false]) compares each part of the version strings lexicographically instead of naturally; 
 *                  this allows suffixes such as "b" or "dev" but will cause "1.10" to be considered smaller than "1.2".
 * zeroExtend: ([true]/false) changes the result if one version string has less parts than the other. In
 *             this case the shorter string will be padded with "zero" parts instead of being considered smaller.
 *
 * @return {number|NaN}
 * - true if the versions are equal
 * - false iff v1 < v2
 * - true iff v1 > v2
 * - NaN if either version string is in the wrong format
 */

function versionCompare(v1, v2, options) {
  var lexicographical = options && options.lexicographical || false,
    zeroExtend = options && options.zeroExtend || true,
    v1parts = (v1 || "0").split('.'),
    v2parts = (v2 || "0").split('.');
  function isValidPart(x) {
    return (lexicographical ? /^\d+[A-Za-zαß]*$/ : /^\d+[A-Za-zαß]?$/).test(x);
  }
  if (!v1parts.every(isValidPart) || !v2parts.every(isValidPart)) {
    return NaN;
  }
  if (zeroExtend) {
    while (v1parts.length < v2parts.length) v1parts.push("0");
    while (v2parts.length < v1parts.length) v2parts.push("0");
  }
  if (!lexicographical) {
    v1parts = v1parts.map(function (x) {
      var match = /[A-Za-zαß]/.exec(x);
      return Number(match ? x.replace(match[0], "." + x.charCodeAt(match.index)) : x);
    });
    v2parts = v2parts.map(function (x) {
      var match = /[A-Za-zαß]/.exec(x);
      return Number(match ? x.replace(match[0], "." + x.charCodeAt(match.index)) : x);
    });
  }
  for (var i = 0; i < v1parts.length; ++i) {
    if (v2parts.length === i) {
      return true;
    }
    if (v1parts[i] === v2parts[i]) {
      continue;
    } else if (v1parts[i] > v2parts[i]) {
      return true;
    } else {
      return false;
    }
  }
  if (v1parts.length !== v2parts.length) {
    return false;
  }
  return true;
}

/**
 * Get the value of defined key from window object cf7svisual.
 * 
 * Returned value can be the default value if it does not exist
 * 
 * @param  {String}		key		the object key of window.cf7svisual
 * 
 * @return {Array/String/Boolean} the value of defined key from window.cf7svisual variable
 * 
 * @since 2.3.0
 */
function getVisualVar(key) {
  var _window2 = window,
    cf7svisual = _window2.cf7svisual;

  // Return if cf7svisual is not set
  if (typeof cf7svisual === 'undefined' || cf7svisual === null) {
    console.log('window.cf7svisual does not exist!');
    return false;
  }

  // Return if key does not exist in cf7svisual
  if (!cf7svisual.hasOwnProperty(key)) {
    return false;
  }

  // Set default type for type checking
  var defaultKeyType = {
    addons: {},
    // object
    environment: '',
    // string
    options: {},
    // object		
    items: [],
    // array
    integration: {},
    // array
    title: '',
    // string
    treeData: [],
    // string, hold temporary treeData/items
    versions: {} // object
  };

  // Check it has valid type
  if (!!defaultKeyType.hasOwnProperty(key)) {
    // exists above
    if (_babel_runtime_helpers_typeof__WEBPACK_IMPORTED_MODULE_0___default()(cf7svisual[key]) === _babel_runtime_helpers_typeof__WEBPACK_IMPORTED_MODULE_0___default()(defaultKeyType[key])) {
      // same type			
      return cf7svisual[key];
    }
  }
  return false;
}

/**
 * Development mode
 * 
 * @return {Booean} true if development, false if production
 * 
 * @since 2.4.2
 */
function isDevelopment() {
  var _window3 = window,
    cf7svisual = _window3.cf7svisual; // window.cf7svisual

  return 'development' === cf7svisual.environment ? true : false;
}

/***/ }),

/***/ "./src/visual/util/index.js":
/*!**********************************!*\
  !*** ./src/visual/util/index.js ***!
  \**********************************/
/*! exports provided: cf7sItems, defaultTreeData, randomizeName, mergeDefault, versionCompare, saveVisualForm, getVisualVar, isDevelopment, cf7sRequest, cf7sDropRules, cf7sSurroundingRules, cf7sDuplicateRules */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _cf7sItems__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./cf7sItems */ "./src/visual/util/cf7sItems.js");
/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "cf7sItems", function() { return _cf7sItems__WEBPACK_IMPORTED_MODULE_0__["cf7sItems"]; });

/* harmony import */ var _api__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./api */ "./src/visual/util/api.js");
/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "cf7sRequest", function() { return _api__WEBPACK_IMPORTED_MODULE_1__["cf7sRequest"]; });

/* harmony import */ var _functions__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./functions */ "./src/visual/util/functions.js");
/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "defaultTreeData", function() { return _functions__WEBPACK_IMPORTED_MODULE_2__["defaultTreeData"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "randomizeName", function() { return _functions__WEBPACK_IMPORTED_MODULE_2__["randomizeName"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "mergeDefault", function() { return _functions__WEBPACK_IMPORTED_MODULE_2__["mergeDefault"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "versionCompare", function() { return _functions__WEBPACK_IMPORTED_MODULE_2__["versionCompare"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "saveVisualForm", function() { return _functions__WEBPACK_IMPORTED_MODULE_2__["saveVisualForm"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "getVisualVar", function() { return _functions__WEBPACK_IMPORTED_MODULE_2__["getVisualVar"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "isDevelopment", function() { return _functions__WEBPACK_IMPORTED_MODULE_2__["isDevelopment"]; });

/* harmony import */ var _cf7sRules__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./cf7sRules */ "./src/visual/util/cf7sRules.js");
/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "cf7sDropRules", function() { return _cf7sRules__WEBPACK_IMPORTED_MODULE_3__["cf7sDropRules"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "cf7sSurroundingRules", function() { return _cf7sRules__WEBPACK_IMPORTED_MODULE_3__["cf7sSurroundingRules"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "cf7sDuplicateRules", function() { return _cf7sRules__WEBPACK_IMPORTED_MODULE_3__["cf7sDuplicateRules"]; });






// Simplify import of utils


/***/ }),

/***/ "lodash":
/*!*************************!*\
  !*** external "lodash" ***!
  \*************************/
/*! no static exports found */
/***/ (function(module, exports) {

module.exports = lodash;

/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "React" ***!
  \************************/
/*! no static exports found */
/***/ (function(module, exports) {

module.exports = React;

/***/ }),

/***/ "react-dom":
/*!***************************!*\
  !*** external "ReactDOM" ***!
  \***************************/
/*! no static exports found */
/***/ (function(module, exports) {

module.exports = ReactDOM;

/***/ })

/******/ });
//# sourceMappingURL=cf7skins.js.map