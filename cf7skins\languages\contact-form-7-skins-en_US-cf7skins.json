{"domain": "messages", "locale_data": {"messages": {"": {"domain": "messages"}, "Acceptance (confirm)": [""], "Make this checkbox checked by default": [""], "Make this work inversely": [""], "Make this checkbox optional": [""], "Checkbox (option)": [""], "Put a label first, a checkbox last": [""], "Wrap each item with label element": [""], "Make checkboxes exclusive": [""], "Date": [""], "Default value": [""], "Use this text as the placeholder of the field": [""], "Range - min": [""], "Range - max": [""], "Email": [""], "Akismet - this field requires author's email address": [""], "File (upload)": [""], "File size limit (bytes)": [""], "Acceptable file types": [""], "Number": [""], "Quiz": [""], "Question 1": [""], "Answer 1": [""], "Question 2": [""], "Answer 2": [""], "Radio Button (option)": [""], "Option 1": [""], "Option 2": [""], "Select (dropdown)": [""], "Allow multiple selections": [""], "Insert a blank item as the first option": [""], "Submit": [""], "Telephone": [""], "Text (short text)": [""], "Textarea (long text)": [""], "URL (website link)": [""], "Fieldset (with legend)": [""], "Legend ..": [""], "List - ol": [""], "List Item - li": [""], "Paragraph - p": [""], "Only one reCAPTCHA per form allowed.": [""], "Only one submit for each form.": [""], "Root node can not be dragged to other node.": [""], "Only one %s allowed in a form.": [""], "Node has recaptcha children. Only one recaptcha allowed in a form.": [""], "Node has submit children. Only one submit allowed in a form.": [""], "Legend": [""], "Your Name (required)": [""], "Email Address (required)": [""], "Your Message": [""], "* Required": [""], "Send": [""], "Can not save! window.cf7svisual or post ID does not exist.": [""], "Dismiss this notice.": [""], "Visual saved!": [""], "Namespace %s is undefined!": [""], "Function %1$s.%2$s is undefined!": [""], "Function %s is undefined!": [""], "Save Visual": [""], "Field Type": [""], "Spinbox": [""], "Slider": [""], "Required": [""], "Name": [""], "Condition": [""], "Label": [""], "Default Value": [""], "This field requires author's name": [""], "This field requires author's email address": [""], "This field requires author's URL": [""], "Range": [""], "Min": [""], "Max": [""], "Content": [""], "Allowed tags:": [""], "Options": [""], "Default": [""], "Answer": [""], "Add Other as last option": [""], "Other": [""], "Size": [""], "Normal": [""], "Compact": [""], "Theme": [""], "Light": [""], "Dark": [""], "Id Attribute": [""], "Class Attribute": [""], "Save": [""], "Done": [""], "Drag/Move": [""], "Collapse": [""], "Expand": [""], "edit": [""], "duplicate": [""], "delete": [""], "Add-on options": [""], "Dismiss this notice": [""], "Forms are easier to follow along with when questions are on separate pages.": [""], "CF7 Skins Multi makes separate pages possible.": [""], "Your form is less likely to be completed if it’s too long. Separate it using": [""], "CF7 Skins Multi.": [""], "Progress bars make long forms feel less daunting. Add this in using ": [""], "Emphasize privacy policy acceptance by putting them on a second page.": [""], "CF7 Skins Multi": [""], " makes this easy to do.": [""], "Your form is more likely to be completed if it isn’t cluttered. Separate information onto different pages using": [""], "Put name and email address fields beside each other easily using CF7 Skins Ready.": [""], "Read more about CF7 Skins Ready.": [""], "Don’t forget that your form fields need to be mobile optimized, especially if fields are beside each other.": [""], "CF7 Skins Ready handles this for you.": [""], "Make your form easier to read by grouping together similar fields.": [""], "CF7 Skins Ready makes this easy to do.": [""], "Visually grouping similar fields together improves the flow of your form. A good way to do this is by drawing a box around the group using": [""], "CF7 Skins Ready.": [""], "Separate cluttered form content into 2 columns or more using": [""], "Looking to arrange your form fields into 2 or more columns?": [""], "CF7 Skins Ready ": [""], "makes this easy to do.": [""], "Increase the chances of your form being completed by aligning the fields. Use the grid structure in": [""], "CF7 Skins Ready": [""], "to get a head start.": [""], "More complex form layouts should follow a grid system.": [""], "You can align fields, put them side-by-side, and more with our easy-to-use": [""], "CF7 Skins Ready Add-on.": [""], "Have a yes/no radio button field? Display them horizontally on the same line using": [""], "Very good, good, neutral? For a survey form, put checkboxes or radio buttons on the same horizontal line.": [""], "It is more professional looking to have a form style that matches your site.": [""], "Find a better fit in our CF7 Skins Pro Styles.": [""], "Having more templates means you save time creating forms from scratch.": [""], "Get more templates to choose from.": [""], "For custom style options, you can use CSS to adjust the form’s style.": [""], "To get help, our premium email support team is available.": [""], "Ensure error-free forms by using templates and the visual editor.": [""], "Get more templates with the CF7 Skins Pro Add-on.": [""], "Show specific fields to customers based on their responses with": [""], "CF7 Skins Logic.": [""], "You may not need to show all of the possible fields to all of your customers.": [""], "Keep forms short & relevant with CF7 Skins Logic.": [""], "You can dynamically show and hide form fields using": [""], "Dynamic form fields are possibly when you add logic to the fields.": [""], "CF7 Skins Logic makes dynamic forms possible.": [""], "Feeling frustrated? Get 1-1 personalized, real-human responses to your tough questions with any of our": [""], "CF7 Skins Add-ons.": [""], "Make sure your form is set up correctly the first time by talking to our trained support staff, available with our": [""], "CF7 Skins Pro Add-on.": [""], "The WordPress support community is available to answer questions. Or, save time searching & ask us directly with our ": [""], "Premium Email support.": [""], "Double check your mail settings to avoid problems sending & receiving forms. Set it up correctly from the start with our": [""], "Tip": [""], "FIELDS (CF7 TAGS)": [""], "Drag and drop or click a Contact Form 7 Tag to add it to the visual form editor.": [""], "To see how Fields are used, select a template it's a great way to get started quickly.": [""], "Any field content can be changed by clicking Edit on the field.": [""], "FIELDS (CF7 SKINS ITEMS)": [""], "Use CF7 Skins Fields for the layout of your form.": [""], "Fieldsets, legends, and lists are used to group related fields, and allow for more detailed styling.": [""], "Form Options": [""], "Configure this forms options": [""], "Visual Data": [""], "Copy & paste visual form data": [""], "Error, the result is not an object or null.": [""], "Error: treeData, form or callbacks property is not available.": [""], "Error, templateTree is not an array!": [""], "Select a template that closely matches your needs then switch to the CF7 Skins Form  tab to add, duplicate or remove fields to match your requirements. Any field content can be changed by clicking Edit on the field.": [""], "Templates are pre-created forms that automatically create the form’s structure and content for you. Each template works as an easy to follow guide.": [""], "Save Form": [""], "Copy & paste visual data": [""], "Unable to add the item.": [""], "There is no edit field available for this item.": [""], "Unable to duplicate!": [""], "Unable to change the item.": [""], "Visual Object Data is not valid ( JSON parse error )": [""], "Visual data is empty.": [""], "Visual data is not valid.": [""]}}}