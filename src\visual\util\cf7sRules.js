/**
 * CF7 Skins Visual Rules.
 * 
 * Field/node event rules for RST
 *   - drop a node from Select or elsewhere in Form
 *   - click a node from Select
 *   - copy/duplicate within Form
 * 
 * Functions:
 *   cf7sAddonRules -
 *   cf7sDropRules - 
 *   cf7sSurroundingRules - 
 *   cf7sDuplicateRules -
 *   cf7sCountType - 
 *   cf7sHasChildren - 
 * 
 * @since 0.4.0
 */
import { cf7sItems } from './cf7sItems';
import { cloneDeep } from 'lodash'; // @since 0.6.1
import { 
	randomizeName, // @since 0.6.3
	getVisualVar // @since 2.3.0
} from './functions';

const { sprintf, __ } = { ...window.wp.i18n }; // @since 0.7.2
const { applyFilters, } = wp.hooks;

/**
 * Get list-ol default property from cf7sItems.
 * 
 * OL node from cf7sItems includes LI as default children
 * Need to clone cf7sItems to avoid modification by CF7 Skins rules.
 * 
 * @since 0.6.1
 */
const nodeOL = cloneDeep( cf7sItems ).filter( ( obj ) => {
	return obj.cf7sType === 'list-ol';
} )[0];

/**
 * Get list-li default property from cf7sItems.
 * 
 * Need to clone cf7sItems to avoid modification by CF7 Skins rules.
 * 
 * @since 0.6.1
 */
const nodeLI = cloneDeep( cf7sItems ).filter( ( obj ) => {
	return obj.cf7sType === 'list-li';
} )[0];

/**
 * Get addon rules for drop, surrounding, and duplicate.
 *
 * @param {String}		type		rule type, drop, surround, or duplicate
 *									See each rule below for each addon function parameter
 *
 * @return {Array} 	all registered addon rules for specific type
 * 
 * @since 0.7.2
 */
function cf7sAddonRules( type = 'drop' ) {
	let rules = {};
	
	/**
	 * Apply addons rules. Addon can register their own rule functions using object 
	 * with a key and function pairs. The function will have parameters as listed below.
	 * Function should return boolean value true to enable or false to disable drop, surround,
	 * or duplicate. For example:
	 * addFilter( 'cf7svisual.cf7sRules', 'cf7svisual', ( filter ) => {
	 *    filter.push( { drop: myDropFunc, surround: mySurroundFunc } );
	 *    return filter;
	 * });	 
	 * 
	 * @since 0.7.0
	 */
	const cf7sRules = applyFilters( 'cf7svisual.cf7sRules', [] );
	
	if ( !! cf7sRules ) { // only if not empty
		for ( let i = 0; i < cf7sRules.length; ++i ) { // loop for each registered addon
			
			for ( let type in cf7sRules[i] ) { // loop for each addon rule object
				
				if ( ! rules[type] ) { // set array if type rule does not exist
					rules[type] = [];
				}
				
				rules[type].push( cf7sRules[i][type] ); // add addon rule to array
			}
		}
	}

	
	if ( !! rules && !! rules[type] ) { // requested rule type
		return rules[type];
	}
}

/**
 * Drop rules for current node into other parent.
 * 
 * Return false to prevent node from dropping in the given location.
 * @link https://github.com/fritz-c/react-sortable-tree
 *
 * @params 	node			(object) 		current dropped node
 *	  		prevPath		(number/string)	previous path
 *	  		prevParent		(object)		previous parent object
 *	  		prevTreeIndex	(number) 		previous tree index
 *	  		nextPath		(number/string)	next path
 *	  		nextParent		(object)		next parent object
 *	  		nextTreeIndex	(number)		next tree index
 *	  		treeData		(object)		current tree data object
 * 
 * @return (Boolean)
 * 
 * @since 0.4.0
 */
export function cf7sDropRules( object ) {
	
	const {
		node,
		prevTreeIndex,
		nextParent,
	} = { ...object };
	
	// Read global treeData if is set by addons, or use object treeData
	// @since 0.6.8
	const treeData = getVisualVar('treeData') || object.treeData;

	/**
	 * Rule: CF7 Tag
	 * Only one CF7 tag inside list-li
	 * Check while moving tag into another LI parent, and has children children
	 */
	if ( nextParent && nextParent.cf7sType === 'list-li' && nextParent.children ) {
		
		// Loop all parent's children.
		// Not sure why nextParent.children contains dropped node candidate, even if it has not been dropped yet. Bug?
		// Count total CF7 tags inside parent.
		let countCF7Tags = 0;
		for ( let child of nextParent.children ) {
			for ( let tag of cf7sItems ) { // loop cf7sItems
				if ( child.cf7sType === tag.cf7sType && 'cf7Tag' === tag.cf7sSelectGroup ) {
					countCF7Tags++;
				}
			}
		}

		// Prevent if list-li already has CF7 tag.
		if ( countCF7Tags > 1 ) {
			console.warn( 'Only one CF7 tag for each list' );
			return false;
		}
	}
	
	/**
	 * Rule: reCAPTCHA 
	 * Prevent drop/click if CF7 reCAPTCHA integration is not activated 
	 * using window.cf7svisual.integration.reCAPTCHA (on hold)
	 * Only one reCAPTCHA tag per form
	 * New dragged reCAPTCHA does not have prevTreeIndex
	 * Disable duplication if node has recaptcha children
	 */
	if ( !! node && null == prevTreeIndex && 'recaptcha' === node.cf7sType ) {
		// Count and prevent if form already has a reCAPTCHA
		// 0 = no reCAPTCHA found in tree
		if ( cf7sCountType( node.cf7sType, treeData ) > 0 && null == prevTreeIndex ) {
			console.warn( __( 'Only one reCAPTCHA per form allowed.', 'contact-form-7-skins' ) );
			return false;
		}
	}

	/**
	 * Rule: only one submit per form
	 * For duplicate event, current node is empty
	 * New dragged submit has undefined prevTreeIndex
	 */
	if ( !! node && null == prevTreeIndex && 'submit' === node.cf7sType ) {
		
		// Count and prevent if form already has a submit tag
		// 0 = no submit found in tree
		if ( cf7sCountType( node.cf7sType, treeData ) > 0 ) {
			console.warn( __( 'Only one submit for each form.', 'contact-form-7-skins' ) );
			return false;
		}
	}
	
	/**
	 * Rule: disable drop for cf7sRoot to any node
	 * Return if is cf7sRoot and next parent target is not null
	 * @since 2.1.2
	 */
	if ( !! node.cf7sRoot && !! nextParent ) {
		console.warn( __( 'Root node can not be dragged to other node.', 'contact-form-7-skins' ) );
		return false;
	}
	
	// Apply addons rules @since 0.7.0
	const addonRules = cf7sAddonRules( 'drop' ); 
	
	if ( !! addonRules && !! addonRules.length ) { // requested rule type
		for ( let i = 0; i < addonRules.length; ++i ) { // loop addon rule
			if ( ! addonRules[i]( object ) )
				return false; // bail early, no need to call subsequent rules
		}
	}

	// Return nextParent if no rules applied to prevent drop
	// or return message
	return ! nextParent || ! nextParent.noChildren;
}

/**
 * OL/LI surrounding rules for tag, based on parent node.
 * 
 * @param {Object}	node			current dropped/clicked node
 * @param {Object}	parentNode		next parent node to move
 *
 * @return {Object} initial node or node with surrounding element
 * 
 * @since 0.4.0
 */
export function cf7sSurroundingRules( node, parentNode ) {

	// Rule: Add LI as default children of OL for new node
	// New node has no children, and no need to return.
	// nodeLI can be rewritten by other rules, LI should not contains any children, 
	// @since 0.6.1	
	if ( node.cf7sType === 'list-ol' && ! node.children ) {
		let LI = randomizeName( cloneDeep( nodeLI ) ); // clone to avoid have same name as others @since 0.6.3
		LI.children = []; // empty LI children
		node.children = [ { ...LI } ]; // add LI as OL children
		node.expanded = true; // make it expanded
	}
	
	// Rule: Add enclosed OL & LI to Fieldsets
	// @since 0.6.1
	if ( ! parentNode || parentNode.cf7sType === 'fieldset' ) {
		// Draged/clicked Fielset from SelectItem has no children. See cf7sItems for Fieldset.
		// Only for new Fieldset, not moving it from other parent in tree
		if ( ! node.children && 'fieldset' === node.cf7sType ) {
			// Clone to avoid have same name as others @since 0.6.3
			let OL = randomizeName( cloneDeep( nodeOL ) );
			let LI = randomizeName( cloneDeep( nodeLI ) );
				
			OL.children = [ { ...LI } ]; // add LI as OL children, LI as default OL children might be removed
			OL.expanded = true; // make it expanded
			node.children = [ { ...OL } ]; // add OL as Fieldset children
			node.expanded = true; // make it expanded
			return node;
		}
	}
	
	// Rule: CF7 Tag (if not reCAPTCHA or Submit) added into Form or Fieldset should be wrapped with OL > LI
	// @since 0.6.1

	if ( ! parentNode || parentNode.cf7sType === 'fieldset' || 
    	( false === parentNode.noChildren && [ 'list-ol', 'list-li' ].indexOf( parentNode.cf7sType ) < 0 ) ) {
		if ( 'cf7Tag' === node.cf7sSelectGroup ) {
			if ( [ 'recaptcha', 'submit' ].indexOf( node.cf7sType ) < 0 ) {	

				// Clone to avoid have same name as others @since 0.6.3
				let OL = randomizeName( cloneDeep( nodeOL ) );
				let LI = randomizeName( cloneDeep( nodeLI ) );
				
				LI.children = [ { ...node } ];
				LI.expanded = true; // make it expanded
				
				// Default list-ol contains list-li as a children, 
				// Overwrite dragged list-li node as a children to prevent lost
				OL.children = [ { ...LI } ];
				OL.expanded = true; // make it expanded
				
				return OL;
			}
		}
	}	

	// Rule: Add surrounding li to node inside an ol
	// If parent is a list-ol and selected node is not a list-li
	if ( ( parentNode && parentNode.cf7sType === 'list-ol' ) && node.cf7sType !== 'list-li' ) {
		
		let LI = randomizeName( cloneDeep( nodeLI ) ); // clone to avoid have same name as others @since 0.6.3
		LI.expanded = true; // expand after added
		LI.children = [ { ...node } ]; // add current selected node
		return LI;

	// Rule: Add surrounding ol for list-li if parent is not a list-ol
	// Only if current node type is a list-li ( with children if available ) and
	// without target parentNode or not a list-ol target parentNode.
	} else if ( ( node.cf7sType === 'list-li' && parentNode === null ) 
		|| ( node.cf7sType === 'list-li' && parentNode.cf7sType !== 'list-ol' ) ) {
		
		let OL = randomizeName( cloneDeep( nodeOL ) ); // clone to avoid have same name as others @since 0.6.3
		OL.children = [ { ...node } ]; // overwrite dragged list-li node as a children to prevent lost
		OL.expanded = true; // expand after added
		return OL;
	}
	
	// Apply addons rules @since 0.7.2
	const addonRules = cf7sAddonRules( 'surround' ); 
	
	if ( !! addonRules && !! addonRules.length ) { // requested rule type
		for ( let i = 0; i < addonRules.length; ++i ) { // loop addon rule
			node = addonRules[i]( { node, parentNode, nodeOL, nodeLI, randomizeName } ); // run addon function and return it's value
		}
	}

	return node;
}

/**
 * Duplicating rules.
 *
 * @param {Object}	rowInfo		current tree data
 *
 * @return {Boolean/String} true to enable duplicating or string message to disable
 * 
 * @since 0.6.3
 */
export function cf7sDuplicateRules( rowInfo ) {
	
	const { node, parentNode } = { ...rowInfo };
	
	// Rule: disable duplicate submit or recaptcha
	if ( 'recaptcha' === node.cf7sType || 'submit' === node.cf7sType ) {
		return sprintf( __( 'Only one %s allowed in a form.', 'contact-form-7-skins' ), node.cf7sType );
	}
	
	// Rule: disable if node contains children of submit or recaptcha
	if ( cf7sHasChildren( node, 'recaptcha' ) ) {
		return __( 'Node has recaptcha children. Only one recaptcha allowed in a form.', 'contact-form-7-skins' );
	}
	if ( cf7sHasChildren( node, 'submit' ) ) {
		return __( 'Node has submit children. Only one submit allowed in a form.', 'contact-form-7-skins' );
	}
		
	// Rule: only one CF7 tag inside list-li
	if ( !! parentNode && 'list-li' === parentNode.cf7sType && 'cf7Tag' === node.cf7sSelectGroup ) {
		return `surrounding`;
	}	

	// Apply addons rules @since 0.7.2
	const addonRules = cf7sAddonRules( 'duplicate' ); 
	
	if ( !! addonRules && !! addonRules.length ) { // requested rule type
		for ( let i = 0; i < addonRules.length; ++i ) { // loop addon rule
			addonRules[i]( rowInfo ); // run addon function and return it's value
		}
	}
	
	return true;
}

/**
 * Count nodes by specific type in tree.
 *
 * @param {String}	type		node type to check and count
 * @param {Object}	treeData	current tree data
 *
 * @return {TYPE} the number of found nodes
 * 
 * @since 0.6.1
 */
function cf7sCountType( type, treeData ) {
	let count = 0;
	const countNodes = ( nodes ) => {
		for ( let node of nodes ) {
			if ( type === node.cf7sType ) {
				count++;
			}

			if ( !! node.children ) { // check if have children
				countNodes( node.children ); // recursive loop for children
			}
		}
	}
	
	countNodes( treeData );
	
	return count;
}

/**
 * Check if given node has specific node type in children, grand-children.
 *
 * @param {Object}	node	current tree data
 * @param {String}	type	the children node type to find
 *
 * @return {Boolean}
 * 
 * @since 0.6.3
 */
function cf7sHasChildren( node, type ) {
	if ( ! node.children ) { // bail early if has no children
		return false;
	}

	for ( let child of node.children ) {
		if ( type === child.cf7sType ) { // child type exists, bail return true
			return true;
		}

		return cf7sHasChildren( child, type ); // recursive loop for children
	}
}
