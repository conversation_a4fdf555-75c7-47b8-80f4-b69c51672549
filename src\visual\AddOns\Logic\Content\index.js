const { find,  filter, flattenDeep } = lodash;
const { useContext, useState } = wp.element;
const { applyFilters, } = wp.hooks;
const { __ } = wp.i18n;
const {
	SelectControl,
	Button,
	Card,
	CardBody,
	Panel,
	PanelBody,
	PanelRow,
	PanelHeader,
	ToggleControl,
	Icon,
	Tooltip,
	DropdownMenu,
} = wp.components;
const {
    moreVertical,
    arrowUp,
    arrowDown,
    copy,
} = { ...Icon };

const { useDispatch, useSelect } = wp.data;
import { Droppable, Draggable } from 'react-beautiful-dnd';
import styled from 'styled-components';
import { LogicContext } from '../context';
import {
	getSTates,
	getComparators,
	getCriteriaTypeField,
	getCriteriaValue,
	getFieldTypeByName,
	getAllowedTagsComparator,
	getUserCriteriaOptions,
	getPostCriteriaOptions,
	convertComparator,
} from '../utils';
import CriteriaValue from '../CriteriaValue';
import {
	DragIcon,
} from '../icons';

import styles from './index.css';

const LogicPanelHeader = styled.div`
	position: relative;
	background-color: #d3e398;
	border: 1px solid #c3c4c7;
	display: flex;
	margin: -1px;
	align-items: center;
	padding: 0 6px;
	height: 51px;
`;
const LogicPanelRow = styled( PanelRow )`
	position: relative;
	border: ${props => props.focused === 'focused' ? '1px solid #2154ED' : '1px solid #c3c4c7'};
	background-color: #dcdcde;
	height: 51px;
	margin-top: 20px;
	padding: 0 6px;
`;
const LogicIconButton = styled( Button )`
	color: transparent;
	transition: all 0.3s ease;
	&:hover {
		color: #1e1e1e;
	}
`;
const LogicActions = styled.div`
	display: flex;
	align-items: center;
	margin-left: auto;
`;
const LogicRowContent = styled.div`
	display: flex;
	align-items: center;
	align-self: stretch;
	justify-content: ${ ( props ) =>
		props.centered ? 'center' : 'flex-start' };
	background-color: ${ ( props ) =>
		props.centered ? '#BCC986' : 'transparent' };
	padding: ${ ( props ) => ( props.centered ? '0px 10px' : '0' ) };
	& > * {
		margin-right: 9px;
	}
`;

const ContentArea = ( props ) => {
	const {
		fieldOptions, // holds available tag field for Fields section
	} = props;

	const { updateVisualState } = useDispatch( 'cf7svisual' );

	const setLogicData = ( data ) => updateVisualState({ logic: data });

	const { logicData, treeData } = useSelect( ( select ) => {
		const visualState = select( 'cf7svisual' ).getStates();
		
		return {
			logicData: visualState.logic,
			treeData: visualState.treeData,
		}
	});	

	const [ isRequesting, setIsRequesting ] = useState( false );
	const [ message, setMessage ] = useState( null );
	const [ opened, setOpened ] = useState( false );
	const [ isOpened, setIsOpened ] = useState( {} );
	const [ focused, setFocused] = useState(null)
	const { fields } = useContext(
		LogicContext
	);

	const getCustomFields = applyFilters( 'cf7slogic.select.fields', [] );	

	const customFields = [];
	getCustomFields.forEach( fields => {
		fields.items.forEach( item => {
			customFields.push( item );
		})
	})

	const mergedFields = fieldOptions.concat( customFields );

	const userOptions = getUserCriteriaOptions();
	const postOptions = getPostCriteriaOptions();
	const removeField = ( fieldName ) => {
		const logicDatakeys = Object.keys( logicData ).filter(
			( k ) => k !== fieldName
		);

		const newLogicData = logicDatakeys.reduce( ( obj, key ) => {
			obj[ key ] = logicData[ key ];
			return obj;
		}, {} );

		setLogicData( newLogicData );
	};

	const addStatement = ( fieldName ) => {
		const newStatements = {
			criteria: '',
			comparator: '=',
			value1: '',
			type: '',
		};

		setLogicData( {
			...logicData,
			[ fieldName ]: {
				...logicData[ fieldName ],
				statements: [
					...logicData[ fieldName ].statements,
					newStatements,
				],
			},
		} );
	};

	const duplicateStatement = ( fieldName, st, i ) => {
		setLogicData( {
			...logicData,
			[ fieldName ]: {
				...logicData[ fieldName ],
				statements: [ ...logicData[ fieldName ].statements, { ...st } ],
			},
		} );
	};

	const removeStatement = ( fieldName, st, i ) => {
		setLogicData( {
			...logicData,
			[ fieldName ]: {
				...logicData[ fieldName ],
				statements: logicData[ fieldName ].statements.filter(
					( el, index ) => i !== index
				),
			},
		} );
	};

	const toggleField = ( fieldName ) => {
		setIsOpened( {
			...isOpened,
			[ fieldName ]:
				typeof isOpened[ fieldName ] !== 'undefined'
					? ! isOpened[ fieldName ]
					: false,
		} );
	};

	const duplicateField = ( fieldName ) => {
		const availableFields = mergedFields.filter(
			( f ) => Object.keys( logicData ).indexOf( f.name ) === -1
		);

		if ( availableFields.length === 0 ) {
			return;
		}

		setLogicData( {
			...logicData,
			[ availableFields[ 0 ].name ]: {
				...logicData[ fieldName ],
			},
		} );
	};

	const changeFieldTag = ( val, fieldName ) => {

		const index = Object.keys( logicData ).findIndex(
			( f ) => f === fieldName
		);

		const updatedLogicData = Object.keys( logicData ).filter(
			( field ) => field !== fieldName
		);

		updatedLogicData.splice( index, 0, val );

		const newLogicData = updatedLogicData.reduce( ( obj, key ) => {

			if ( Object.keys( logicData ).indexOf( key ) === -1 ) {
				obj[ key ] = {
					...logicData[ fieldName ],
				};
			} else {
				obj[ key ] = logicData[ key ];
			}

			return obj;
		}, {} );

		setLogicData( newLogicData );
	};

	//criteria
	const selectCriteria = ( val, fieldName, i ) => {
		const typeField = getCriteriaTypeField( val );
		const criteriaValues = getCriteriaValue( val, fields, typeField );

		const defaultValue = () => {
			switch ( criteriaValues.type ) {
				case "checkbox":
					return criteriaValues.values.map( v => v.value );
					break;

				case 'radio':
				case 'file':
				case 'acceptance':
				case 'user_logged_in':
				case 'have_meta_key':
					return criteriaValues.values[ 0 ].value;
					break;

				case 'select':
					if ( criteriaValues.multiple ) {
						return [];
					} else {
						return criteriaValues.values[ 0 ].value
					}
					break;

				case 'user_role':
					return [];
					break;

				default:
					return criteriaValues.values;
			}
		};

		const newStatementData = logicData[ fieldName ].statements.map(
			( st, index ) => {
				if ( i === index ) {
					return {
						...st,
						criteria: val,
						comparator: Object.keys(
							getComparators( st.criteria, typeField, fields )
						)[ 0 ],
						value1: defaultValue(),
						type: typeField,
						cf7sType: typeField === 'field' && getFieldType( val ).toLowerCase(),
					};
				}
				return st;
			}
		);

		setLogicData( {
			...logicData,
			[ fieldName ]: {
				...logicData[ fieldName ],
				statements: newStatementData,
			},
		} );
	};

	const selectComparator = ( val, fieldName, i ) => {
		const newStatementData = logicData[ fieldName ].statements.map(
			( st, index ) => {
				if ( i === index ) {
					return {
						...st,
						comparator: val,
					};
				}
				return st;
			}
		);

		setLogicData( {
			...logicData,
			[ fieldName ]: {
				...logicData[ fieldName ],
				statements: newStatementData,
			},
		} );
	};

	const selectCriteriaAction = ( val, fieldName ) => {
		setLogicData( {
			...logicData,
			[ fieldName ]: {
				...logicData[ fieldName ],
				action: val ? 'show' : 'hide',
			},
		} );
	};

	const selectCriteriaIf = ( val, fieldName ) => {
		setLogicData( {
			...logicData,
			[ fieldName ]: { ...logicData[ fieldName ], if: val },
		} );
	};

	const handleSetFocused = (rowIndex, stIndex) => {
		setFocused(`${rowIndex}-${stIndex}`);
	}

	// Get base type from its name
	const getFieldType = ( fieldName ) => {
		let _treeData = window.cf7svisual.treeData || treeData;
		let fieldType = getFieldTypeByName( fieldName, _treeData );
		return !! fieldType && fieldType[0].toUpperCase() + fieldType.slice(1).toLowerCase();
	}

	const enableDuplicate = Object.keys( logicData ).length === mergedFields.length ? true : false;

	const fieldIsMissing = ( fieldName ) => ! mergedFields.find( f => f.name === fieldName ) ? true : false;

	const criteriaIsMissing = ( criteriaName ) => {		
		const users = userOptions.map( ( option ) =>  option.value  );	
		const posts = postOptions.map( ( option ) =>  option.value  );	

		if ( criteriaName ) { // this is not a new statement
			if ( users.indexOf( criteriaName ) < 0 // not in userOptions, user looged in, user role.
				&& posts.indexOf( criteriaName ) < 0 ) { // and not in postOptions, post ID, have meta key.
					// field exists in mergedFields or not
					return ! mergedFields.find( f => f.name === criteriaName ) ? true : false;
			}
		}
		
		return false;
	}

	return (
		<div className={ styles.Content }>
			<Card className={ styles.logicCard }>
				<CardBody>
					<Droppable droppableId="content">
						{ ( provided, snapshot ) => (
							<ul
								{ ...provided.droppableProps }
								ref={ provided.innerRef }
								style={ {
									backgroundColor: snapshot.isDraggingOver ? 'lightblue' : '#fff',
								} }
							>
								{ Object.keys( logicData ).map(
									( fieldName, i ) => (
										<Draggable
											key={ `content-${ fieldName }` }
											draggableId={ `content-${ fieldName }` }
											index={ i }
										>
											{ ( provided ) => (
												<li
													{ ...provided.draggableProps }
													ref={ provided.innerRef }
													style={ { marginBottom: '30px', ...provided.draggableProps.style } }
												>
													<Panel className={ styles.logicItemPanel }>
														<LogicPanelHeader>
															<div
																style={ {
																	position: 'absolute',
																	top: '-17px',
																	left: '20px',
																	border: '1px solid #BCC986',
																	backgroundColor: '#C4C4C4',
																	padding: '2px 8px',
																	fontWeight: 500,
																} }
															>
																{ __( 'Logic Item', 'cf7skins-logic' ) }

																{ !! getFieldType( fieldName ) && 
																	<span className={ styles.fieldType }>
																		{ getFieldType( fieldName ) }
																	</span>
																}
															</div>
															<LogicRowContent>
																<div
																	{ ...provided.dragHandleProps }
																	style={ {
																		marginTop: '6px',
																	} }
																>
																	<Tooltip
																		position="top center"
																		text={ __( 'Drag to move', 'cf7skins-logic' ) }
																	>
																		<div>
																			<DragIcon />
																		</div>
																	</Tooltip>
																</div>

																<SelectControl
																	value={ fieldName }
																	onChange={ ( val ) => changeFieldTag( val, fieldName ) }
																	className={ 
																		"noMarginBottom select-tag-header" + 
																		( !! fieldIsMissing( fieldName ) ? ` ${ styles.fieldIsMissing }` : "" ) 
																	}
																>
																	{ !! fieldIsMissing( fieldName ) &&
																		<option value="" title={ __( 'Field is missing, please set to other available field, or it will be deleted after save.', 'cf7skins-logic' ) }>
																			{ sprintf( __( 'Missing %s', 'cf7skins-logic' ), fieldName ) }
																		</option>
																	}

																	{ fieldOptions.length ? (
																		<optgroup label={ __( 'Field', 'cf7skins-logic' ) }>
																			{ fieldOptions
																				.filter( field => {
																					if ( field.name === fieldName ) {
																						return true;
																					}

																					return Object.keys(logicData).indexOf( field.name ) === -1;
																				}).map( field => (
																					<option value={ field.name }>
																						{ field.name }
																					</option>
																				))
																			}
																		</optgroup>
																	) : ( "" ) }

																	{ !! getCustomFields.length && getCustomFields.map( fields => {
																		return (
																			<optgroup label={ fields.group }>
																				{ fields.items
																					.filter( field => {
																						if ( field.name === fieldName ) {
																							return true;
																						}
																						return Object.keys( logicData ).indexOf( field.name ) === -1;
																					} )
																					.map( field => (
																						<option value={ field.name } >
																							{ field.name }
																						</option>
																					) )
																				}
																			</optgroup>
																		)
																	} ) }
																</SelectControl>
															</LogicRowContent>

															<LogicRowContent
																centered
																className={ styles.hideShowIfSection }
															>
																<span className={ styles.logicHideText } >
																	{ __( 'Hide', 'cf7skins-logic' ) }
																</span>

																<ToggleControl
																	label={ __( 'Show', 'cf7skins-logic' ) }
																	checked={ logicData[ fieldName ].action === 'show' }
																	onChange={ ( val ) => selectCriteriaAction( val, fieldName ) }
																	className={ styles.noMarginBottom }
																/>

																<SelectControl 
																	className={ styles.selectIf }
																	label={ __( 'if', 'cf7skins-logic' ) }
																	value={ logicData[ fieldName ].if }
																	labelPosition="side"
																	options={ [
																		{ label: __( 'Any', 'cf7skins-logic' ), value: 'any', },
																		{ label: __( 'All', 'cf7skins-logic' ), value: 'all', },
																	] }
																	onChange={ ( val ) => selectCriteriaIf( val, fieldName ) }
																></SelectControl>

																<Tooltip
																	position="top center"
																	text={ __( 'of the following statements are met', 'cf7skins-logic' ) } 
																>
																	<div><Icon className={ styles.IfToolTip } icon="editor-help" /></div>
																</Tooltip>

															</LogicRowContent>

															<div className={ styles.LogicActions }>

																<DropdownMenu
																	className={ styles.LogicControlsDropdown }
																	icon={ moreVertical }
																	label={ __( 'Select an action', 'cf7skins-logic' ) }
																	controls={ [
																		{
																			title: __( 'Add Logic Statement', 'cf7skins-logic' ),
																			icon: arrowUp,
																			onClick: () => addStatement( fieldName ),
																		},
																		{
																			title: __( 'Duplicate Logic Item', 'cf7skins-logic' ),
																			icon: copy,
																			onClick: () => duplicateField( fieldName ),
																		},
																		{
																			title: __( 'Delete Logic Item', 'cf7skins-logic' ),
																			icon: arrowDown,
																			onClick: () => removeField( fieldName ),
																		},
																	] }
																/>

																<div className={ styles.LogicControls }>
																	<button
																		data-balloon={ __( 'add statement', 'cf7skins-logic' ) }
																		data-balloon-pos="up"
																		onClick={ () => addStatement( fieldName ) }
																	>
																		<Icon icon="plus" className={ styles.addStatement }/>
																	</button>

																	<button
																		data-balloon={ __( 'duplicate logic', 'cf7skins-logic' ) }
																		data-balloon-pos="up"
																		disabled={ enableDuplicate }
																		onClick={ () => duplicateField( fieldName ) }
																	>
																		<Icon icon="admin-page" className={ ! enableDuplicate ? '' : styles.disabled } />
																	</button>

																	<button
																		data-balloon={ __( 'delete logic', 'cf7skins-logic' ) }
																		data-balloon-pos="up"
																		onClick={ () => removeField( fieldName ) }
																	>
																		<Icon icon="no" />
																	</button>
																</div>

																<button
																	data-balloon={ __( 'expand logic', 'cf7skins-logic' ) }
																	data-balloon-pos="up"
																	onClick={ () => toggleField( fieldName ) }
																>
																	<Icon icon="arrow-down-alt2" />
																</button>

															</div>
														</LogicPanelHeader>
														<PanelBody 
															opened={ typeof isOpened[ fieldName ] !== 'undefined' ? isOpened[ fieldName ] : true }
															className={ styles.logicStatementsPanel }
														>
															{ logicData[
																fieldName
															].statements.map(
																( st, stIndex ) => (
																	<LogicPanelRow focused={focused === `${i}-${stIndex}` ? 'focused' : ''}>
																		<div
																			style={ {
																				position: 'absolute',
																				top: '-15px',
																				left: '20px',
																				border: '1px solid #C3C4C7',
																				backgroundColor: '#D3E398',
																				padding: '2px 8px',
																				fontSize: '11px',
																			} }
																		>
																			{ __('Logic Statement', 'cf7skins-logic') }
																		</div>
																		<LogicRowContent>
																			<SelectControl
																				value={ st.criteria }
																				onChange={ ( val ) => selectCriteria( val, fieldName, stIndex ) }
																				onFocus={() => handleSetFocused(i, stIndex)}
																				className={ 
																					"select-criteria-tag" + 
																					( !! criteriaIsMissing( st.criteria ) ? ` ${ styles.fieldIsMissing }` : "" ) 
																				}																				
																			>
																				{ !! criteriaIsMissing( st.criteria ) &&
																					<option value="" title={ __( 'Field is missing, please set to other available field, or it will be deleted after save.', 'cf7skins-logic' ) }>
																						{ sprintf( __( 'Missing %s', 'cf7skins-logic' ), st.criteria ) }
																					</option>
																				}

																				{ ! criteriaIsMissing( st.criteria ) &&
																					<option value="">
																						{ __('Select criteria', 'cf7skins-logic') }
																					</option>
																				}

																				{ fieldOptions.length ? (
																					<optgroup label={ __('Field', 'cf7skins-logic') }>
																						{ fieldOptions .filter( ( field ) => {
																							if ( field.name === fieldName ) {
																								return false;
																							}
																							return true;
																						} ).map( ( field ) => (
																							<option value={ field.name } >
																								{ field.name }
																							</option>
																						) ) }
																					</optgroup>
																				) : (
																					''
																				) }
																				<optgroup label={ __('User', 'cf7skins-logic') }>
																					{ userOptions.map( ( field ) => (
																						<option value={ field.value } >
																							{ field.label }
																						</option>
																					) ) }
																				</optgroup>
																				<optgroup label={ __('Post', 'cf7skins-logic') }>
																					{ postOptions.map( ( field ) => (
																						<option value={ field.value } >
																							{ field.label }
																						</option>
																					) ) }
																				</optgroup>
																			</SelectControl>

																			{ ! criteriaIsMissing( st.criteria ) && st.criteria !== '' && getAllowedTagsComparator( st.criteria, fields ) && (
																				<SelectControl
																					value={ st.comparator === '' ? 
																						Object.keys( getComparators( st.criteria, st.type, fields ) )[ 0 ] :
																						convertComparator( st.comparator )
																					}
																					onChange={ ( val ) => selectComparator( val, fieldName, stIndex ) }
																					className="select-comparator"
																				>																	
																					{ Object.keys( getComparators( st.criteria, st.type, fields ) ).map( ( c ) => (
																							<option
																								value={ getComparators( st.criteria, st.type, fields )[ c ] }
																							>
																								{ c }
																							</option>
																						)
																					) }
																				</SelectControl>
																			) }

																			{ ! st.criteria !== '' && st.comparator !== 'changed' &&
																				<CriteriaValue
																					fieldName={ fieldName }
																					logicData={ logicData }
																					setLogicData={ setLogicData }
																					fields={ fields }
																					st={ st }
																					index={ stIndex }
																					className="statement-value-field"
																				/>
																			}
																		</LogicRowContent>

																		<LogicActions className={ styles.StatementAction }>
																			<button
																				data-balloon={ __( 'duplicate statement', 'cf7skins-logic' ) } 
																				data-balloon-pos="up"
																				onClick={ () => duplicateStatement( fieldName, st, stIndex ) }
																			>
																				<Icon icon="admin-page" />
																			</button>
																			
																			<button
																				data-balloon={ __( 'delete statement', 'cf7skins-logic' ) } 
																				data-balloon-pos="up"
																				onClick={ () => removeStatement( fieldName, st, stIndex ) }
																			>
																				<Icon icon="no" />
																			</button>
																			
																		</LogicActions>
																	</LogicPanelRow>
																)
															) }
														</PanelBody>
													</Panel>
												</li>
											) }
										</Draggable>
									)
								) }
								{ provided.placeholder }
							</ul>
						) }
					</Droppable>
				</CardBody>
			</Card>
		</div>
	);
};

export default ContentArea;
