/**
 * Build .po files and convert to json
 *
 * Console command: node translations.js
 *
 * https://stackoverflow.com/questions/20643470/execute-a-command-line-binary-with-node-js
 */

const fs = require('fs');
const execSync = require("child_process").execSync;
const po2json = require('po2json');
const chalk = require('chalk');

// Print messages
console.log( chalk.yellow( "\n\tPlease run build before building translations." ) );
console.log( chalk.green( "\n\tScanning from /js/ directory and generating .po files...\n" ) );

// Language object structure: 
// key: [ js source dir, lang destination dir, po filename, domain ].
// Default language is set to en_US.
const langObj = {
	'skins' : [ 'cf7skins/js/',			'contact-form-7-skins-en_US-cf7skins.po', 	'contact-form-7-skins' ],
	'pro'   : [ 'cf7skins-pro/js/',		'cf7skins-pro-en_US-visual-pro.po',			'cf7skins-pro'		   ],
	'ready' : [ 'cf7skins-ready/js/', 	'cf7skins-ready-en_US-visual-ready.po', 	'cf7skins-ready'       ],
	'multi' : [ 'cf7skins-multi/js/', 	'cf7skins-multi-en_US-visual-multi.po',		'cf7skins-multi'       ],
	'logic' : [ 'cf7skins-logic/js/', 	'cf7skins-logic-en_US-visual-logic.po',		'cf7skins-logic'       ],
};

const destDir = {
	'skins' : [ 'cf7skins/languages/', 		 'contact-form-7-skins/languages/', ],
	'pro'   : [ 'cf7skins-pro/languages/',	 'cf7-skins-pro/languages/',   		],
	'ready' : [ 'cf7skins-ready/languages/', 'cf7-skins-ready/languages/', 		],
	'multi' : [ 'cf7skins-multi/languages/', 'cf7-skins-multi/languages/', 		],
	'logic' : [ 'cf7skins-logic/languages/', 'cf7-skins-logic/languages/', 		],
};

const idx = !! process.argv.slice(2)[0] ? 1 : 0; // 0 for develop, 1 for build

// Build po files using wp i18n make-pot.
// https://developer.wordpress.org/cli/commands/i18n/make-pot/
// WP filename format: {$domain}-{$locale}-{$handle}.
// Use --debug option to ouput debug console.
// Use --skip-audit to hide warning about advanced translator comment.
for ( let key in langObj ) {

	if ( langObj.hasOwnProperty( key ) ) {

		// Example: wp i18n make-pot src/pro/js/ src/pro/languages/cf7skins-pro-en_US-visual-pro.po --domain=cf7skins-pro --skip-audit"	
		console.log( `\tScanning ${ langObj[key][0] }...` );

		// console.log( `\twp i18n make-pot ${langObj[key][0]} ${destDir[key][idx]}${langObj[key][1]} --domain=${langObj[key][2]} --skip-audit` );
		let makePot = execSync( `wp i18n make-pot ${langObj[key][0]} ${destDir[key][idx]}${langObj[key][1]} --domain=${langObj[key][2]} --skip-audit` );
		
		console.log( `\tGenerating ${destDir[key][idx]}${langObj[key][1]}` );
		console.log( '\t' + chalk.blue( makePot.toString() ) );
	}
}

// Print messages
console.log( chalk.green( "\tCreating .json files...\n" ) );

// Build .json based on each .po file found in scanned directories
for ( let key in langObj ) {

	if ( langObj.hasOwnProperty( key ) ) {

		let langPath = destDir[key][idx]; // get language path

		if ( fs.existsSync( langPath ) ) { // directory exists

			fs.readdir( langPath, function(err, files) { // files inside lang directory	

				for ( let j = 0; j < files.length; j++ ) { 

					let file = files[j].split('.'); // split by dot to get file name and type

					// Only .po file with 'visual' string handle in filename, see above
					if ( file[1] === 'po' && files[j].includes('visual') ) {

						var jsonData = '';

						try {
							jsonData = po2json.parseFileSync( langPath + files[j], { format: 'jed1.x' } ); // parse .po to Jed JSON

							fs.writeFileSync( langPath + file[0] + '.json', JSON.stringify( jsonData ), function( err ) { // write to .json file
								if ( err ) {
									return console.log( err );
								}
							} );

							// Print message about the generated json file
							let stat = fs.statSync( langPath + file[0] + '.json' );
							console.log( `\t${ file[0] }.json      \t`, stat.size / 1000 + ' kB' );
						} catch ( e ) {
							console.log( e );
						}
					}
				}

				// Shows message to run webpack if this is the last loop
				let lastLoop = Object.keys( langObj )[Object.keys( langObj ).length-1];
				if ( lastLoop === key ) { //  this is the last loop
					console.log( chalk.yellow( "\n\tPlease run build to distribute those files into respective repository." ) );
				}
			} );
		}
	}
}
