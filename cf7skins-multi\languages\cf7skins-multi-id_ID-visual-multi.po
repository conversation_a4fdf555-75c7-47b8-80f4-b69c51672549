msgid ""
msgstr ""
"Project-Id-Version: \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-07-06T17:44:29+02:00\n"
"PO-Revision-Date: 2024-07-08 23:36+0700\n"
"Last-Translator: \n"
"Language-Team: \n"
"Language: id\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=1; plural=0;\n"
"X-Generator: Poedit 3.4.4\n"
"X-Domain: cf7skins-multi\n"

#: multi.js:13660
msgid "Multi Tab"
msgstr "Tab Multi"

#: multi.js:13666
msgid "Tab 1"
msgstr "Tab 1"

#: multi.js:13778
msgid "Tab Title"
msgstr "Judul Tab"

#: multi.js:14225
msgid "New Tab %s"
msgstr "Tab Baru %s"

#: multi.js:14248
msgid "Cannot delete, at least one tab exists in form."
msgstr "Tidak dapat menghapus, paling tidak ada satu tab di formulir."

#: multi.js:14253
msgid ""
"Are you sure want to delete this tab and its content? \n"
"This action cannot be undone."
msgstr ""
"Apakah anda yakin ingin menghapus tab dan kontennya? \n"
"Aksi ini tidak dapat dikembalikan ke semula."

#: multi.js:14508
msgid "Multi Tabs"
msgstr "Tab Multi"

#: multi.js:14819
msgid "name is missing"
msgstr "name tidak ada"

#: multi.js:14821
msgid "value is missing"
msgstr "value tidak ada"

#: multi.js:14823
msgid "checked is missing"
msgstr "checked tidak ada"

#: multi.js:14825
msgid "type is missing"
msgstr "type tidak ada"

#: multi.js:14842
msgid "Multi Options"
msgstr "Opsi Multi"

#: multi.js:14850
msgid "Show navigation links"
msgstr "Tampilkan tautan navigasi"

#: multi.js:14856
msgid "Show progress bar"
msgstr "Tampilkan batang kemajuan"

#: multi.js:14862
msgid "Show pagination"
msgstr "Tampilkan penomoran"

#: multi.js:14866
msgid "Start Button"
msgstr "Tombol Mulai"

#: multi.js:14869
msgid "Start Button text here..."
msgstr "Teks Tombol Mulai disini..."

#: multi.js:14875
msgid "End Button"
msgstr "Tombol Selesai"

#: multi.js:14878
msgid "End Button text here..."
msgstr "Teks Tombol Akhir disini..."

#: multi.js:14886
msgid "Prev Button"
msgstr "Tombol Sebelumnya"

#: multi.js:14889
msgid "Prev Button text here..."
msgstr "Teks Tombol Sebelumnya disini..."

#: multi.js:14895
msgid "Next Button"
msgstr "Tombol Berikutnya"

#: multi.js:14898
msgid "Next Button text here..."
msgstr "Teks Tombol Berikutnya disini..."

#: multi.js:14904
msgid "Thank You Tab Text"
msgstr "Teks Tab Terima Kasih"

#: multi.js:14908
msgid "Thank you message here..."
msgstr "Ucapan terima kasih disini..."

#: multi.js:15021
msgid "Drag to move"
msgstr "Seret untuk menggeser"

#: multi.js:15029
msgid "Double-click to rename"
msgstr "Klik ganda untuk mengganti nama"

#: multi.js:15035
msgid "Remove tab and content"
msgstr "Hapus tab dan kontennya"

#: multi.js:15043
msgid "Add new tab"
msgstr "Tambah tab baru"

#~ msgid "Start Tab Text"
#~ msgstr "Kalimat Tab Awal"

#~ msgid "End Tab Text"
#~ msgstr "Kalimat Tab Akhir"

#~ msgid "Set CF7 Skins Multi options"
#~ msgstr "Atur opsi CF7 Skins Multi"

#~ msgid "End Tab Text..."
#~ msgstr "Teks tab akhir..."
