msgid ""
msgstr ""
"Project-Id-Version: \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-07-10T17:29:11+02:00\n"
"PO-Revision-Date: 2024-07-10 22:31+0700\n"
"Last-Translator: \n"
"Language-Team: \n"
"Language: id\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: Poedit 3.4.4\n"
"X-Domain: cf7skins-logic\n"

#: logic.js:24729
msgid "Logic Item"
msgstr "Item Logika"

#: logic.js:24737
msgid "Drag to move"
msgstr "Geser untuk memindahkan"

#: logic.js:24746 logic.js:24893
msgid ""
"Field is missing, please set to other available field, or it will be deleted "
"after save."
msgstr ""
"<PERSON><PERSON><PERSON> hilang, silahkan ganti ke ruas lain atau ruas tersebut akan dihapus "
"setelah disimpan."

#: logic.js:24747 logic.js:24894
msgid "Missing %s"
msgstr "%s hilang"

#: logic.js:24748 logic.js:24897
msgid "Field"
msgstr "Ruas"

#: logic.js:24776
msgid "Hide"
msgstr "Sembunyikan"

#: logic.js:24777
msgid "Show"
msgstr "Tampilkan"

#: logic.js:24785
msgid "if"
msgstr "jika"

#: logic.js:24789
msgid "Any"
msgstr "Salah satu"

#: logic.js:24792
msgid "All"
msgstr "Semua"

#: logic.js:24800
msgid "of the following statements are met"
msgstr "pernyataan berikut benar"

#: logic.js:24809
msgid "Select an action"
msgstr "Pilih aksi"

#: logic.js:24811
msgid "Add Logic Statement"
msgstr "Tambah Pernyataan Logika"

#: logic.js:24817
msgid "Duplicate Logic Item"
msgstr "Gandakan Item Logika"

#: logic.js:24823
msgid "Delete Logic Item"
msgstr "Hapus Item Logika"

#: logic.js:24832
msgid "add statement"
msgstr "tambah pernyataan"

#: logic.js:24841
msgid "duplicate logic"
msgstr "gandakan logika"

#: logic.js:24851
msgid "delete logic"
msgstr "hapus logika"

#: logic.js:24859
msgid "expand logic"
msgstr "kembangkan logika"

#: logic.js:24882
msgid "Logic Statement"
msgstr "Penyataan Logika"

#: logic.js:24896
msgid "Select criteria"
msgstr "Pilih kriteria"

#: logic.js:24908
msgid "User"
msgstr "Pengguna"

#: logic.js:24914
msgid "Post"
msgstr "Pos"

#: logic.js:24940
msgid "duplicate statement"
msgstr "gandakan pernyataan"

#: logic.js:24948
msgid "delete statement"
msgstr "hapus pernyataan"

#: logic.js:25116 logic.js:25204
msgid "Select..."
msgstr "Pilih..."

#: logic.js:25416
msgid "Fields"
msgstr "Ruas"

#: logic.js:25418
msgid "Drag and drop or click this field to add to the Logic editor."
msgstr "Geser dan letakkan atau ketuk ruas untuk menambahkan ke editor Logika."

#: logic.js:25811
msgid ""
"Field %s is no longer existed in treeData, please create a new one or select "
"another field."
msgstr ""
"Ruas %s tidak ada di treeData, silahkan buat yang baru atau pilih ruas yang "
"lain."

#: logic.js:26012 logic.js:26023
msgid "contains"
msgstr "mengandung"

#: logic.js:26039
msgid "changed"
msgstr "diubah"

#: logic.js:26040
msgid "contains text"
msgstr "mengandung teks"

#: logic.js:26055
msgid "user logged in"
msgstr "pengguna masuk"

#: logic.js:26058
msgid "user role"
msgstr "peran pengguna"

#: logic.js:26064
msgid "post ID"
msgstr "ID pos"

#: logic.js:26067
msgid "have meta key"
msgstr "memiliki kunci meta"

#: logic.js:26149
msgid "unchecked"
msgstr "tidak dicentang"

#: logic.js:26152
msgid "checked"
msgstr "dicentang"

#: logic.js:26160
msgid "is empty"
msgstr "kosong"

#: logic.js:26163
msgid "is not empty"
msgstr "tidak kosong"
