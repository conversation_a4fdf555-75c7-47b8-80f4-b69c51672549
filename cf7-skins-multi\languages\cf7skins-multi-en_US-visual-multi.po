msgid ""
msgstr ""
"Project-Id-Version: \n"
"Report-Msgid-Bugs-To: \n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"POT-Creation-Date: 2024-07-10T17:33:39+02:00\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"X-Generator: WP-CLI 2.10.0\n"
"X-Domain: cf7skins-multi\n"

#: multi.js:13660
msgid "Multi Tab"
msgstr ""

#: multi.js:13666
msgid "Tab 1"
msgstr ""

#: multi.js:13778
msgid "Tab Title"
msgstr ""

#: multi.js:14225
msgid "New Tab %s"
msgstr ""

#: multi.js:14248
msgid "Cannot delete, at least one tab exists in form."
msgstr ""

#: multi.js:14253
msgid ""
"Are you sure want to delete this tab and its content? \n"
"This action cannot be undone."
msgstr ""

#: multi.js:14508
msgid "Multi Tabs"
msgstr ""

#: multi.js:14819
msgid "name is missing"
msgstr ""

#: multi.js:14821
msgid "value is missing"
msgstr ""

#: multi.js:14823
msgid "checked is missing"
msgstr ""

#: multi.js:14825
msgid "type is missing"
msgstr ""

#: multi.js:14842
msgid "Multi Options"
msgstr ""

#: multi.js:14850
msgid "Show navigation links"
msgstr ""

#: multi.js:14856
msgid "Show progress bar"
msgstr ""

#: multi.js:14862
msgid "Show pagination"
msgstr ""

#: multi.js:14866
msgid "Start Button"
msgstr ""

#: multi.js:14869
msgid "Start Button text here..."
msgstr ""

#: multi.js:14875
msgid "End Button"
msgstr ""

#: multi.js:14878
msgid "End Button text here..."
msgstr ""

#: multi.js:14886
msgid "Prev Button"
msgstr ""

#: multi.js:14889
msgid "Prev Button text here..."
msgstr ""

#: multi.js:14895
msgid "Next Button"
msgstr ""

#: multi.js:14898
msgid "Next Button text here..."
msgstr ""

#: multi.js:14904
msgid "Thank You Tab Text"
msgstr ""

#: multi.js:14908
msgid "Thank you message here..."
msgstr ""

#: multi.js:15021
msgid "Drag to move"
msgstr ""

#: multi.js:15029
msgid "Double-click to rename"
msgstr ""

#: multi.js:15035
msgid "Remove tab and content"
msgstr ""

#: multi.js:15043
msgid "Add new tab"
msgstr ""
