{"version": 3, "file": "cf7skins.js", "sources": ["webpack:///webpack/bootstrap", "webpack:///./node_modules/@babel/runtime/helpers/defineProperty.js", "webpack:///./node_modules/@babel/runtime/helpers/toPrimitive.js", "webpack:///./node_modules/@babel/runtime/helpers/toPropertyKey.js", "webpack:///./node_modules/@babel/runtime/helpers/typeof.js", "webpack:///./node_modules/balloon-css/balloon.min.css", "webpack:///./src/cf7skins/index.js", "webpack:///./src/visual/util/api.js", "webpack:///./src/visual/util/cf7sItems.js", "webpack:///./src/visual/util/cf7sRules.js", "webpack:///./src/visual/util/functions.js", "webpack:///./src/visual/util/index.js", "webpack:///external \"lodash\"", "webpack:///external \"React\"", "webpack:///external \"ReactDOM\""], "sourcesContent": [" \t// The module cache\n \tvar installedModules = {};\n\n \t// The require function\n \tfunction __webpack_require__(moduleId) {\n\n \t\t// Check if module is in cache\n \t\tif(installedModules[moduleId]) {\n \t\t\treturn installedModules[moduleId].exports;\n \t\t}\n \t\t// Create a new module (and put it into the cache)\n \t\tvar module = installedModules[moduleId] = {\n \t\t\ti: moduleId,\n \t\t\tl: false,\n \t\t\texports: {}\n \t\t};\n\n \t\t// Execute the module function\n \t\tmodules[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n\n \t\t// Flag the module as loaded\n \t\tmodule.l = true;\n\n \t\t// Return the exports of the module\n \t\treturn module.exports;\n \t}\n\n\n \t// expose the modules object (__webpack_modules__)\n \t__webpack_require__.m = modules;\n\n \t// expose the module cache\n \t__webpack_require__.c = installedModules;\n\n \t// define getter function for harmony exports\n \t__webpack_require__.d = function(exports, name, getter) {\n \t\tif(!__webpack_require__.o(exports, name)) {\n \t\t\tObject.defineProperty(exports, name, { enumerable: true, get: getter });\n \t\t}\n \t};\n\n \t// define __esModule on exports\n \t__webpack_require__.r = function(exports) {\n \t\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n \t\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n \t\t}\n \t\tObject.defineProperty(exports, '__esModule', { value: true });\n \t};\n\n \t// create a fake namespace object\n \t// mode & 1: value is a module id, require it\n \t// mode & 2: merge all properties of value into the ns\n \t// mode & 4: return value when already ns object\n \t// mode & 8|1: behave like require\n \t__webpack_require__.t = function(value, mode) {\n \t\tif(mode & 1) value = __webpack_require__(value);\n \t\tif(mode & 8) return value;\n \t\tif((mode & 4) && typeof value === 'object' && value && value.__esModule) return value;\n \t\tvar ns = Object.create(null);\n \t\t__webpack_require__.r(ns);\n \t\tObject.defineProperty(ns, 'default', { enumerable: true, value: value });\n \t\tif(mode & 2 && typeof value != 'string') for(var key in value) __webpack_require__.d(ns, key, function(key) { return value[key]; }.bind(null, key));\n \t\treturn ns;\n \t};\n\n \t// getDefaultExport function for compatibility with non-harmony modules\n \t__webpack_require__.n = function(module) {\n \t\tvar getter = module && module.__esModule ?\n \t\t\tfunction getDefault() { return module['default']; } :\n \t\t\tfunction getModuleExports() { return module; };\n \t\t__webpack_require__.d(getter, 'a', getter);\n \t\treturn getter;\n \t};\n\n \t// Object.prototype.hasOwnProperty.call\n \t__webpack_require__.o = function(object, property) { return Object.prototype.hasOwnProperty.call(object, property); };\n\n \t// __webpack_public_path__\n \t__webpack_require__.p = \"\";\n\n\n \t// Load entry module and return exports\n \treturn __webpack_require__(__webpack_require__.s = \"./src/cf7skins/index.js\");\n", "var toPropertyKey = require(\"./toPropertyKey.js\");\nfunction _defineProperty(e, r, t) {\n  return (r = toPropertyKey(r)) in e ? Object.defineProperty(e, r, {\n    value: t,\n    enumerable: !0,\n    configurable: !0,\n    writable: !0\n  }) : e[r] = t, e;\n}\nmodule.exports = _defineProperty, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "var _typeof = require(\"./typeof.js\")[\"default\"];\nfunction toPrimitive(t, r) {\n  if (\"object\" != _typeof(t) || !t) return t;\n  var e = t[Symbol.toPrimitive];\n  if (void 0 !== e) {\n    var i = e.call(t, r || \"default\");\n    if (\"object\" != _typeof(i)) return i;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (\"string\" === r ? String : Number)(t);\n}\nmodule.exports = toPrimitive, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "var _typeof = require(\"./typeof.js\")[\"default\"];\nvar toPrimitive = require(\"./toPrimitive.js\");\nfunction toPropertyKey(t) {\n  var i = toPrimitive(t, \"string\");\n  return \"symbol\" == _typeof(i) ? i : i + \"\";\n}\nmodule.exports = toPropertyKey, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "function _typeof(o) {\n  \"@babel/helpers - typeof\";\n\n  return (module.exports = _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) {\n    return typeof o;\n  } : function (o) {\n    return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o;\n  }, module.exports.__esModule = true, module.exports[\"default\"] = module.exports), _typeof(o);\n}\nmodule.exports = _typeof, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "// extracted by mini-css-extract-plugin\nexport default {\"font-awesome\":\"font-awesome___0v5p5\"};", "import 'balloon-css';\r\n\r\nconst util = require('../../src/visual/util/index.js');\r\n\r\n// Follows webpack approach to expose global using library and libraryTarget\r\nwindow[\"cf7svisual\"] = window[\"cf7svisual\"] || {}; \r\nwindow[\"cf7svisual\"][\"util\"] = util;\r\n\r\n// Get all tooltip/help elements\r\nconst elements = document.getElementsByClassName( 'balloon' );\r\n\r\nfor ( let i = 0; i < elements.length; i++ ) {\r\n\r\n\t// Get initial tooltip text\r\n\tlet title = elements[i].getAttribute( 'title' );\r\n\t\r\n\tlet element = elements[ i ];\r\n\r\n\t// Empty/remove title attribute to disable default browser tooltip\r\n\telement.setAttribute( 'title', '' );\r\n\r\n\t// Set balloon-css attributes\r\n\telement.setAttribute( 'data-balloon', title );\t\r\n\r\n\tif ( ! element.hasAttribute( 'data-balloon-pos' ) ) {\t\t\r\n\t\telement.setAttribute( 'data-balloon-pos', 'right' );\r\n\t}\r\n\r\n\tif ( ! element.hasAttribute( 'data-balloon-length' ) ) {\t\t\r\n\t\telement.setAttribute( 'data-balloon-length', 'large' );\t\r\n\t}\r\n}\r\n", "/**\r\n * CF7 Skins Visual API\r\n * \r\n * API util for handling various AJAX requests to the PHP backend\r\n *\r\n */\r\n \r\nimport { defaultTreeData } from './functions';\r\n\r\n/**\r\n * AJAX post request function.\r\n * \r\n * Replace previous functions visualSelectTemplate()and visualFormPOST()\r\n *\r\n * @link https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise (check supports for IE/Edge), replaced by 'whatwg-fetch'\r\n * @link https://developer.mozilla.org/en-US/docs/Web/API/Fetch_API (check supports for IE/Edge)\r\n * \r\n * @param\t{object}\tpostData\t\tAJAX post data\r\n * @param\t{string}\tdataType\t\treturned data type\r\n * \r\n * @returns (void)\r\n * \r\n * @since 0.5.0\r\n */\r\nfunction cf7sRequest( postData, dataType = 'text' ) {\r\n\t\r\n\t// Allow addons to use their own nonce for security purpose during AJAX,\r\n\t// set to cf7svisual nonce for default @since 0.6.8\r\n\tif ( ! postData.nonce ) {\r\n\t\tpostData.nonce = window.cf7svisual.nonce; // add nonce for WordPress security check\r\n\t}\r\n\r\n\tconst postBody = Object.keys( postData ).map( function( k ) {\r\n\t\treturn encodeURIComponent( k ) + \"=\" + encodeURIComponent( postData[ k ] );\r\n\t}).join( '&' );\r\n\t\r\n\tconst postOptions = { \r\n\t\tmethod: 'POST',\r\n\t\tcredentials: 'same-origin',\r\n\t\theaders: { \r\n\t\t  'Content-Type': `application/x-www-form-urlencoded; charset=UTF-8`        \r\n\t\t},\r\n\t\tbody: postBody\r\n\t};\r\n\t\r\n\t// Start fetch for AJAX request\r\n\tlet savedData = fetch( window.cf7svisual.ajaxurl, postOptions )\r\n\t\t.then( function( response ) {\r\n\t\t\treturn dataType === 'json' ? response.json() : response.text();\r\n\t\t} )\r\n\t\t.then( function( data ) {\r\n\t\t\tif ( data ) {\r\n\t\t\t\treturn data;\r\n\t\t\t} else {\r\n\t\t\t\tthrow new Error( `Error. Returned data: ${data}` );\r\n\t\t\t}\r\n\t\t} )\r\n\t\t.catch ( error => { \r\n\t\t\tconsole.error( `Error: ${ error }` );\r\n\t\t\treturn;\r\n\t\t} );\r\n\r\n\treturn Promise.resolve( savedData );\r\n}\r\n\r\nexport { defaultTreeData, cf7sRequest };", "import { getVisualVar } from './functions'; // @since 2.3.0\r\n\r\nconst { __ } = { ...window.wp.i18n }; // @since 0.7.2\r\nconst { applyFilters, } = wp.hooks;\r\n\r\n\r\n/**\r\n * Define all CF7 Tags & CF7 Skins Items.\r\n * \r\n * @since 0.4.0\r\n */\r\nconst cf7sItemsDefault = [\r\n\r\n/** CF7 Tags **/\r\n\t{\r\n\t\tcf7sType: 'acceptance',\r\n\t\tcf7sSelectLabel: __( 'Acceptance (confirm)', 'contact-form-7-skins' ),\r\n\t\tcf7sSelectGroup: 'cf7Tag',\r\n\t\tcf7Name: 'acceptance',\r\n\t\tcf7sLabel: '',\r\n\t\tcf7Required: false,\r\n\t\tcf7TagOptions: [\r\n\t\t\t{\r\n\t\t\t\tcf7Option: 'default_on',\r\n\t\t\t\tisChecked: false,\r\n\t\t\t\toptionLabel: __( 'Make this checkbox checked by default', 'contact-form-7-skins' ),\r\n\t\t\t\toptionType: 'checkbox',\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\tcf7Option: 'invert',\r\n\t\t\t\tisChecked: false,\r\n\t\t\t\toptionLabel: __( 'Make this work inversely', 'contact-form-7-skins' ),\r\n\t\t\t\toptionType: 'checkbox',\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\tcf7Option: 'optional',\r\n\t\t\t\tisChecked: true,\r\n\t\t\t\toptionLabel: __( 'Make this checkbox optional', 'contact-form-7-skins' ),\r\n\t\t\t\toptionType: 'checkbox',\r\n\t\t\t},\r\n\t\t],\r\n\t\tcf7DefaultOn: false,\r\n\t\tcf7Invert: false,\r\n\t\tcf7Optional: true,\r\n\t\tcf7Content: '', // for condition field, not the same as paragraph cf7sContent @since 0.6.0\r\n\t\tcf7IdAttribute: '',\r\n\t\tcf7ClassAttribute: '',\r\n\t\tcf7sIcon: 'yes-alt',\r\n\t\tnoChildren: true,\r\n\t},\r\n\t{\r\n\t\tcf7sType: 'checkbox',\r\n\t\tcf7sSelectLabel: __( 'Checkbox (option)', 'contact-form-7-skins' ),\r\n\t\tcf7sSelectGroup: 'cf7Tag',\r\n\t\tcf7Name: 'checkbox',\r\n\t\tcf7sLabel: '',\r\n\t\tcf7Required: false,\r\n\t\tcf7TagOptions: [\r\n\t\t\t{\r\n\t\t\t\tcf7Option: 'label_first',\r\n\t\t\t\tisChecked: false,\r\n\t\t\t\toptionLabel: __( 'Put a label first, a checkbox last', 'contact-form-7-skins' ),\r\n\t\t\t\toptionType: 'checkbox',\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\tcf7Option: 'use_label_element',\r\n\t\t\t\tisChecked: false,\r\n\t\t\t\toptionLabel: __( 'Wrap each item with label element', 'contact-form-7-skins' ),\r\n\t\t\t\toptionType: 'checkbox',\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\tcf7Option: 'exclusive',\r\n\t\t\t\tisChecked: false,\r\n\t\t\t\toptionLabel: __( 'Make checkboxes exclusive', 'contact-form-7-skins' ),\r\n\t\t\t\toptionType: 'checkbox',\r\n\t\t\t},\r\n\t\t],\r\n\t\tcf7Options: [ // Option Checked +\r\n\t\t\t{\r\n\t\t\t\tvalue: 'Option 1',\r\n\t\t\t\tisChecked: true,\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\tvalue: 'Option 2',\r\n\t\t\t\tisChecked: false,\r\n\t\t\t},\r\n\t\t],\r\n\t\tcf7LabelFirst: false,\r\n\t\tcf7UseLabelElement: false,\r\n\t\tcf7Exclusive: false,\r\n\t\tcf7IdAttribute: '',\r\n\t\tcf7ClassAttribute: '',\r\n\t\tcf7sIcon: 'forms',\r\n\t\tnoChildren: true,\r\n\t},\r\n\t{\r\n\t\tcf7sType: 'date',\r\n\t\tcf7sSelectLabel: __( 'Date', 'contact-form-7-skins' ),\r\n\t\tcf7sSelectGroup: 'cf7Tag',\r\n\t\tcf7Name: 'date',\r\n\t\tcf7sLabel: '',\r\n\t\tcf7Required: false,\r\n\t\tcf7TagOptions: [\r\n\t\t\t{\r\n\t\t\t\tcf7Option: '',\r\n\t\t\t\toptionLabel: __( 'Default value', 'contact-form-7-skins' ),\r\n\t\t\t\toptionType: 'input',\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\tcf7Option: 'placeholder',\r\n\t\t\t\tisChecked: false,\r\n\t\t\t\toptionLabel: __( 'Use this text as the placeholder of the field', 'contact-form-7-skins' ),\r\n\t\t\t\toptionType: 'checkbox',\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\tcf7Option: '',\r\n\t\t\t\toptionLabel: __( 'Range - min', 'contact-form-7-skins' ),\r\n\t\t\t\toptionType: 'input',\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\tcf7Option: '',\r\n\t\t\t\toptionLabel: __( 'Range - max', 'contact-form-7-skins' ),\r\n\t\t\t\toptionType: 'input',\r\n\t\t\t},\r\n\t\t],\r\n\t\tcf7Values: '',\r\n\t\tcf7Placeholder: false,\r\n\t\tcf7Min: '',\r\n\t\tcf7Max: '',\r\n\t\tcf7IdAttribute: '',\r\n\t\tcf7ClassAttribute: '',\r\n\t\tcf7sIcon: 'calendar',\r\n\t\tnoChildren: true,\r\n\t},\r\n\t{\r\n\t\tcf7sType: 'email',\r\n\t\tcf7sSelectLabel: __( 'Email', 'contact-form-7-skins' ),\r\n\t\tcf7sSelectGroup: 'cf7Tag',\r\n\t\tcf7Name: 'email',\r\n\t\tcf7sLabel: '',\r\n\t\tcf7Required: false,\r\n\t\tcf7TagOptions: [\r\n\t\t\t{\r\n\t\t\t\tcf7Option: '',\r\n\t\t\t\toptionLabel: 'Default value',\r\n\t\t\t\toptionType: 'input',\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\tcf7Option: 'placeholder',\r\n\t\t\t\tisChecked: false,\r\n\t\t\t\toptionLabel: __( 'Use this text as the placeholder of the field', 'contact-form-7-skins' ),\r\n\t\t\t\toptionType: 'checkbox',\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\tcf7Option: 'akismet_author_email',\r\n\t\t\t\tisChecked: false,\r\n\t\t\t\toptionLabel: __( \"Akismet - this field requires author's email address\", 'contact-form-7-skins' ),\r\n\t\t\t\toptionType: 'checkbox',\r\n\t\t\t},\r\n\t\t],\r\n\t\tcf7Values: '',\r\n\t\tcf7Placeholder: false,\r\n\t\tcf7AkismetAuthorEmail: false,\r\n\t\tcf7IdAttribute: '',\r\n\t\tcf7ClassAttribute: '',\r\n\t\tcf7sIcon: 'email-alt',\r\n\t\tnoChildren: true,\r\n\t},\r\n\t{\r\n\t\tcf7sType: 'file',\r\n\t\tcf7sSelectLabel: __( 'File (upload)', 'contact-form-7-skins' ),\r\n\t\tcf7sSelectGroup: 'cf7Tag',\r\n\t\tcf7Name: 'file',\r\n\t\tcf7sLabel: '',\r\n\t\tcf7Required: false,\r\n\t\tcf7TagOptions: [\r\n\t\t\t{\r\n\t\t\t\tcf7Option: 'limit',\r\n\t\t\t\toptionLabel: __( 'File size limit (bytes)', 'contact-form-7-skins' ),\r\n\t\t\t\toptionType: 'input',\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\tcf7Option: '',\r\n\t\t\t\toptionLabel: __( 'Acceptable file types', 'contact-form-7-skins' ),\r\n\t\t\t\toptionType: 'select',\r\n\t\t\t},\r\n\t\t],\r\n\t\tcf7Limit: '',\r\n\t\tcf7FileTypes: '',\r\n\t\tcf7IdAttribute: '',\r\n\t\tcf7ClassAttribute: '',\r\n\t\tcf7sIcon: 'paperclip',\r\n\t\tnoChildren: true,\r\n\t},\r\n\t{\r\n\t\tcf7sType: 'number',\r\n\t\tcf7sSelectLabel: __( 'Number', 'contact-form-7-skins' ),\r\n\t\tcf7sSelectGroup: 'cf7Tag',\r\n\t\tcf7Name:  'number',\r\n\t\tcf7sLabel: '',\r\n\t\tcf7Required: false,\r\n\t\tcf7TagOptions: [\r\n\t\t\t{\r\n\t\t\t\tcf7Option: '',\r\n\t\t\t\toptionLabel: 'Field Type', // Spinbox, Slider\r\n\t\t\t\toptionType: 'select',\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\tcf7Option: '',\r\n\t\t\t\toptionLabel: __( 'Default value', 'contact-form-7-skins' ),\r\n\t\t\t\toptionType: 'input',\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\tcf7Option: 'placeholder',\r\n\t\t\t\tisChecked: false,\r\n\t\t\t\toptionLabel: __( 'Use this text as the placeholder of the field', 'contact-form-7-skins' ),\r\n\t\t\t\toptionType: 'checkbox',\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\tcf7Option: '',\r\n\t\t\t\toptionLabel: __( 'Range - min', 'contact-form-7-skins' ),\r\n\t\t\t\toptionType: 'input',\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\tcf7Option: '',\r\n\t\t\t\toptionLabel: __( 'Range - max', 'contact-form-7-skins' ),\r\n\t\t\t\toptionType: 'input',\r\n\t\t\t},\r\n\t\t],\r\n\t\tcf7TagType: 'number', // number: Spinbox, range: Slider\r\n\t\tcf7Values: '',\r\n\t\tcf7Placeholder: '',\r\n\t\tcf7Min: '',\r\n\t\tcf7Max: '',\r\n\t\tcf7IdAttribute: '',\r\n\t\tcf7ClassAttribute: '',\r\n\t\tcf7sIcon: 'plus-alt2',\r\n\t\tnoChildren: true,\r\n\t},\r\n\t{\r\n\t\tcf7sType: 'quiz',\r\n\t\tcf7sSelectLabel: __( 'Quiz', 'contact-form-7-skins' ),\r\n\t\tcf7sSelectGroup: 'cf7Tag',\r\n\t\tcf7Name: 'quiz',\r\n\t\tcf7sLabel: '',\r\n\t\tcf7Required: false,\r\n\t\tcf7Options: [ // Question Answer +\r\n\t\t\t{\r\n\t\t\t\tquestion: __( 'Question 1', 'contact-form-7-skins' ),\r\n\t\t\t\tanswer: __( 'Answer 1', 'contact-form-7-skins' ),\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\tquestion: __( 'Question 2', 'contact-form-7-skins' ),\r\n\t\t\t\tanswer: __( 'Answer 2', 'contact-form-7-skins' ),\r\n\t\t\t},\r\n\t\t],\r\n\t\tcf7IdAttribute: '',\r\n\t\tcf7ClassAttribute: '',\r\n\t\tcf7sIcon: 'editor-help',\r\n\t\tnoChildren: true,\r\n\t},\r\n\t{\r\n\t\tcf7sType: 'radio',\r\n\t\tcf7sSelectLabel: __( 'Radio Button (option)', 'contact-form-7-skins' ),\r\n\t\tcf7sSelectGroup: 'cf7Tag',\r\n\t\tcf7Name: 'radio',\r\n\t\tcf7sLabel: '',\r\n\t\tcf7Required: false,\r\n\t\tcf7TagOptions: [\r\n\t\t\t{\r\n\t\t\t\tcf7Option: 'label_first',\r\n\t\t\t\tisChecked: false,\r\n\t\t\t\toptionLabel: __( 'Put a label first, a checkbox last', 'contact-form-7-skins' ),\r\n\t\t\t\toptionType: 'checkbox',\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\tcf7Option: 'use_label_element',\r\n\t\t\t\tisChecked: false,\r\n\t\t\t\toptionLabel: __( 'Wrap each item with label element', 'contact-form-7-skins' ),\r\n\t\t\t\toptionType: 'checkbox',\r\n\t\t\t},\r\n\t\t],\r\n\t\tcf7Options: [ // Option Default +\r\n\t\t\t{\r\n\t\t\t\tvalue: __( 'Option 1', 'contact-form-7-skins' ), // default: 1\r\n\t\t\t\tisChecked: true, // radio-button\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\tvalue: __( 'Option 2', 'contact-form-7-skins' ),\r\n\t\t\t\tisChecked: false,\r\n\t\t\t},\r\n\t\t],\r\n\t\tcf7LabelFirst: false,\r\n\t\tcf7UseLabelElement: false,\r\n\t\tcf7IdAttribute: '',\r\n\t\tcf7ClassAttribute: '',\r\n\t\tcf7sIcon: 'marker',\r\n\t\tnoChildren: true,\r\n\t},\r\n\t{\r\n\t\tcf7sType: 'select',\r\n\t\tcf7sSelectLabel: __( 'Select (dropdown)', 'contact-form-7-skins' ),\r\n\t\tcf7sSelectGroup: 'cf7Tag',\r\n\t\tcf7Name: 'select',\r\n\t\tcf7sLabel: '',\r\n\t\tcf7Required: false,\r\n\t\tcf7TagOptions: [\r\n\t\t\t{\r\n\t\t\t\tcf7Option: '',\r\n\t\t\t\tisChecked: false,\r\n\t\t\t\toptionLabel: __( 'Allow multiple selections', 'contact-form-7-skins' ),\r\n\t\t\t\toptionType: 'checkbox',\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\tcf7Option: '',\r\n\t\t\t\tisChecked: false,\r\n\t\t\t\toptionLabel: __( 'Insert a blank item as the first option', 'contact-form-7-skins' ),\r\n\t\t\t\toptionType: 'checkbox',\r\n\t\t\t},\r\n\t\t],\r\n\t\tcf7Options: [ // Option Checked +\r\n\t\t\t{\r\n\t\t\t\tvalue: __( 'Option 1', 'contact-form-7-skins' ),\r\n\t\t\t\tisChecked: true,\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\tvalue: __( 'Option 2', 'contact-form-7-skins' ),\r\n\t\t\t\tisChecked: false,\r\n\t\t\t},\r\n\t\t],\r\n\t\tcf7Multiple: false,\r\n\t\tcf7IncludeBlank: false,\r\n\t\tcf7IdAttribute: '',\r\n\t\tcf7ClassAttribute: '',\r\n\t\tcf7sIcon: 'list-view',\r\n\t\tnoChildren: true,\r\n\t},\r\n\t{\r\n\t\tcf7sType: 'submit',\r\n\t\tcf7sSelectLabel: __( 'Submit', 'contact-form-7-skins' ),\r\n\t\tcf7sSelectGroup: 'cf7Tag',\r\n\t\tcf7Name: 'submit',\r\n\t\tcf7sLabel: 'Submit', // used as submit value\r\n\t\tcf7Values: '',\r\n\t\tcf7Required: false,\r\n\t\tcf7IdAttribute: '',\r\n\t\tcf7ClassAttribute: '',\r\n\t\tcf7sIcon: 'button',\r\n\t\tnoChildren: true,\r\n\t},\r\n\t{\r\n\t\tcf7sType: 'tel',\r\n\t\tcf7sSelectLabel: __( 'Telephone', 'contact-form-7-skins' ),\r\n\t\tcf7sSelectGroup: 'cf7Tag',\r\n\t\tcf7Name: 'tel',\r\n\t\tcf7sLabel: '',\r\n\t\tcf7Required: false,\r\n\t\tcf7TagOptions: [\r\n\t\t\t{\r\n\t\t\t\tcf7Option: '',\r\n\t\t\t\toptionLabel: __( 'Default value', 'contact-form-7-skins' ),\r\n\t\t\t\toptionType: 'input',\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\tcf7Option: 'placeholder',\r\n\t\t\t\tisChecked: false,\r\n\t\t\t\toptionLabel: __( 'Use this text as the placeholder of the field', 'contact-form-7-skins' ),\r\n\t\t\t\toptionType: 'checkbox',\r\n\t\t\t},\r\n\t\t],\r\n\t\tcf7Values: '',\r\n\t\tcf7Placeholder: false,\r\n\t\tcf7IdAttribute: '',\r\n\t\tcf7ClassAttribute: '',\r\n\t\tcf7sIcon: 'phone',\r\n\t\tnoChildren: true,\r\n\t},\r\n\t{\r\n\t\tcf7sType: 'text',\r\n\t\tcf7sSelectLabel: __( 'Text (short text)', 'contact-form-7-skins' ),\r\n\t\tcf7sSelectGroup: 'cf7Tag',\r\n\t\tcf7Name: 'text',\r\n\t\tcf7sLabel: '',\r\n\t\tcf7Required: false,\r\n\t\tcf7TagOptions: [\r\n\t\t\t{\r\n\t\t\t\tcf7Option: '',\r\n\t\t\t\toptionLabel: __( 'Default value', 'contact-form-7-skins' ),\r\n\t\t\t\toptionType: 'input',\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\tcf7Option: 'placeholder',\r\n\t\t\t\tisChecked: false,\r\n\t\t\t\toptionLabel: __( 'Use this text as the placeholder of the field', 'contact-form-7-skins' ),\r\n\t\t\t\toptionType: 'checkbox',\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\tcf7Option: 'akismet_author_email',\r\n\t\t\t\tisChecked: false,\r\n\t\t\t\toptionLabel: __( \"Akismet - this field requires author's email address\", 'contact-form-7-skins' ),\r\n\t\t\t\toptionType: 'checkbox',\r\n\t\t\t},\r\n\t\t],\r\n\t\tcf7Values: '',\r\n\t\tcf7Placeholder: false,\r\n\t\tcf7AkismetAuthor: false,\r\n\t\tcf7IdAttribute: '',\r\n\t\tcf7ClassAttribute: '',\r\n\t\tcf7sIcon: 'editor-textcolor',\r\n\t\tnoChildren: true,\r\n\t},\r\n\t{\r\n\t\tcf7sType: 'textarea',\r\n\t\tcf7sSelectLabel: __( 'Textarea (long text)', 'contact-form-7-skins' ),\r\n\t\tcf7sSelectGroup: 'cf7Tag',\r\n\t\tcf7Name: 'textarea',\r\n\t\tcf7sLabel: '',\r\n\t\tcf7Required: false,\r\n\t\tcf7TagOptions: [\r\n\t\t\t{\r\n\t\t\t\tcf7Option: '',\r\n\t\t\t\toptionLabel: __( 'Default value', 'contact-form-7-skins' ),\r\n\t\t\t\toptionType: 'input',\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\tcf7Option: 'placeholder',\r\n\t\t\t\tisChecked: false,\r\n\t\t\t\toptionLabel: __( 'Use this text as the placeholder of the field', 'contact-form-7-skins' ),\r\n\t\t\t\toptionType: 'checkbox',\r\n\t\t\t},\r\n\t\t],\r\n\t\tcf7Values: '',\r\n\t\tcf7Placeholder: false,\r\n\t\tcf7IdAttribute: '',\r\n\t\tcf7ClassAttribute: '',\r\n\t\tcf7sIcon: 'format-aside',\r\n\t\tnoChildren: true,\r\n\t},\r\n\t{\r\n\t\tcf7sType: 'url',\r\n\t\tcf7sSelectLabel: __( 'URL (website link)', 'contact-form-7-skins' ),\r\n\t\tcf7sSelectGroup: 'cf7Tag',\r\n\t\tcf7Name: 'url',\r\n\t\tcf7sLabel: '',\r\n\t\tcf7Required: false,\r\n\t\tcf7TagOptions: [\r\n\t\t\t{\r\n\t\t\t\tcf7Option: '',\r\n\t\t\t\toptionLabel: __( 'Default value', 'contact-form-7-skins' ),\r\n\t\t\t\toptionType: 'input',\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\tcf7Option: 'placeholder',\r\n\t\t\t\tisChecked: false,\r\n\t\t\t\toptionLabel: __( 'Use this text as the placeholder of the field', 'contact-form-7-skins' ),\r\n\t\t\t\toptionType: 'checkbox',\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\tcf7Option: 'akismet_author_email',\r\n\t\t\t\tisChecked: false,\r\n\t\t\t\toptionLabel: __( \"Akismet - this field requires author's email address\", 'contact-form-7-skins' ),\r\n\t\t\t\toptionType: 'checkbox',\r\n\t\t\t},\r\n\t\t],\r\n\t\tcf7Values: '',\r\n\t\tcf7Placeholder: false,\r\n\t\tcf7AkismetAuthorUrl: false,\r\n\t\tcf7IdAttribute: '',\r\n\t\tcf7ClassAttribute: '',\r\n\t\tcf7sIcon: 'admin-links',\r\n\t\tnoChildren: true,\r\n\t},\r\n\t{\r\n\t\tcf7sType: 'recaptcha', // @since 0.5.4\r\n\t\tcf7sSelectLabel: 'reCAPTCHA',\r\n\t\tcf7sSelectGroup: 'cf7Tag',\r\n\t\tcf7Name: 'recaptcha',\r\n\t\tcf7sLabel: '',\r\n\t\tcf7IdAttribute: '',\r\n\t\tcf7Size: '',\r\n\t\tcf7Theme: '',\r\n\t\tcf7ClassAttribute: '',\r\n\t\tcf7sIcon: 'update',\r\n\t\tnoChildren: true,\r\n\t},\r\n\t\r\n/** CF7 Skins Items **/\r\n\t{\r\n\t\tcf7sType: 'fieldset',\r\n\t\tcf7sSelectLabel: __( 'Fieldset (with legend)', 'contact-form-7-skins' ),\r\n\t\tcf7sSelectGroup: 'cf7sItem',\r\n\t\tcf7Name: 'fieldset',\r\n\t\tcf7sLabel: __( 'Legend ..', 'contact-form-7-skins' ), // used as legend - has default value\r\n\t\tcf7sIcon: 'category',\r\n\t\t// SK - why aren't we setting 'noChildren' key here\r\n\t},\r\n\t{\r\n\t\tcf7sType: 'list-ol',\r\n\t\tcf7sSelectLabel: __( 'List - ol', 'contact-form-7-skins' ),\r\n\t\tcf7sSelectGroup: 'cf7sItem',\r\n\t\tcf7Name: 'listol',\r\n\t\tcf7sLabel: '', // not displayed at this stage - consider allowing user to add this\r\n\t\tcf7sIcon: 'editor-ol',\r\n\t\tnoChildren: false,\r\n\t},\r\n\t{\r\n\t\tcf7sType: 'list-li',\r\n\t\tcf7sSelectLabel: __( 'List Item - li', 'contact-form-7-skins' ),\r\n\t\tcf7sSelectGroup: 'cf7sItem',\r\n\t\tcf7Name: 'listli',\r\n\t\tcf7sLabel: '', // not displayed at this stage - consider allowing user to add this\r\n\t\tcf7sIcon: 'editor-ul',\r\n\t\tnoChildren: false,\r\n\t},\r\n\t{\r\n\t\tcf7sType: 'paragraph',\r\n\t\tcf7sSelectLabel: __( 'Paragraph - p', 'contact-form-7-skins' ),\r\n\t\tcf7sSelectGroup: 'cf7sItem',\r\n\t\tcf7Name: 'paragraph',\r\n\t\tcf7sContent: '', // @previous: paragraph. Permitted content: https://developer.mozilla.org/en-US/docs/Web/HTML/Content_categories#Phrasing_content\r\n\t\tcf7sIcon: 'editor-paragraph',\r\n\t\tnoChildren: true,\r\n\t},\r\n\r\n];\r\n\r\nconst cf7sItems = applyFilters( 'cf7sItems', [ ...cf7sItemsDefault ] );\r\n\r\nexport { cf7sItems };\r\n", "/**\r\n * CF7 Skins Visual Rules.\r\n * \r\n * Field/node event rules for RST\r\n *   - drop a node from Select or elsewhere in Form\r\n *   - click a node from Select\r\n *   - copy/duplicate within Form\r\n * \r\n * Functions:\r\n *   cf7sAddonRules -\r\n *   cf7sDropRules - \r\n *   cf7sSurroundingRules - \r\n *   cf7sDuplicateRules -\r\n *   cf7sCountType - \r\n *   cf7sHasChildren - \r\n * \r\n * @since 0.4.0\r\n */\r\nimport { cf7sItems } from './cf7sItems';\r\nimport { cloneDeep } from 'lodash'; // @since 0.6.1\r\nimport { \r\n\trandomizeName, // @since 0.6.3\r\n\tgetVisualVar // @since 2.3.0\r\n} from './functions';\r\n\r\nconst { sprintf, __ } = { ...window.wp.i18n }; // @since 0.7.2\r\nconst { applyFilters, } = wp.hooks;\r\n\r\n/**\r\n * Get list-ol default property from cf7sItems.\r\n * \r\n * OL node from cf7sItems includes LI as default children\r\n * Need to clone cf7sItems to avoid modification by CF7 Skins rules.\r\n * \r\n * @since 0.6.1\r\n */\r\nconst nodeOL = cloneDeep( cf7sItems ).filter( ( obj ) => {\r\n\treturn obj.cf7sType === 'list-ol';\r\n} )[0];\r\n\r\n/**\r\n * Get list-li default property from cf7sItems.\r\n * \r\n * Need to clone cf7sItems to avoid modification by CF7 Skins rules.\r\n * \r\n * @since 0.6.1\r\n */\r\nconst nodeLI = cloneDeep( cf7sItems ).filter( ( obj ) => {\r\n\treturn obj.cf7sType === 'list-li';\r\n} )[0];\r\n\r\n/**\r\n * Get addon rules for drop, surrounding, and duplicate.\r\n *\r\n * @param {String}\t\ttype\t\trule type, drop, surround, or duplicate\r\n *\t\t\t\t\t\t\t\t\tSee each rule below for each addon function parameter\r\n *\r\n * @return {Array} \tall registered addon rules for specific type\r\n * \r\n * @since 0.7.2\r\n */\r\nfunction cf7sAddonRules( type = 'drop' ) {\r\n\tlet rules = {};\r\n\t\r\n\t/**\r\n\t * Apply addons rules. Addon can register their own rule functions using object \r\n\t * with a key and function pairs. The function will have parameters as listed below.\r\n\t * Function should return boolean value true to enable or false to disable drop, surround,\r\n\t * or duplicate. For example:\r\n\t * addFilter( 'cf7svisual.cf7sRules', 'cf7svisual', ( filter ) => {\r\n\t *    filter.push( { drop: myDropFunc, surround: mySurroundFunc } );\r\n\t *    return filter;\r\n\t * });\t \r\n\t * \r\n\t * @since 0.7.0\r\n\t */\r\n\tconst cf7sRules = applyFilters( 'cf7svisual.cf7sRules', [] );\r\n\t\r\n\tif ( !! cf7sRules ) { // only if not empty\r\n\t\tfor ( let i = 0; i < cf7sRules.length; ++i ) { // loop for each registered addon\r\n\t\t\t\r\n\t\t\tfor ( let type in cf7sRules[i] ) { // loop for each addon rule object\r\n\t\t\t\t\r\n\t\t\t\tif ( ! rules[type] ) { // set array if type rule does not exist\r\n\t\t\t\t\trules[type] = [];\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\trules[type].push( cf7sRules[i][type] ); // add addon rule to array\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\t\r\n\tif ( !! rules && !! rules[type] ) { // requested rule type\r\n\t\treturn rules[type];\r\n\t}\r\n}\r\n\r\n/**\r\n * Drop rules for current node into other parent.\r\n * \r\n * Return false to prevent node from dropping in the given location.\r\n * @link https://github.com/fritz-c/react-sortable-tree\r\n *\r\n * @params \tnode\t\t\t(object) \t\tcurrent dropped node\r\n *\t  \t\tprevPath\t\t(number/string)\tprevious path\r\n *\t  \t\tprevParent\t\t(object)\t\tprevious parent object\r\n *\t  \t\tprevTreeIndex\t(number) \t\tprevious tree index\r\n *\t  \t\tnextPath\t\t(number/string)\tnext path\r\n *\t  \t\tnextParent\t\t(object)\t\tnext parent object\r\n *\t  \t\tnextTreeIndex\t(number)\t\tnext tree index\r\n *\t  \t\ttreeData\t\t(object)\t\tcurrent tree data object\r\n * \r\n * @return (Boolean)\r\n * \r\n * @since 0.4.0\r\n */\r\nexport function cf7sDropRules( object ) {\r\n\t\r\n\tconst {\r\n\t\tnode,\r\n\t\tprevTreeIndex,\r\n\t\tnextParent,\r\n\t} = { ...object };\r\n\t\r\n\t// Read global treeData if is set by addons, or use object treeData\r\n\t// @since 0.6.8\r\n\tconst treeData = getVisualVar('treeData') || object.treeData;\r\n\r\n\t/**\r\n\t * Rule: CF7 Tag\r\n\t * Only one CF7 tag inside list-li\r\n\t * Check while moving tag into another LI parent, and has children children\r\n\t */\r\n\tif ( nextParent && nextParent.cf7sType === 'list-li' && nextParent.children ) {\r\n\t\t\r\n\t\t// Loop all parent's children.\r\n\t\t// Not sure why nextParent.children contains dropped node candidate, even if it has not been dropped yet. Bug?\r\n\t\t// Count total CF7 tags inside parent.\r\n\t\tlet countCF7Tags = 0;\r\n\t\tfor ( let child of nextParent.children ) {\r\n\t\t\tfor ( let tag of cf7sItems ) { // loop cf7sItems\r\n\t\t\t\tif ( child.cf7sType === tag.cf7sType && 'cf7Tag' === tag.cf7sSelectGroup ) {\r\n\t\t\t\t\tcountCF7Tags++;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t// Prevent if list-li already has CF7 tag.\r\n\t\tif ( countCF7Tags > 1 ) {\r\n\t\t\tconsole.warn( 'Only one CF7 tag for each list' );\r\n\t\t\treturn false;\r\n\t\t}\r\n\t}\r\n\t\r\n\t/**\r\n\t * Rule: reCAPTCHA \r\n\t * Prevent drop/click if CF7 reCAPTCHA integration is not activated \r\n\t * using window.cf7svisual.integration.reCAPTCHA (on hold)\r\n\t * Only one reCAPTCHA tag per form\r\n\t * New dragged reCAPTCHA does not have prevTreeIndex\r\n\t * Disable duplication if node has recaptcha children\r\n\t */\r\n\tif ( !! node && null == prevTreeIndex && 'recaptcha' === node.cf7sType ) {\r\n\t\t// Count and prevent if form already has a reCAPTCHA\r\n\t\t// 0 = no reCAPTCHA found in tree\r\n\t\tif ( cf7sCountType( node.cf7sType, treeData ) > 0 && null == prevTreeIndex ) {\r\n\t\t\tconsole.warn( __( 'Only one reCAPTCHA per form allowed.', 'contact-form-7-skins' ) );\r\n\t\t\treturn false;\r\n\t\t}\r\n\t}\r\n\r\n\t/**\r\n\t * Rule: only one submit per form\r\n\t * For duplicate event, current node is empty\r\n\t * New dragged submit has undefined prevTreeIndex\r\n\t */\r\n\tif ( !! node && null == prevTreeIndex && 'submit' === node.cf7sType ) {\r\n\t\t\r\n\t\t// Count and prevent if form already has a submit tag\r\n\t\t// 0 = no submit found in tree\r\n\t\tif ( cf7sCountType( node.cf7sType, treeData ) > 0 ) {\r\n\t\t\tconsole.warn( __( 'Only one submit for each form.', 'contact-form-7-skins' ) );\r\n\t\t\treturn false;\r\n\t\t}\r\n\t}\r\n\t\r\n\t/**\r\n\t * Rule: disable drop for cf7sRoot to any node\r\n\t * Return if is cf7sRoot and next parent target is not null\r\n\t * @since 2.1.2\r\n\t */\r\n\tif ( !! node.cf7sRoot && !! nextParent ) {\r\n\t\tconsole.warn( __( 'Root node can not be dragged to other node.', 'contact-form-7-skins' ) );\r\n\t\treturn false;\r\n\t}\r\n\t\r\n\t// Apply addons rules @since 0.7.0\r\n\tconst addonRules = cf7sAddonRules( 'drop' ); \r\n\t\r\n\tif ( !! addonRules && !! addonRules.length ) { // requested rule type\r\n\t\tfor ( let i = 0; i < addonRules.length; ++i ) { // loop addon rule\r\n\t\t\tif ( ! addonRules[i]( object ) )\r\n\t\t\t\treturn false; // bail early, no need to call subsequent rules\r\n\t\t}\r\n\t}\r\n\r\n\t// Return nextParent if no rules applied to prevent drop\r\n\t// or return message\r\n\treturn ! nextParent || ! nextParent.noChildren;\r\n}\r\n\r\n/**\r\n * OL/LI surrounding rules for tag, based on parent node.\r\n * \r\n * @param {Object}\tnode\t\t\tcurrent dropped/clicked node\r\n * @param {Object}\tparentNode\t\tnext parent node to move\r\n *\r\n * @return {Object} initial node or node with surrounding element\r\n * \r\n * @since 0.4.0\r\n */\r\nexport function cf7sSurroundingRules( node, parentNode ) {\r\n\r\n\t// Rule: Add LI as default children of OL for new node\r\n\t// New node has no children, and no need to return.\r\n\t// nodeLI can be rewritten by other rules, LI should not contains any children, \r\n\t// @since 0.6.1\t\r\n\tif ( node.cf7sType === 'list-ol' && ! node.children ) {\r\n\t\tlet LI = randomizeName( cloneDeep( nodeLI ) ); // clone to avoid have same name as others @since 0.6.3\r\n\t\tLI.children = []; // empty LI children\r\n\t\tnode.children = [ { ...LI } ]; // add LI as OL children\r\n\t\tnode.expanded = true; // make it expanded\r\n\t}\r\n\t\r\n\t// Rule: Add enclosed OL & LI to Fieldsets\r\n\t// @since 0.6.1\r\n\tif ( ! parentNode || parentNode.cf7sType === 'fieldset' ) {\r\n\t\t// Draged/clicked Fielset from SelectItem has no children. See cf7sItems for Fieldset.\r\n\t\t// Only for new Fieldset, not moving it from other parent in tree\r\n\t\tif ( ! node.children && 'fieldset' === node.cf7sType ) {\r\n\t\t\t// Clone to avoid have same name as others @since 0.6.3\r\n\t\t\tlet OL = randomizeName( cloneDeep( nodeOL ) );\r\n\t\t\tlet LI = randomizeName( cloneDeep( nodeLI ) );\r\n\t\t\t\t\r\n\t\t\tOL.children = [ { ...LI } ]; // add LI as OL children, LI as default OL children might be removed\r\n\t\t\tOL.expanded = true; // make it expanded\r\n\t\t\tnode.children = [ { ...OL } ]; // add OL as Fieldset children\r\n\t\t\tnode.expanded = true; // make it expanded\r\n\t\t\treturn node;\r\n\t\t}\r\n\t}\r\n\t\r\n\t// Rule: CF7 Tag (if not reCAPTCHA or Submit) added into Form or Fieldset should be wrapped with OL > LI\r\n\t// @since 0.6.1\r\n\r\n\tif ( ! parentNode || parentNode.cf7sType === 'fieldset' || \r\n    \t( false === parentNode.noChildren && [ 'list-ol', 'list-li' ].indexOf( parentNode.cf7sType ) < 0 ) ) {\r\n\t\tif ( 'cf7Tag' === node.cf7sSelectGroup ) {\r\n\t\t\tif ( [ 'recaptcha', 'submit' ].indexOf( node.cf7sType ) < 0 ) {\t\r\n\r\n\t\t\t\t// Clone to avoid have same name as others @since 0.6.3\r\n\t\t\t\tlet OL = randomizeName( cloneDeep( nodeOL ) );\r\n\t\t\t\tlet LI = randomizeName( cloneDeep( nodeLI ) );\r\n\t\t\t\t\r\n\t\t\t\tLI.children = [ { ...node } ];\r\n\t\t\t\tLI.expanded = true; // make it expanded\r\n\t\t\t\t\r\n\t\t\t\t// Default list-ol contains list-li as a children, \r\n\t\t\t\t// Overwrite dragged list-li node as a children to prevent lost\r\n\t\t\t\tOL.children = [ { ...LI } ];\r\n\t\t\t\tOL.expanded = true; // make it expanded\r\n\t\t\t\t\r\n\t\t\t\treturn OL;\r\n\t\t\t}\r\n\t\t}\r\n\t}\t\r\n\r\n\t// Rule: Add surrounding li to node inside an ol\r\n\t// If parent is a list-ol and selected node is not a list-li\r\n\tif ( ( parentNode && parentNode.cf7sType === 'list-ol' ) && node.cf7sType !== 'list-li' ) {\r\n\t\t\r\n\t\tlet LI = randomizeName( cloneDeep( nodeLI ) ); // clone to avoid have same name as others @since 0.6.3\r\n\t\tLI.expanded = true; // expand after added\r\n\t\tLI.children = [ { ...node } ]; // add current selected node\r\n\t\treturn LI;\r\n\r\n\t// Rule: Add surrounding ol for list-li if parent is not a list-ol\r\n\t// Only if current node type is a list-li ( with children if available ) and\r\n\t// without target parentNode or not a list-ol target parentNode.\r\n\t} else if ( ( node.cf7sType === 'list-li' && parentNode === null ) \r\n\t\t|| ( node.cf7sType === 'list-li' && parentNode.cf7sType !== 'list-ol' ) ) {\r\n\t\t\r\n\t\tlet OL = randomizeName( cloneDeep( nodeOL ) ); // clone to avoid have same name as others @since 0.6.3\r\n\t\tOL.children = [ { ...node } ]; // overwrite dragged list-li node as a children to prevent lost\r\n\t\tOL.expanded = true; // expand after added\r\n\t\treturn OL;\r\n\t}\r\n\t\r\n\t// Apply addons rules @since 0.7.2\r\n\tconst addonRules = cf7sAddonRules( 'surround' ); \r\n\t\r\n\tif ( !! addonRules && !! addonRules.length ) { // requested rule type\r\n\t\tfor ( let i = 0; i < addonRules.length; ++i ) { // loop addon rule\r\n\t\t\tnode = addonRules[i]( { node, parentNode, nodeOL, nodeLI, randomizeName } ); // run addon function and return it's value\r\n\t\t}\r\n\t}\r\n\r\n\treturn node;\r\n}\r\n\r\n/**\r\n * Duplicating rules.\r\n *\r\n * @param {Object}\trowInfo\t\tcurrent tree data\r\n *\r\n * @return {Boolean/String} true to enable duplicating or string message to disable\r\n * \r\n * @since 0.6.3\r\n */\r\nexport function cf7sDuplicateRules( rowInfo ) {\r\n\t\r\n\tconst { node, parentNode } = { ...rowInfo };\r\n\t\r\n\t// Rule: disable duplicate submit or recaptcha\r\n\tif ( 'recaptcha' === node.cf7sType || 'submit' === node.cf7sType ) {\r\n\t\treturn sprintf( __( 'Only one %s allowed in a form.', 'contact-form-7-skins' ), node.cf7sType );\r\n\t}\r\n\t\r\n\t// Rule: disable if node contains children of submit or recaptcha\r\n\tif ( cf7sHasChildren( node, 'recaptcha' ) ) {\r\n\t\treturn __( 'Node has recaptcha children. Only one recaptcha allowed in a form.', 'contact-form-7-skins' );\r\n\t}\r\n\tif ( cf7sHasChildren( node, 'submit' ) ) {\r\n\t\treturn __( 'Node has submit children. Only one submit allowed in a form.', 'contact-form-7-skins' );\r\n\t}\r\n\t\t\r\n\t// Rule: only one CF7 tag inside list-li\r\n\tif ( !! parentNode && 'list-li' === parentNode.cf7sType && 'cf7Tag' === node.cf7sSelectGroup ) {\r\n\t\treturn `surrounding`;\r\n\t}\t\r\n\r\n\t// Apply addons rules @since 0.7.2\r\n\tconst addonRules = cf7sAddonRules( 'duplicate' ); \r\n\t\r\n\tif ( !! addonRules && !! addonRules.length ) { // requested rule type\r\n\t\tfor ( let i = 0; i < addonRules.length; ++i ) { // loop addon rule\r\n\t\t\taddonRules[i]( rowInfo ); // run addon function and return it's value\r\n\t\t}\r\n\t}\r\n\t\r\n\treturn true;\r\n}\r\n\r\n/**\r\n * Count nodes by specific type in tree.\r\n *\r\n * @param {String}\ttype\t\tnode type to check and count\r\n * @param {Object}\ttreeData\tcurrent tree data\r\n *\r\n * @return {TYPE} the number of found nodes\r\n * \r\n * @since 0.6.1\r\n */\r\nfunction cf7sCountType( type, treeData ) {\r\n\tlet count = 0;\r\n\tconst countNodes = ( nodes ) => {\r\n\t\tfor ( let node of nodes ) {\r\n\t\t\tif ( type === node.cf7sType ) {\r\n\t\t\t\tcount++;\r\n\t\t\t}\r\n\r\n\t\t\tif ( !! node.children ) { // check if have children\r\n\t\t\t\tcountNodes( node.children ); // recursive loop for children\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\t\r\n\tcountNodes( treeData );\r\n\t\r\n\treturn count;\r\n}\r\n\r\n/**\r\n * Check if given node has specific node type in children, grand-children.\r\n *\r\n * @param {Object}\tnode\tcurrent tree data\r\n * @param {String}\ttype\tthe children node type to find\r\n *\r\n * @return {Boolean}\r\n * \r\n * @since 0.6.3\r\n */\r\nfunction cf7sHasChildren( node, type ) {\r\n\tif ( ! node.children ) { // bail early if has no children\r\n\t\treturn false;\r\n\t}\r\n\r\n\tfor ( let child of node.children ) {\r\n\t\tif ( type === child.cf7sType ) { // child type exists, bail return true\r\n\t\t\treturn true;\r\n\t\t}\r\n\r\n\t\treturn cf7sHasChildren( child, type ); // recursive loop for children\r\n\t}\r\n}\r\n", "/**\r\n * CF7 Skins Visual Utility Functions.\r\n * \r\n * @since 0.6.1\r\n */\r\n\r\nimport React from 'react';\r\nimport ReactDOM from 'react-dom';\r\nimport { isPlainObject } from 'lodash';\r\nimport { cf7sItems, } from './';\r\nimport { cf7sRequest } from './api';\r\n\r\nconst { sprintf, __ } = { ...window.wp.i18n };\r\nconst { applyFilters } = wp.hooks;\r\nconst { dispatch, select, } = { ...window.wp.data }; // @since 2.5.0\r\n\r\n/**\r\n * Setup default treeData for RST and add-ons.\r\n * \r\n * @return {Object}\t\ttreeData\t\tdefault RST treeData\r\n * \r\n * @since 0.7.0\r\n */\r\n export function defaultTreeData() {\r\n\r\n\tconst tags = [], treeData = [];\r\n\r\n\t// Arrange cf7sItems based on cf7sType\r\n\tfor ( let item of cf7sItems ) {\r\n\t\ttags[ item.cf7sType ] = randomizeName( item );\r\n\t}\r\n\r\n\t// Set default treeData\r\n\ttags['fieldset'].expanded = true; // make fieldset children expanded\r\n\ttags['fieldset'].cf7sLabel = __( 'Legend', 'contact-form-7-skins' ); // add label\r\n\ttreeData.push( tags['fieldset'] ); // insert fieldset\r\n\r\n\t\ttags['list-ol'].expanded = true;\r\n\t\ttreeData[0].children = [ { ...tags['list-ol'] } ]; // insert list-ol as fieldset children\r\n\r\n\t\t\ttags['text'].cf7sLabel = __( 'Your Name (required)', 'contact-form-7-skins' );\r\n\t\t\ttags['text'].cf7Required = true;\r\n\t\t\ttags['list-li'].expanded = true;\r\n\t\t\ttags['list-li'].children = [ { ...tags['text'] } ];\r\n\t\t\ttreeData[0].children[0].children = [ { ...tags['list-li'] } ]; // insert list-li to list-ol\r\n\r\n\t\t\ttags['email'].cf7sLabel = __( 'Email Address (required)', 'contact-form-7-skins' );\r\n\t\t\ttags['email'].cf7Required = true;\r\n\t\t\ttags['list-li'] = randomizeName( tags['list-li'] ); // randomize list-li\r\n\t\t\ttags['list-li'].children = [ { ...tags['email'] } ]; // insert email as list-li children\r\n\t\t\ttreeData[0].children[0].children[1] = { ...tags['list-li'] }; // insert list-li to list-ol\r\n\r\n\t\t\ttags['textarea'].cf7sLabel = __( 'Your Message', 'contact-form-7-skins' );\r\n\t\t\ttags['list-li'] = randomizeName( tags['list-li'] ); // randomize list-li\r\n\t\t\ttags['list-li'].children = [ { ...tags['textarea'] } ]; // insert textarea as list-li children\r\n\t\t\ttreeData[0].children[0].children[2] = { ...tags['list-li'] }; // insert list-li to list-ol\r\n\r\n\t\ttags['paragraph'].cf7sContent = __( '* Required', 'contact-form-7-skins' );\r\n\t\ttreeData[0].children[1] = { ...tags['paragraph'] }; // insert paragraph as fieldset children\r\n\r\n\ttags['submit'].cf7sLabel = __( 'Send', 'contact-form-7-skins' );\r\n\ttreeData.push( tags['submit'] ); // insert submit button\r\n\r\n\treturn treeData;\r\n}\r\n\r\n/**\r\n * Save Visual Form.\r\n * \r\n * @param {Array}\t\ttabIds\t\tRegistered Tabs\r\n * \r\n * @since 0.4.0\r\n */\r\n export function saveVisualForm() {\r\n\tconst { setNotice } = dispatch( 'cf7svisual' );\r\n\tconst state = { ...select( 'cf7svisual' ).getStates() };\r\n\r\n\t// Read global treeData if is set by addons, or use Visual treeData\r\n\tconst treeData = window.cf7svisual.treeData /* @since 0.7.0 */ || state.treeData.slice();\r\n\r\n\tconst { cf7svisual } = window; // window.cf7svisual\r\n\r\n\tif ( 'development' === cf7svisual.environment ) { // logging @since 0.7.0\r\n\t\tconsole.log( 'on save:', treeData);\r\n\t\tconsole.log( JSON.stringify( treeData ) );\r\n\t}\r\n\r\n\t// Return if window.cf7svisual does not exist in Production, or if saved in Dev mode.\r\n\tif ( ! cf7svisual || ! document.getElementById('post_ID') ) {\r\n\t\tsetNotice( __( 'Can not save! window.cf7svisual or post ID does not exist.', 'contact-form-7-skins' ), 'error' );\r\n\t\treturn;\r\n\t}\r\n\r\n\t// Get form id and <form/> element\r\n\tconst form_id = document.getElementById('post_ID').getAttribute('value');\r\n\tconst CF7Form = document.getElementById('wpcf7-admin-form-element');\r\n\r\n\t// Input to add to CF7 form before CF7 form submit\r\n\tconst appendedInput = document.createElement('input');\r\n\r\n\t// If there isn't a form already, then set the critical input element\r\n\t// attributes first before submitting the form\r\n\tif (!form_id || form_id < 0) {\r\n\t\tappendedInput.setAttribute( 'type', 'hidden' );\r\n\t\tappendedInput.setAttribute( 'name', 'cf7s-visual' );\r\n\t\tappendedInput.setAttribute( 'value', JSON.stringify( treeData ) );\r\n\t\tCF7Form.appendChild( appendedInput );\r\n\t\t// submit CF7 form itself\r\n\t\tCF7Form.submit();\r\n\t} else {\r\n\r\n\t\t// Show spinner\r\n\t\t// const spinner = document.getElementsByClassName('cf7sSpinner')[0];\r\n\t\t// spinner.style.visibility = 'visible';\r\n\t\t\r\n\t\t// Object data to send during AJAX request\r\n\t\tconst postData = applyFilters( 'cf7svisual.postData', {\r\n\t\t\taction   : 'cf7skins_visual_update', // callback function\r\n\t\t\tform_id  : form_id,\r\n\t\t\tvisual   : JSON.stringify( treeData ),\r\n\t\t\ttemplate : document.getElementById( 'cf7s-template' ).value , // since 0.6.2\r\n\t\t\tstyle    : document.getElementById( 'cf7s-style' ).value, // since 0.6.2\r\n\t\t});\r\n\r\n\t\t// If title has changed, window.cf7svisual.title is not the same as DOM #title value.\r\n\t\t// Add title property to postData for AJAX post\r\n\t\t// @since 0.6.3\r\n\t\tconst title = document.getElementById('title').value;\r\n\t\tif ( cf7svisual.title !== title ) {\r\n\t\t\tpostData.title = title;\r\n\t\t}\r\n\r\n\t\t// Save visual data into database using AJAX\r\n\t\tcf7sRequest( postData, 'json' )\r\n\t\t\t.then( ( data ) => { // object\r\n\t\t\t\twindow.cf7sAdmin.getTextarea().value = data.form; // update active textarea\r\n\r\n\t\t\t\twindow.onbeforeunload = null; // remove leave warning @since 0.6.5\r\n\r\n\t\t\t\t// Add Internet Explorer checks for no support of Event constructor @since 0.6.8\r\n\t\t\t\tlet event; \r\n\t\t\t\tif ( typeof( Event ) === 'function' ) {\r\n\t\t\t\t\tevent = new Event( 'change', { 'bubbles': true } )\r\n\t\t\t\t} else {\r\n\t\t\t\t\tevent = document.createEvent('Event');\r\n\t\t\t\t\tevent.initEvent( 'change', true, true );\r\n\t\t\t\t}\r\n\r\n\t\t\t\t// Trigger change to active textarea @since 0.6.5\r\n\t\t\t\twindow.cf7sAdmin.getTextarea().dispatchEvent( \r\n\t\t\t\t\tnew Event( 'change', { 'bubbles': true } )\r\n\t\t\t\t);\r\n\r\n\t\t\t\t// spinner.style.visibility = 'hidden'; // hide spinner\r\n\r\n\t\t\t\t// Show admin notification after successful Save\r\n\t\t\t\t// Follow WordPress HTML structure example for admin notice with dismissable click button\r\n\t\t\t\t// @link https://codex.wordpress.org/Plugin_API/Action_Reference/admin_notices\r\n\t\t\t\t// @link https://reactjs.org/docs/react-dom.html#unmountcomponentatnode\r\n\t\t\t\t// @since 0.5.2\r\n\t\t\t\tconst adminNotice = (\r\n\t\t\t\t\t<div className=\"notice notice-success is-dismissible\">\r\n\t\t\t\t\t\t<p>Visual saved!</p>\r\n\t\t\t\t\t\t<button type=\"button\" className=\"notice-dismiss\" \r\n\t\t\t\t\t\t\t/* Remove admin notification section on click dismiss */\r\n\t\t\t\t\t\t\tonClick={ () => ReactDOM.unmountComponentAtNode( document.getElementById('cf7s-visual-notice') ) }>\r\n\t\t\t\t\t\t\t\t<span className=\"screen-reader-text\">\r\n\t\t\t\t\t\t\t\t\t{ __( 'Dismiss this notice.', 'contact-form-7-skins' ) }\r\n\t\t\t\t\t\t\t\t</span>\r\n\t\t\t\t\t\t</button>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t);\r\n\r\n\t\t\t\t// Render notice into <div id=\"cf7s-visual-notice\"></div> DOM\r\n\t\t\t\tReactDOM.render( adminNotice, document.getElementById('cf7s-visual-notice') );\r\n\r\n\t\t\t\tsetNotice( __( 'Visual saved!', 'contact-form-7-skins' ), 'success' );\r\n\r\n\t\t\t\t// Run addon callback JavaScript functions after save\r\n\t\t\t\t// Callbacks can contains Object or String i.e. [ {\"MyNameSpace\":\"myFunction\"}, \"anotherFunction\" ];\r\n\t\t\t\t// @since 0.6.4\r\n\t\t\t\tif ( !! data.callbacks ) {\r\n\t\t\t\t\tfor ( let callback of data.callbacks ) {\r\n\t\t\t\t\t\tif ( \"object\" === typeof( callback ) ) { // object type, namespace function\r\n\t\t\t\t\t\t\tlet namespace = Object.keys(callback)[0]; // namespace\r\n\t\t\t\t\t\t\tif ( typeof window[namespace] === \"undefined\" ) { // namespace is not defined\r\n\t\t\t\t\t\t\t\tconsole.warn( sprintf( __( 'Namespace %s is undefined!', 'contact-form-7-skins' ), namespace ) );\r\n\t\t\t\t\t\t\t\tcontinue; // continue to next loop\r\n\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\tlet func = callback[namespace]; // namespace function\r\n\t\t\t\t\t\t\tif ( typeof window[namespace][func] !== \"undefined\" ) { // function is defined\r\n\t\t\t\t\t\t\t\twindow[namespace][func].apply();\r\n\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\tconsole.warn( sprintf( __( 'Function %1$s.%2$s is undefined!', 'contact-form-7-skins' ), namespace, `${func}()` ) );\r\n\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t} else { // function type\r\n\t\t\t\t\t\t\tif ( typeof window[callback] !== \"undefined\" ) { // function is defined\r\n\t\t\t\t\t\t\t\twindow[callback].apply();\r\n\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\tconsole.warn( sprintf( __( 'Function %s is undefined!', 'contact-form-7-skins' ), `${callback}()` ) );\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t} )\r\n\t\t\t.catch( ( err ) => console.error( err ) );\r\n\t}\r\n}\r\n\r\n/**\r\n * Add tag name numbering using random number.\r\n * \r\n * Adds random number to cf7sType e.g. text-777\r\n * Generates unique CF7 Form-tag Name when field is added to form\r\n * Can be used while dragging, clicking, duplicating (single or nested) item\r\n * \r\n * @param  {Object}\tnode\ttag << NEEDS BETTER DESCRIPTION\r\n * @return {Object}\tnode\r\n * \r\n * @since 0.4.0\r\n */\r\nexport function randomizeName( node ) {\r\n\tconst min = 100, max = 999;\r\n\tlet rand = Math.floor( Math.random() * (max - min) + min );\r\n\tnode.cf7Name = `${ node.cf7sType }-${ rand }`;\r\n\r\n\tif ( !! node.children ) { // loop children\r\n\t\tfor ( let child of node.children ) {\r\n\t\t\trandomizeName( child );\r\n\t\t}\r\n\t}\r\n\t\r\n\treturn node;\r\n}\r\n\r\n/**\r\n * Merge node with default properties from cf7sItems.js such as cf7sIcon, cf7sSelectGroup etc.\r\n * \r\n * This can be used to merge with cf7sItems after selecting template\r\n * \r\n * @param  {Array}\tnode\tnode item with minimal property\r\n * \r\n * @return {Array}\tmerging nodes with full cf7sItems property\r\n * \r\n * @since 0.4.0\r\n */\r\nexport function mergeDefault( node ) {\r\n\tfor ( let i in node ) { // plain object is not iterable using 'for ...of' loop\r\n\t\tconst item = cf7sItems.filter( ( obj ) => { // get default item properties\r\n\t\t\treturn obj.cf7sType === node[i].cf7sType;\r\n\t\t} )[0];\r\n\r\n\t\tnode[i] = { ...item, ...node[i] }; // merge objects with spread operator\r\n\r\n\t\tif ( !! node[i].children ) { // loop children\r\n\t\t\tmergeDefault( node[i].children ); // recursive\r\n\t\t}\r\n\t}\r\n\r\n\treturn node;\r\n}\r\n\r\n/**\r\n * Compares two software version numbers (e.g. \"1.7.1\" or \"1.2b\").\r\n *\r\n * This function was born in http://stackoverflow.com/a/6832721.\r\n *\r\n * @param {string} v1 The first version to be compared.\r\n * @param {string} v2 The second version to be compared.\r\n * @param {object} [options] Optional flags that affect comparison behavior:\r\n * lexicographical: (true/[false]) compares each part of the version strings lexicographically instead of naturally; \r\n *                  this allows suffixes such as \"b\" or \"dev\" but will cause \"1.10\" to be considered smaller than \"1.2\".\r\n * zeroExtend: ([true]/false) changes the result if one version string has less parts than the other. In\r\n *             this case the shorter string will be padded with \"zero\" parts instead of being considered smaller.\r\n *\r\n * @return {number|NaN}\r\n * - true if the versions are equal\r\n * - false iff v1 < v2\r\n * - true iff v1 > v2\r\n * - NaN if either version string is in the wrong format\r\n */\r\n\r\nexport function versionCompare(v1, v2, options) {\r\n\tvar lexicographical = (options && options.lexicographical) || false,\r\n\t\tzeroExtend = (options && options.zeroExtend) || true,\r\n\t\tv1parts = (v1 || \"0\").split('.'),\r\n\t\tv2parts = (v2 || \"0\").split('.');\r\n\r\n\tfunction isValidPart(x) {\r\n\t\treturn (lexicographical ? /^\\d+[A-Za-zαß]*$/ : /^\\d+[A-Za-zαß]?$/).test(x);\r\n\t}\r\n\r\n\tif (!v1parts.every(isValidPart) || !v2parts.every(isValidPart)) {\r\n\t\treturn NaN;\r\n\t}\r\n\r\n\tif (zeroExtend) {\r\n\t\twhile (v1parts.length < v2parts.length) v1parts.push(\"0\");\r\n\t\twhile (v2parts.length < v1parts.length) v2parts.push(\"0\");\r\n\t}\r\n\r\n\tif (!lexicographical) {\r\n\t\tv1parts = v1parts.map(function(x){\r\n\t\t\tvar match = (/[A-Za-zαß]/).exec(x);  \r\n\t\t\treturn Number(match ? x.replace(match[0], \".\" + x.charCodeAt(match.index)):x);\r\n\t\t});\r\n\t\tv2parts = v2parts.map(function(x){\r\n\t\t\tvar match = (/[A-Za-zαß]/).exec(x);  \r\n\t\t\treturn Number(match ? x.replace(match[0], \".\" + x.charCodeAt(match.index)):x);\r\n\t\t});\r\n\t}\r\n\r\n\tfor (var i = 0; i < v1parts.length; ++i) {\r\n\t\tif (v2parts.length === i) {\r\n\t\t\treturn true;\r\n\t\t}\r\n\r\n\t\tif (v1parts[i] === v2parts[i]) {\r\n\t\t\tcontinue;\r\n\t\t}\r\n\t\telse if (v1parts[i] > v2parts[i]) {\r\n\t\t\treturn true;\r\n\t\t}\r\n\t\telse {\r\n\t\t\treturn false;\r\n\t\t}\r\n\t}\r\n\r\n\tif (v1parts.length !== v2parts.length) {\r\n\t\treturn false;\r\n\t}\r\n\r\n\treturn true;\r\n}\r\n\r\n/**\r\n * Get the value of defined key from window object cf7svisual.\r\n * \r\n * Returned value can be the default value if it does not exist\r\n * \r\n * @param  {String}\t\tkey\t\tthe object key of window.cf7svisual\r\n * \r\n * @return {Array/String/Boolean} the value of defined key from window.cf7svisual variable\r\n * \r\n * @since 2.3.0\r\n */\r\nexport function getVisualVar( key ) {\r\n\r\n\tconst { cf7svisual } = window;\r\n\r\n\t// Return if cf7svisual is not set\r\n\tif ( typeof cf7svisual === 'undefined' || cf7svisual === null ) {\r\n\t\tconsole.log( 'window.cf7svisual does not exist!' );\r\n\t\treturn false;\r\n\t}\r\n\r\n\t// Return if key does not exist in cf7svisual\r\n\tif ( ! cf7svisual.hasOwnProperty( key ) ) {\r\n\t\treturn false;\r\n\t}\r\n\r\n\t// Set default type for type checking\r\n\tconst defaultKeyType = {\r\n\t\taddons\t\t:  {}, // object\r\n\t\tenvironment\t:  '', // string\r\n\t\toptions\t\t:  {}, // object\t\t\r\n\t\titems \t\t:  [], // array\r\n\t\tintegration\t:  {}, // array\r\n\t\ttitle \t\t:  '', // string\r\n\t\ttreeData\t:  [], // string, hold temporary treeData/items\r\n\t\tversions\t:  {}, // object\r\n\t};\r\n\r\n\t// Check it has valid type\r\n\tif ( !! defaultKeyType.hasOwnProperty( key ) ) { // exists above\r\n\t\tif ( typeof( cf7svisual[ key ] ) === typeof( defaultKeyType[ key ] ) ) { // same type\t\t\t\r\n\t\t\treturn cf7svisual[ key ];\r\n\t\t}\r\n\t}\r\n\r\n\treturn false;\r\n}\r\n\r\n/**\r\n * Development mode\r\n * \r\n * @return {Booean} true if development, false if production\r\n * \r\n * @since 2.4.2\r\n */\r\nexport function isDevelopment() {\r\n\tconst { cf7svisual } = window; // window.cf7svisual\r\n\t\r\n\treturn 'development' === cf7svisual.environment ? true : false;\r\n}\r\n", "import { cf7sItems } from './cf7sItems';\r\nimport { cf7sRequest } from './api';\r\nimport { \r\n\tdefaultTreeData,\r\n\trandomizeName,\r\n\tmergeDefault, \r\n\tversionCompare, \r\n\tsaveVisualForm,\r\n\tgetVisualVar, // @since 2.3.0\r\n\tisDevelopment,\r\n} from './functions';\r\nimport { cf7sDropRules, cf7sSurroundingRules , cf7sDuplicateRules } from './cf7sRules';\r\n\r\n// Simplify import of utils\r\nexport {\r\n\t// cf7sItems.js\r\n\tcf7sItems,\r\n\r\n\t// functions.js\r\n\tdefaultTreeData,\r\n\trandomizeName,\r\n\tmergeDefault,\r\n\tversionCompare,\r\n\tsaveVisualForm,\r\n\tgetVisualVar, // @since 2.3.0\r\n\tisDevelopment,\r\n\t\r\n\t// api.js\r\n\tcf7sRequest,\r\n\r\n\t// cf7sRules.js\r\n\tcf7sDropRules,\r\n\tcf7sSurroundingRules,\r\n\tcf7sDuplicateRules,\r\n};\r\n", "module.exports = lodash;", "module.exports = React;", "module.exports = ReactDOM;"], "mappings": ";AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;ACl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g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rZA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AAAA;AAAA;AACA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AAAA;AACA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AAEA;AAAA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAEA;AAAA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AAAA;AACA;AAMA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AACA;AAAA;AACA;AACA;AAAA;AACA;AACA;AACA;AAEA;AACA;AAAA;AACA;AACA;AACA;AACA;AAEA;AAAA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AAEA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AAAA;AACA;AACA;AAEA;AACA;AACA;AAAA;AACA;AACA;AACA;AAVA;AAAA;AAAA;AAYA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AAAA;AACA;AAAA;AACA;AAAA;AACA;AAAA;AACA;AAAA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AAAA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;;;;;;;;;;;;AC5YA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAQA;AAEA;AACA;AACA;;;;;;;;;;;;ACbA;;;;;;;;;;;ACAA;;;;;;;;;;;ACAA;;;;A", "sourceRoot": ""}