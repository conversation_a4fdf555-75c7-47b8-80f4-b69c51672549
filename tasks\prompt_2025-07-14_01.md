# PROMPT

Please provide a comprehensive overview of the CF7 Skins Visual codebase based on the retrieved files. Include:

1. The overall architecture and purpose of the project
2. The main components and their relationships
3. How the React application integrates with WordPress
4. The role of the different add-ons (Logic, Multi, Pro, Ready)
5. The build system and development workflow
6. How data flows through the application

Please organize your explanation in a structured way with clear headings and focus on the most important aspects of the codebase.

# CF7 Skins Visual Codebase Overview

1. Project Purpose and Architecture

CF7 Skins Visual is a React-based visual editor for Contact Form 7 (CF7), a popular WordPress form plugin. The project provides a user-friendly interface for creating and editing forms, with a modular architecture that supports various add-ons.

The codebase is organized into:

Core Visual Editor (src/visual)
Standalone WordPress plugins (dev, lite, pro)
Add-ons (Logic, Ready, etc.)
Documentation and utilities

## 2. Main Components and Relationships
The component hierarchy follows:

```
Visual (Main Controller)
├── Form (Main container for Visual Editor)
│   ├── Edit
│   ├── Items
│   └── Select
├── AddOns (Container for add-on content)
│   ├── Ready
│   ├── Logic
│   ├── Multi
│   └── Other
└── Utility Functions
```

The Visual component (visual/index.js) serves as the main controller, managing state and event functions that are passed down to child components. It handles data exchange between WordPress and React, including parsing data and AJAX calls.

## 3. WordPress Integration

The integration with WordPress happens through:

Mounting Point: WordPress prints a tab-visual section into the DOM where React renders the Visual components.
Data Exchange: Initial data is delivered via a global CF7SkinsVisual object before React mounts.
AJAX Communication: The Visual component makes AJAX requests to fetch Pro features and add-ons that a user has purchased.
Window API: The codebase exposes functionality through window.cf7svisual for integration with WordPress.

## 4. Add-ons System

CF7 Skins supports several add-ons that extend functionality:

Ready: Provides pre-defined styles (box, column, grid, singleline, horizontal)
Logic: Adds conditional logic to show/hide fields based on user selections
Pro: Extends the core functionality with premium features
Multi: (Mentioned but details not provided in the retrieved files)

The add-ons system uses a hook-based architecture similar to Gutenberg, allowing the lite version to be extended without including disabled code (to comply with wp.org requirements).

## 5. Build System and Development

The project appears to use webpack for bundling:

webpack.config.prod.js is mentioned for production builds
The codebase separates development source ( src/visual) from deployable plugins
Window exports are handled separately to expose global functions

-ons are designed to be placed in their respective directories and should use Visual hooks like edit, menu, etc. The documentation advises against importing external modules for non-bundled projects.

## 6. Data Flow

Data flows through the application as follows:

Initial Load: WordPress provides initial data via the global CF7SkinsVisual object
State Management: The Visual component maintains state and passes it down to child components
User Interactions: Child components send state updates back to the Visual component
AJAX Communication: The api.js utility handles AJAX functions for saving data and selecting templates
Add-on Integration: Add-ons update their props using functions from the Visual component
Hooks System: The codebase provides hooks like cf7svisual.cf7sRules and cf7svisual.postData for extending functionality

The Visual component saves form data using a tree structure, as seen in the visual.json files, which represents the form's structure and properties.

## Key Features

Visual form editor with drag-and-drop functionality (React Sortable Tree)
Template and style selection
Grid-based layouts
Conditional logic through the Logic add-on
Pre-defined styles through the Ready add-on
Extensible architecture through hooks and add-ons

This architecture allows CF7 Skins to provide a user-friendly interface for creating Contact Form 7 forms while maintaining flexibility through its add-on system.

