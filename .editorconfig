# WordPress Coding Standards for CF7 Skins Project
# This file prevents inheritance from parent directories and ensures
# consistent formatting across all editors and IDEs

root = true

# Default settings for all files
[*]
charset = utf-8
end_of_line = lf
insert_final_newline = true
trim_trailing_whitespace = true

# PHP files - WordPress coding standards
[*.php]
indent_style = tab
indent_size = 4

# JavaScript files - WordPress coding standards
[*.js]
indent_style = tab
indent_size = 4

# CSS/SCSS files - WordPress coding standards
[*.{css,scss,sass}]
indent_style = tab
indent_size = 4

# JSON and YAML files - use spaces for better compatibility
[*.{json,yml,yaml}]
indent_style = space
indent_size = 2

# Markdown files
[*.md]
indent_style = space
indent_size = 2
trim_trailing_whitespace = false

# Configuration files
[*.{ini,conf}]
indent_style = space
indent_size = 2
