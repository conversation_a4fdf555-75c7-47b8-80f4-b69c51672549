!function(e){var t={};function n(r){if(t[r])return t[r].exports;var o=t[r]={i:r,l:!1,exports:{}};return e[r].call(o.exports,o,o.exports,n),o.l=!0,o.exports}n.m=e,n.c=t,n.d=function(e,t,r){n.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:r})},n.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},n.t=function(e,t){if(1&t&&(e=n(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var r=Object.create(null);if(n.r(r),Object.defineProperty(r,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var o in e)n.d(r,o,function(t){return e[t]}.bind(null,o));return r},n.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return n.d(t,"a",t),t},n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},n.p="",n(n.s=371)}([function(e,t){e.exports=React},function(e,t,n){var r=n(23);e.exports=function(e,t,n){return(t=r(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e},e.exports.__esModule=!0,e.exports.default=e.exports},function(e,t,n){e.exports=n(113)()},function(e,t){e.exports=function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e},e.exports.__esModule=!0,e.exports.default=e.exports},function(e,t){e.exports=function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")},e.exports.__esModule=!0,e.exports.default=e.exports},function(e,t,n){var r=n(23);function o(e,t){for(var n=0;n<t.length;n++){var o=t[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,r(o.key),o)}}e.exports=function(e,t,n){return t&&o(e.prototype,t),n&&o(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e},e.exports.__esModule=!0,e.exports.default=e.exports},function(e,t){function n(t){return e.exports=n=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},e.exports.__esModule=!0,e.exports.default=e.exports,n(t)}e.exports=n,e.exports.__esModule=!0,e.exports.default=e.exports},function(e,t,n){var r=n(57);e.exports=function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&r(e,t)},e.exports.__esModule=!0,e.exports.default=e.exports},function(e,t,n){var r=n(16).default,o=n(3);e.exports=function(e,t){if(t&&("object"==r(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return o(e)},e.exports.__esModule=!0,e.exports.default=e.exports},function(e,t){e.exports=lodash},function(e,t,n){"use strict";t.a={Edit:"Edit___DsDI3",attribute:"attribute___ouspy",row:"row___I0yGU",label:"label___mfZTQ",value:"value___Z--gA",control:"control___+YNEW",default:"default___cYs1M",range:"range___wsPe8",option:"option___+t59z",description:"description___CvqdB",EditTypeSelect:"EditTypeSelect___rae3d",visualData:"visualData___oRYfW",allowedTags:"allowedTags___4kz5C"}},function(e,t,n){"use strict";n.d(t,"a",(function(){return x})),n.d(t,"f",(function(){return C})),n.d(t,"e",(function(){return O})),n.d(t,"d",(function(){return T})),n.d(t,"g",(function(){return I})),n.d(t,"b",(function(){return D})),n.d(t,"c",(function(){return k}));var r=n(16),o=n.n(r),i=n(1),a=n.n(i),l=n(0),s=n.n(l),c=n(13),u=n.n(c),d=(n(9),n(12)),f=n(42);function h(e,t){var n="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!n){if(Array.isArray(e)||(n=function(e,t){if(e){if("string"==typeof e)return p(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?p(e,t):void 0}}(e))||t&&e&&"number"==typeof e.length){n&&(e=n);var _n=0,r=function(){};return{s:r,n:function(){return _n>=e.length?{done:!0}:{done:!1,value:e[_n++]}},e:function(e){throw e},f:r}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,i=!0,a=!1;return{s:function(){n=n.call(e)},n:function(){var e=n.next();return i=e.done,e},e:function(e){a=!0,o=e},f:function(){try{i||null==n.return||n.return()}finally{if(a)throw o}}}}function p(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=Array(t);n<t;n++)r[n]=e[n];return r}function g(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function v(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?g(Object(n),!0).forEach((function(t){a()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):g(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var m=v({},window.wp.i18n),y=m.sprintf,__=m.__,_=wp.hooks.applyFilters,b=v({},window.wp.data),w=b.dispatch,S=b.select;function x(){var e,t=[],n=[],r=h(d.cf7sItems);try{for(r.s();!(e=r.n()).done;){var o=e.value;t[o.cf7sType]=O(o)}}catch(e){r.e(e)}finally{r.f()}return t.fieldset.expanded=!0,t.fieldset.cf7sLabel=__("Legend","contact-form-7-skins"),n.push(t.fieldset),t["list-ol"].expanded=!0,n[0].children=[v({},t["list-ol"])],t.text.cf7sLabel=__("Your Name (required)","contact-form-7-skins"),t.text.cf7Required=!0,t["list-li"].expanded=!0,t["list-li"].children=[v({},t.text)],n[0].children[0].children=[v({},t["list-li"])],t.email.cf7sLabel=__("Email Address (required)","contact-form-7-skins"),t.email.cf7Required=!0,t["list-li"]=O(t["list-li"]),t["list-li"].children=[v({},t.email)],n[0].children[0].children[1]=v({},t["list-li"]),t.textarea.cf7sLabel=__("Your Message","contact-form-7-skins"),t["list-li"]=O(t["list-li"]),t["list-li"].children=[v({},t.textarea)],n[0].children[0].children[2]=v({},t["list-li"]),t.paragraph.cf7sContent=__("* Required","contact-form-7-skins"),n[0].children[1]=v({},t.paragraph),t.submit.cf7sLabel=__("Send","contact-form-7-skins"),n.push(t.submit),n}function C(){var e=w("cf7svisual").setNotice,t=v({},S("cf7svisual").getStates()),n=window.cf7svisual.treeData||t.treeData.slice(),r=window.cf7svisual;if("development"===r.environment&&(console.log("on save:",n),console.log(JSON.stringify(n))),r&&document.getElementById("post_ID")){var i=document.getElementById("post_ID").getAttribute("value"),a=document.getElementById("wpcf7-admin-form-element"),l=document.createElement("input");if(!i||i<0)l.setAttribute("type","hidden"),l.setAttribute("name","cf7s-visual"),l.setAttribute("value",JSON.stringify(n)),a.appendChild(l),a.submit();else{var c=_("cf7svisual.postData",{action:"cf7skins_visual_update",form_id:i,visual:JSON.stringify(n),template:document.getElementById("cf7s-template").value,style:document.getElementById("cf7s-style").value}),d=document.getElementById("title").value;r.title!==d&&(c.title=d),Object(f.a)(c,"json").then((function(t){window.cf7sAdmin.getTextarea().value=t.form,window.onbeforeunload=null,"function"==typeof Event?new Event("change",{bubbles:!0}):document.createEvent("Event").initEvent("change",!0,!0),window.cf7sAdmin.getTextarea().dispatchEvent(new Event("change",{bubbles:!0}));var n=s.a.createElement("div",{className:"notice notice-success is-dismissible"},s.a.createElement("p",null,"Visual saved!"),s.a.createElement("button",{type:"button",className:"notice-dismiss",onClick:function(){return u.a.unmountComponentAtNode(document.getElementById("cf7s-visual-notice"))}},s.a.createElement("span",{className:"screen-reader-text"},__("Dismiss this notice.","contact-form-7-skins"))));if(u.a.render(n,document.getElementById("cf7s-visual-notice")),e(__("Visual saved!","contact-form-7-skins"),"success"),t.callbacks){var r,i=h(t.callbacks);try{for(i.s();!(r=i.n()).done;){var a=r.value;if("object"===o()(a)){var l=Object.keys(a)[0];if(void 0===window[l]){console.warn(y(__("Namespace %s is undefined!","contact-form-7-skins"),l));continue}var c=a[l];void 0!==window[l][c]?window[l][c].apply():console.warn(y(__("Function %1$s.%2$s is undefined!","contact-form-7-skins"),l,"".concat(c,"()")))}else void 0!==window[a]?window[a].apply():console.warn(y(__("Function %s is undefined!","contact-form-7-skins"),"".concat(a,"()")))}}catch(e){i.e(e)}finally{i.f()}}})).catch((function(e){return console.error(e)}))}}else e(__("Can not save! window.cf7svisual or post ID does not exist.","contact-form-7-skins"),"error")}function O(e){var t=Math.floor(899*Math.random()+100);if(e.cf7Name="".concat(e.cf7sType,"-").concat(t),e.children){var n,r=h(e.children);try{for(r.s();!(n=r.n()).done;)O(n.value)}catch(e){r.e(e)}finally{r.f()}}return e}function T(e){var t=function(t){var n=d.cf7sItems.filter((function(n){return n.cf7sType===e[t].cf7sType}))[0];e[t]=v(v({},n),e[t]),e[t].children&&T(e[t].children)};for(var n in e)t(n);return e}function I(e,t,n){var r=n&&n.lexicographical||!1,o=n&&n.zeroExtend||!0,i=(e||"0").split("."),a=(t||"0").split(".");function l(e){return(r?/^\d+[A-Za-zαß]*$/:/^\d+[A-Za-zαß]?$/).test(e)}if(!i.every(l)||!a.every(l))return NaN;if(o){for(;i.length<a.length;)i.push("0");for(;a.length<i.length;)a.push("0")}r||(i=i.map((function(e){var t=/[A-Za-zαß]/.exec(e);return Number(t?e.replace(t[0],"."+e.charCodeAt(t.index)):e)})),a=a.map((function(e){var t=/[A-Za-zαß]/.exec(e);return Number(t?e.replace(t[0],"."+e.charCodeAt(t.index)):e)})));for(var s=0;s<i.length;++s){if(a.length===s)return!0;if(i[s]!==a[s])return i[s]>a[s]}return i.length===a.length}function D(e){var t=window.cf7svisual;if(null==t)return console.log("window.cf7svisual does not exist!"),!1;if(!t.hasOwnProperty(e))return!1;var n={addons:{},environment:"",options:{},items:[],integration:{},title:"",treeData:[],versions:{}};return!(!n.hasOwnProperty(e)||o()(t[e])!==o()(n[e]))&&t[e]}function k(){return"development"===window.cf7svisual.environment}},function(e,t,n){"use strict";n.r(t),n.d(t,"cf7sItems",(function(){return c})),n.d(t,"defaultTreeData",(function(){return i.a})),n.d(t,"randomizeName",(function(){return i.e})),n.d(t,"mergeDefault",(function(){return i.d})),n.d(t,"versionCompare",(function(){return i.g})),n.d(t,"saveVisualForm",(function(){return i.f})),n.d(t,"getVisualVar",(function(){return i.b})),n.d(t,"isDevelopment",(function(){return i.c})),n.d(t,"cf7sRequest",(function(){return u.a})),n.d(t,"cf7sDropRules",(function(){return x})),n.d(t,"cf7sSurroundingRules",(function(){return C})),n.d(t,"cf7sDuplicateRules",(function(){return O}));var r=n(1),o=n.n(r),i=n(11);function a(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}var __=function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?a(Object(n),!0).forEach((function(t){o()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):a(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}({},window.wp.i18n).__,l=wp.hooks.applyFilters,s=[{cf7sType:"acceptance",cf7sSelectLabel:__("Acceptance (confirm)","contact-form-7-skins"),cf7sSelectGroup:"cf7Tag",cf7Name:"acceptance",cf7sLabel:"",cf7Required:!1,cf7TagOptions:[{cf7Option:"default_on",isChecked:!1,optionLabel:__("Make this checkbox checked by default","contact-form-7-skins"),optionType:"checkbox"},{cf7Option:"invert",isChecked:!1,optionLabel:__("Make this work inversely","contact-form-7-skins"),optionType:"checkbox"},{cf7Option:"optional",isChecked:!0,optionLabel:__("Make this checkbox optional","contact-form-7-skins"),optionType:"checkbox"}],cf7DefaultOn:!1,cf7Invert:!1,cf7Optional:!0,cf7Content:"",cf7IdAttribute:"",cf7ClassAttribute:"",cf7sIcon:"yes-alt",noChildren:!0},{cf7sType:"checkbox",cf7sSelectLabel:__("Checkbox (option)","contact-form-7-skins"),cf7sSelectGroup:"cf7Tag",cf7Name:"checkbox",cf7sLabel:"",cf7Required:!1,cf7TagOptions:[{cf7Option:"label_first",isChecked:!1,optionLabel:__("Put a label first, a checkbox last","contact-form-7-skins"),optionType:"checkbox"},{cf7Option:"use_label_element",isChecked:!1,optionLabel:__("Wrap each item with label element","contact-form-7-skins"),optionType:"checkbox"},{cf7Option:"exclusive",isChecked:!1,optionLabel:__("Make checkboxes exclusive","contact-form-7-skins"),optionType:"checkbox"}],cf7Options:[{value:"Option 1",isChecked:!0},{value:"Option 2",isChecked:!1}],cf7LabelFirst:!1,cf7UseLabelElement:!1,cf7Exclusive:!1,cf7IdAttribute:"",cf7ClassAttribute:"",cf7sIcon:"forms",noChildren:!0},{cf7sType:"date",cf7sSelectLabel:__("Date","contact-form-7-skins"),cf7sSelectGroup:"cf7Tag",cf7Name:"date",cf7sLabel:"",cf7Required:!1,cf7TagOptions:[{cf7Option:"",optionLabel:__("Default value","contact-form-7-skins"),optionType:"input"},{cf7Option:"placeholder",isChecked:!1,optionLabel:__("Use this text as the placeholder of the field","contact-form-7-skins"),optionType:"checkbox"},{cf7Option:"",optionLabel:__("Range - min","contact-form-7-skins"),optionType:"input"},{cf7Option:"",optionLabel:__("Range - max","contact-form-7-skins"),optionType:"input"}],cf7Values:"",cf7Placeholder:!1,cf7Min:"",cf7Max:"",cf7IdAttribute:"",cf7ClassAttribute:"",cf7sIcon:"calendar",noChildren:!0},{cf7sType:"email",cf7sSelectLabel:__("Email","contact-form-7-skins"),cf7sSelectGroup:"cf7Tag",cf7Name:"email",cf7sLabel:"",cf7Required:!1,cf7TagOptions:[{cf7Option:"",optionLabel:"Default value",optionType:"input"},{cf7Option:"placeholder",isChecked:!1,optionLabel:__("Use this text as the placeholder of the field","contact-form-7-skins"),optionType:"checkbox"},{cf7Option:"akismet_author_email",isChecked:!1,optionLabel:__("Akismet - this field requires author's email address","contact-form-7-skins"),optionType:"checkbox"}],cf7Values:"",cf7Placeholder:!1,cf7AkismetAuthorEmail:!1,cf7IdAttribute:"",cf7ClassAttribute:"",cf7sIcon:"email-alt",noChildren:!0},{cf7sType:"file",cf7sSelectLabel:__("File (upload)","contact-form-7-skins"),cf7sSelectGroup:"cf7Tag",cf7Name:"file",cf7sLabel:"",cf7Required:!1,cf7TagOptions:[{cf7Option:"limit",optionLabel:__("File size limit (bytes)","contact-form-7-skins"),optionType:"input"},{cf7Option:"",optionLabel:__("Acceptable file types","contact-form-7-skins"),optionType:"select"}],cf7Limit:"",cf7FileTypes:"",cf7IdAttribute:"",cf7ClassAttribute:"",cf7sIcon:"paperclip",noChildren:!0},{cf7sType:"number",cf7sSelectLabel:__("Number","contact-form-7-skins"),cf7sSelectGroup:"cf7Tag",cf7Name:"number",cf7sLabel:"",cf7Required:!1,cf7TagOptions:[{cf7Option:"",optionLabel:"Field Type",optionType:"select"},{cf7Option:"",optionLabel:__("Default value","contact-form-7-skins"),optionType:"input"},{cf7Option:"placeholder",isChecked:!1,optionLabel:__("Use this text as the placeholder of the field","contact-form-7-skins"),optionType:"checkbox"},{cf7Option:"",optionLabel:__("Range - min","contact-form-7-skins"),optionType:"input"},{cf7Option:"",optionLabel:__("Range - max","contact-form-7-skins"),optionType:"input"}],cf7TagType:"number",cf7Values:"",cf7Placeholder:"",cf7Min:"",cf7Max:"",cf7IdAttribute:"",cf7ClassAttribute:"",cf7sIcon:"plus-alt2",noChildren:!0},{cf7sType:"quiz",cf7sSelectLabel:__("Quiz","contact-form-7-skins"),cf7sSelectGroup:"cf7Tag",cf7Name:"quiz",cf7sLabel:"",cf7Required:!1,cf7Options:[{question:__("Question 1","contact-form-7-skins"),answer:__("Answer 1","contact-form-7-skins")},{question:__("Question 2","contact-form-7-skins"),answer:__("Answer 2","contact-form-7-skins")}],cf7IdAttribute:"",cf7ClassAttribute:"",cf7sIcon:"editor-help",noChildren:!0},{cf7sType:"radio",cf7sSelectLabel:__("Radio Button (option)","contact-form-7-skins"),cf7sSelectGroup:"cf7Tag",cf7Name:"radio",cf7sLabel:"",cf7Required:!1,cf7TagOptions:[{cf7Option:"label_first",isChecked:!1,optionLabel:__("Put a label first, a checkbox last","contact-form-7-skins"),optionType:"checkbox"},{cf7Option:"use_label_element",isChecked:!1,optionLabel:__("Wrap each item with label element","contact-form-7-skins"),optionType:"checkbox"}],cf7Options:[{value:__("Option 1","contact-form-7-skins"),isChecked:!0},{value:__("Option 2","contact-form-7-skins"),isChecked:!1}],cf7LabelFirst:!1,cf7UseLabelElement:!1,cf7IdAttribute:"",cf7ClassAttribute:"",cf7sIcon:"marker",noChildren:!0},{cf7sType:"select",cf7sSelectLabel:__("Select (dropdown)","contact-form-7-skins"),cf7sSelectGroup:"cf7Tag",cf7Name:"select",cf7sLabel:"",cf7Required:!1,cf7TagOptions:[{cf7Option:"",isChecked:!1,optionLabel:__("Allow multiple selections","contact-form-7-skins"),optionType:"checkbox"},{cf7Option:"",isChecked:!1,optionLabel:__("Insert a blank item as the first option","contact-form-7-skins"),optionType:"checkbox"}],cf7Options:[{value:__("Option 1","contact-form-7-skins"),isChecked:!0},{value:__("Option 2","contact-form-7-skins"),isChecked:!1}],cf7Multiple:!1,cf7IncludeBlank:!1,cf7IdAttribute:"",cf7ClassAttribute:"",cf7sIcon:"list-view",noChildren:!0},{cf7sType:"submit",cf7sSelectLabel:__("Submit","contact-form-7-skins"),cf7sSelectGroup:"cf7Tag",cf7Name:"submit",cf7sLabel:"Submit",cf7Values:"",cf7Required:!1,cf7IdAttribute:"",cf7ClassAttribute:"",cf7sIcon:"button",noChildren:!0},{cf7sType:"tel",cf7sSelectLabel:__("Telephone","contact-form-7-skins"),cf7sSelectGroup:"cf7Tag",cf7Name:"tel",cf7sLabel:"",cf7Required:!1,cf7TagOptions:[{cf7Option:"",optionLabel:__("Default value","contact-form-7-skins"),optionType:"input"},{cf7Option:"placeholder",isChecked:!1,optionLabel:__("Use this text as the placeholder of the field","contact-form-7-skins"),optionType:"checkbox"}],cf7Values:"",cf7Placeholder:!1,cf7IdAttribute:"",cf7ClassAttribute:"",cf7sIcon:"phone",noChildren:!0},{cf7sType:"text",cf7sSelectLabel:__("Text (short text)","contact-form-7-skins"),cf7sSelectGroup:"cf7Tag",cf7Name:"text",cf7sLabel:"",cf7Required:!1,cf7TagOptions:[{cf7Option:"",optionLabel:__("Default value","contact-form-7-skins"),optionType:"input"},{cf7Option:"placeholder",isChecked:!1,optionLabel:__("Use this text as the placeholder of the field","contact-form-7-skins"),optionType:"checkbox"},{cf7Option:"akismet_author_email",isChecked:!1,optionLabel:__("Akismet - this field requires author's email address","contact-form-7-skins"),optionType:"checkbox"}],cf7Values:"",cf7Placeholder:!1,cf7AkismetAuthor:!1,cf7IdAttribute:"",cf7ClassAttribute:"",cf7sIcon:"editor-textcolor",noChildren:!0},{cf7sType:"textarea",cf7sSelectLabel:__("Textarea (long text)","contact-form-7-skins"),cf7sSelectGroup:"cf7Tag",cf7Name:"textarea",cf7sLabel:"",cf7Required:!1,cf7TagOptions:[{cf7Option:"",optionLabel:__("Default value","contact-form-7-skins"),optionType:"input"},{cf7Option:"placeholder",isChecked:!1,optionLabel:__("Use this text as the placeholder of the field","contact-form-7-skins"),optionType:"checkbox"}],cf7Values:"",cf7Placeholder:!1,cf7IdAttribute:"",cf7ClassAttribute:"",cf7sIcon:"format-aside",noChildren:!0},{cf7sType:"url",cf7sSelectLabel:__("URL (website link)","contact-form-7-skins"),cf7sSelectGroup:"cf7Tag",cf7Name:"url",cf7sLabel:"",cf7Required:!1,cf7TagOptions:[{cf7Option:"",optionLabel:__("Default value","contact-form-7-skins"),optionType:"input"},{cf7Option:"placeholder",isChecked:!1,optionLabel:__("Use this text as the placeholder of the field","contact-form-7-skins"),optionType:"checkbox"},{cf7Option:"akismet_author_email",isChecked:!1,optionLabel:__("Akismet - this field requires author's email address","contact-form-7-skins"),optionType:"checkbox"}],cf7Values:"",cf7Placeholder:!1,cf7AkismetAuthorUrl:!1,cf7IdAttribute:"",cf7ClassAttribute:"",cf7sIcon:"admin-links",noChildren:!0},{cf7sType:"recaptcha",cf7sSelectLabel:"reCAPTCHA",cf7sSelectGroup:"cf7Tag",cf7Name:"recaptcha",cf7sLabel:"",cf7IdAttribute:"",cf7Size:"",cf7Theme:"",cf7ClassAttribute:"",cf7sIcon:"update",noChildren:!0},{cf7sType:"fieldset",cf7sSelectLabel:__("Fieldset (with legend)","contact-form-7-skins"),cf7sSelectGroup:"cf7sItem",cf7Name:"fieldset",cf7sLabel:__("Legend ..","contact-form-7-skins"),cf7sIcon:"category"},{cf7sType:"list-ol",cf7sSelectLabel:__("List - ol","contact-form-7-skins"),cf7sSelectGroup:"cf7sItem",cf7Name:"listol",cf7sLabel:"",cf7sIcon:"editor-ol",noChildren:!1},{cf7sType:"list-li",cf7sSelectLabel:__("List Item - li","contact-form-7-skins"),cf7sSelectGroup:"cf7sItem",cf7Name:"listli",cf7sLabel:"",cf7sIcon:"editor-ul",noChildren:!1},{cf7sType:"paragraph",cf7sSelectLabel:__("Paragraph - p","contact-form-7-skins"),cf7sSelectGroup:"cf7sItem",cf7Name:"paragraph",cf7sContent:"",cf7sIcon:"editor-paragraph",noChildren:!0}],c=l("cf7sItems",[].concat(s)),u=n(42),d=n(9);function f(e,t){var n="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!n){if(Array.isArray(e)||(n=function(e,t){if(e){if("string"==typeof e)return h(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?h(e,t):void 0}}(e))||t&&e&&"number"==typeof e.length){n&&(e=n);var _n=0,r=function(){};return{s:r,n:function(){return _n>=e.length?{done:!0}:{done:!1,value:e[_n++]}},e:function(e){throw e},f:r}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,i=!0,a=!1;return{s:function(){n=n.call(e)},n:function(){var e=n.next();return i=e.done,e},e:function(e){a=!0,o=e},f:function(){try{i||null==n.return||n.return()}finally{if(a)throw o}}}}function h(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=Array(t);n<t;n++)r[n]=e[n];return r}function p(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function g(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?p(Object(n),!0).forEach((function(t){o()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):p(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var v=g({},window.wp.i18n),m=v.sprintf,y=v.__,_=wp.hooks.applyFilters,b=Object(d.cloneDeep)(c).filter((function(e){return"list-ol"===e.cf7sType}))[0],w=Object(d.cloneDeep)(c).filter((function(e){return"list-li"===e.cf7sType}))[0];function S(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"drop",t={},n=_("cf7svisual.cf7sRules",[]);if(n)for(var r=0;r<n.length;++r)for(var o in n[r])t[o]||(t[o]=[]),t[o].push(n[r][o]);if(t&&t[e])return t[e]}function x(e){var t=g({},e),n=t.node,r=t.prevTreeIndex,o=t.nextParent,a=Object(i.b)("treeData")||e.treeData;if(o&&"list-li"===o.cf7sType&&o.children){var l,s=0,u=f(o.children);try{for(u.s();!(l=u.n()).done;){var d,h=l.value,p=f(c);try{for(p.s();!(d=p.n()).done;){var v=d.value;h.cf7sType===v.cf7sType&&"cf7Tag"===v.cf7sSelectGroup&&s++}}catch(e){p.e(e)}finally{p.f()}}}catch(e){u.e(e)}finally{u.f()}if(s>1)return console.warn("Only one CF7 tag for each list"),!1}if(n&&null==r&&"recaptcha"===n.cf7sType&&T(n.cf7sType,a)>0&&null==r)return console.warn(y("Only one reCAPTCHA per form allowed.","contact-form-7-skins")),!1;if(n&&null==r&&"submit"===n.cf7sType&&T(n.cf7sType,a)>0)return console.warn(y("Only one submit for each form.","contact-form-7-skins")),!1;if(n.cf7sRoot&&o)return console.warn(y("Root node can not be dragged to other node.","contact-form-7-skins")),!1;var m=S("drop");if(m&&m.length)for(var _=0;_<m.length;++_)if(!m[_](e))return!1;return!o||!o.noChildren}function C(e,t){if("list-ol"===e.cf7sType&&!e.children){var n=Object(i.e)(Object(d.cloneDeep)(w));n.children=[],e.children=[g({},n)],e.expanded=!0}if(!(t&&"fieldset"!==t.cf7sType||e.children||"fieldset"!==e.cf7sType)){var r=Object(i.e)(Object(d.cloneDeep)(b)),o=Object(i.e)(Object(d.cloneDeep)(w));return r.children=[g({},o)],r.expanded=!0,e.children=[g({},r)],e.expanded=!0,e}if((!t||"fieldset"===t.cf7sType||!1===t.noChildren&&["list-ol","list-li"].indexOf(t.cf7sType)<0)&&"cf7Tag"===e.cf7sSelectGroup&&["recaptcha","submit"].indexOf(e.cf7sType)<0){var a=Object(i.e)(Object(d.cloneDeep)(b)),l=Object(i.e)(Object(d.cloneDeep)(w));return l.children=[g({},e)],l.expanded=!0,a.children=[g({},l)],a.expanded=!0,a}if(t&&"list-ol"===t.cf7sType&&"list-li"!==e.cf7sType){var s=Object(i.e)(Object(d.cloneDeep)(w));return s.expanded=!0,s.children=[g({},e)],s}if("list-li"===e.cf7sType&&null===t||"list-li"===e.cf7sType&&"list-ol"!==t.cf7sType){var c=Object(i.e)(Object(d.cloneDeep)(b));return c.children=[g({},e)],c.expanded=!0,c}var u=S("surround");if(u&&u.length)for(var f=0;f<u.length;++f)e=u[f]({node:e,parentNode:t,nodeOL:b,nodeLI:w,randomizeName:i.e});return e}function O(e){var t=g({},e),n=t.node,r=t.parentNode;if("recaptcha"===n.cf7sType||"submit"===n.cf7sType)return m(y("Only one %s allowed in a form.","contact-form-7-skins"),n.cf7sType);if(I(n,"recaptcha"))return y("Node has recaptcha children. Only one recaptcha allowed in a form.","contact-form-7-skins");if(I(n,"submit"))return y("Node has submit children. Only one submit allowed in a form.","contact-form-7-skins");if(r&&"list-li"===r.cf7sType&&"cf7Tag"===n.cf7sSelectGroup)return"surrounding";var o=S("duplicate");if(o&&o.length)for(var i=0;i<o.length;++i)o[i](e);return!0}function T(e,t){var n=0;return function t(r){var o,i=f(r);try{for(i.s();!(o=i.n()).done;){var a=o.value;e===a.cf7sType&&n++,a.children&&t(a.children)}}catch(e){i.e(e)}finally{i.f()}}(t),n}function I(e,t){if(!e.children)return!1;var n,r=f(e.children);try{for(r.s();!(n=r.n()).done;){var o=n.value;return t===o.cf7sType||I(o,t)}}catch(e){r.e(e)}finally{r.f()}}},function(e,t){e.exports=ReactDOM},function(e,t,n){"use strict";e.exports=function(e,t,n,r,o,i,a,l){if(!e){var s;if(void 0===t)s=new Error("Minified exception occurred; use the non-minified dev environment for the full error message and additional helpful warnings.");else{var c=[n,r,o,i,a,l],u=0;(s=new Error(t.replace(/%s/g,(function(){return c[u++]})))).name="Invariant Violation"}throw s.framesToPop=1,s}}},function(e,t){function n(){return e.exports=n=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)({}).hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},e.exports.__esModule=!0,e.exports.default=e.exports,n.apply(null,arguments)}e.exports=n,e.exports.__esModule=!0,e.exports.default=e.exports},function(e,t){function n(t){return e.exports=n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},e.exports.__esModule=!0,e.exports.default=e.exports,n(t)}e.exports=n,e.exports.__esModule=!0,e.exports.default=e.exports},,function(e,t,n){var r=n(109),o=n(110),i=n(64),a=n(111);e.exports=function(e){return r(e)||o(e)||i(e)||a()},e.exports.__esModule=!0,e.exports.default=e.exports},function(e,t,n){var r=n(34),o=n(119),i=n(26),a=Function.prototype,l=Object.prototype,s=a.toString,c=l.hasOwnProperty,u=s.call(Object);e.exports=function(e){if(!i(e)||"[object Object]"!=r(e))return!1;var t=o(e);if(null===t)return!0;var n=c.call(t,"constructor")&&t.constructor;return"function"==typeof n&&n instanceof n&&s.call(n)==u}},function(e,t){var n=Array.isArray;e.exports=n},function(e,t,n){"use strict";function r(){var e=this.constructor.getDerivedStateFromProps(this.props,this.state);null!=e&&this.setState(e)}function o(e){this.setState(function(t){var n=this.constructor.getDerivedStateFromProps(e,t);return null!=n?n:null}.bind(this))}function i(e,t){try{var n=this.props,r=this.state;this.props=e,this.state=t,this.__reactInternalSnapshotFlag=!0,this.__reactInternalSnapshot=this.getSnapshotBeforeUpdate(n,r)}finally{this.props=n,this.state=r}}function a(e){var t=e.prototype;if(!t||!t.isReactComponent)throw new Error("Can only polyfill class components");if("function"!=typeof e.getDerivedStateFromProps&&"function"!=typeof t.getSnapshotBeforeUpdate)return e;var n=null,a=null,l=null;if("function"==typeof t.componentWillMount?n="componentWillMount":"function"==typeof t.UNSAFE_componentWillMount&&(n="UNSAFE_componentWillMount"),"function"==typeof t.componentWillReceiveProps?a="componentWillReceiveProps":"function"==typeof t.UNSAFE_componentWillReceiveProps&&(a="UNSAFE_componentWillReceiveProps"),"function"==typeof t.componentWillUpdate?l="componentWillUpdate":"function"==typeof t.UNSAFE_componentWillUpdate&&(l="UNSAFE_componentWillUpdate"),null!==n||null!==a||null!==l){var s=e.displayName||e.name,c="function"==typeof e.getDerivedStateFromProps?"getDerivedStateFromProps()":"getSnapshotBeforeUpdate()";throw Error("Unsafe legacy lifecycles will not be called for components using new component APIs.\n\n"+s+" uses "+c+" but also contains the following legacy lifecycles:"+(null!==n?"\n  "+n:"")+(null!==a?"\n  "+a:"")+(null!==l?"\n  "+l:"")+"\n\nThe above lifecycles should be removed. Learn more about this warning here:\nhttps://fb.me/react-async-component-lifecycle-hooks")}if("function"==typeof e.getDerivedStateFromProps&&(t.componentWillMount=r,t.componentWillReceiveProps=o),"function"==typeof t.getSnapshotBeforeUpdate){if("function"!=typeof t.componentDidUpdate)throw new Error("Cannot polyfill getSnapshotBeforeUpdate() for components that do not define componentDidUpdate() on the prototype");t.componentWillUpdate=i;var u=t.componentDidUpdate;t.componentDidUpdate=function(e,t,n){var r=this.__reactInternalSnapshotFlag?this.__reactInternalSnapshot:n;u.call(this,e,t,r)}}return e}n.r(t),n.d(t,"polyfill",(function(){return a})),r.__suppressDeprecationWarning=!0,o.__suppressDeprecationWarning=!0,i.__suppressDeprecationWarning=!0},function(e,t){var n;n=function(){return this}();try{n=n||new Function("return this")()}catch(e){"object"==typeof window&&(n=window)}e.exports=n},function(e,t,n){var r=n(16).default,o=n(43);e.exports=function(e){var t=o(e,"string");return"symbol"==r(t)?t:t+""},e.exports.__esModule=!0,e.exports.default=e.exports},function(e,t,n){var r=n(204),o=n(205),i=n(64),a=n(206);e.exports=function(e,t){return r(e)||o(e,t)||i(e,t)||a()},e.exports.__esModule=!0,e.exports.default=e.exports},function(e,t,n){var r=n(65),o="object"==typeof self&&self&&self.Object===Object&&self,i=r||o||Function("return this")();e.exports=i},function(e,t){e.exports=function(e){return null!=e&&"object"==typeof e}},function(e,t){e.exports=function(e){var t=typeof e;return null!=e&&("object"==t||"function"==t)}},function(e,t,n){var r=n(71),o=n(155),i=n(157);e.exports=function(e,t){return i(o(e,t,r),e+"")}},,function(e,t,n){var r=n(247);e.exports=function(e,t){if(null==e)return{};var n,o,i=r(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(o=0;o<a.length;o++)n=a[o],t.indexOf(n)>=0||{}.propertyIsEnumerable.call(e,n)&&(i[n]=e[n])}return i},e.exports.__esModule=!0,e.exports.default=e.exports},,,function(e,t,n){"undefined"!=typeof self&&self,e.exports=function(e){function t(r){if(n[r])return n[r].exports;var o=n[r]={i:r,l:!1,exports:{}};return e[r].call(o.exports,o,o.exports,t),o.l=!0,o.exports}var n={};return t.m=e,t.c=n,t.d=function(e,n,r){t.o(e,n)||Object.defineProperty(e,n,{configurable:!1,enumerable:!0,get:r})},t.n=function(e){var n=e&&e.__esModule?function(){return e.default}:function(){return e};return t.d(n,"a",n),n},t.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},t.p="",t(t.s=7)}([function(e,t,n){"use strict";function r(e){if(Array.isArray(e)){for(var t=0,n=Array(e.length);t<e.length;t++)n[t]=e[t];return n}return Array.from(e)}function o(e){var t=e.targetIndex,n=e.node,i=e.currentIndex,a=e.getNodeKey,l=e.path,s=void 0===l?[]:l,c=e.lowerSiblingCounts,u=void 0===c?[]:c,d=e.ignoreCollapsed,f=void 0===d||d,h=e.isPseudoRoot,p=void 0!==h&&h?[]:[].concat(r(s),[a({node:n,treeIndex:i})]);if(i===t)return{node:n,lowerSiblingCounts:u,path:p};if(!n.children||f&&!0!==n.expanded)return{nextIndex:i+1};for(var g=i+1,v=n.children.length,m=0;m<v;m+=1){var y=o({ignoreCollapsed:f,getNodeKey:a,targetIndex:t,node:n.children[m],currentIndex:g,lowerSiblingCounts:[].concat(r(u),[v-m-1]),path:p});if(y.node)return y;g=y.nextIndex}return{nextIndex:g}}function i(e){var t=e.node,n=e.ignoreCollapsed;return o({getNodeKey:function(){},ignoreCollapsed:void 0===n||n,node:t,currentIndex:0,targetIndex:-1}).nextIndex-1}function a(e){var t=e.treeData,n=e.getNodeKey,o=e.callback,i=e.ignoreCollapsed,a=void 0===i||i;!t||t.length<1||function e(t){var n=t.callback,o=t.getNodeKey,i=t.ignoreCollapsed,a=t.isPseudoRoot,l=void 0!==a&&a,s=t.node,c=t.parentNode,u=void 0===c?null:c,d=t.currentIndex,f=t.path,h=void 0===f?[]:f,p=t.lowerSiblingCounts,g=void 0===p?[]:p,v=l?[]:[].concat(r(h),[o({node:s,treeIndex:d})]);if(!l&&!1===n(l?null:{node:s,parentNode:u,path:v,lowerSiblingCounts:g,treeIndex:d}))return!1;if(!s.children||!0!==s.expanded&&i&&!l)return d;var m=d,y=s.children.length;if("function"!=typeof s.children)for(var _=0;_<y;_+=1)if(!1===(m=e({callback:n,getNodeKey:o,ignoreCollapsed:i,node:s.children[_],parentNode:l?null:s,currentIndex:m+1,lowerSiblingCounts:[].concat(r(g),[y-_-1]),path:v})))return!1;return m}({callback:o,getNodeKey:n,ignoreCollapsed:a,isPseudoRoot:!0,node:{children:t},currentIndex:-1,path:[],lowerSiblingCounts:[]})}function l(e){var t=e.treeData,n=e.getNodeKey,o=e.callback,i=e.ignoreCollapsed,a=void 0===i||i;return!t||t.length<1?[]:function e(t){var n=t.callback,o=t.getNodeKey,i=t.ignoreCollapsed,a=t.isPseudoRoot,l=void 0!==a&&a,s=t.node,u=t.parentNode,d=void 0===u?null:u,f=t.currentIndex,h=t.path,p=void 0===h?[]:h,g=t.lowerSiblingCounts,v=void 0===g?[]:g,m=c({},s),y=l?[]:[].concat(r(p),[o({node:m,treeIndex:f})]),_={node:m,parentNode:d,path:y,lowerSiblingCounts:v,treeIndex:f};if(!m.children||!0!==m.expanded&&i&&!l)return{treeIndex:f,node:n(_)};var b=f,w=m.children.length;return"function"!=typeof m.children&&(m.children=m.children.map((function(t,a){var s=e({callback:n,getNodeKey:o,ignoreCollapsed:i,node:t,parentNode:l?null:m,currentIndex:b+1,lowerSiblingCounts:[].concat(r(v),[w-a-1]),path:y});return b=s.treeIndex,s.node}))),{node:n(_),treeIndex:b}}({callback:o,getNodeKey:n,ignoreCollapsed:a,isPseudoRoot:!0,node:{children:t},currentIndex:-1,path:[],lowerSiblingCounts:[]}).node.children}function s(e){var t=e.treeData,n=e.path,o=e.newNode,a=e.getNodeKey,l=e.ignoreCollapsed,s=void 0===l||l,u=function e(t){var l=t.isPseudoRoot,u=void 0!==l&&l,d=t.node,f=t.currentTreeIndex,h=t.pathIndex;if(!u&&a({node:d,treeIndex:f})!==n[h])return"RESULT_MISS";if(h>=n.length-1)return"function"==typeof o?o({node:d,treeIndex:f}):o;if(!d.children)throw new Error("Path referenced children of node with no children.");for(var p=f+1,g=0;g<d.children.length;g+=1){var v=e({node:d.children[g],currentTreeIndex:p,pathIndex:h+1});if("RESULT_MISS"!==v)return c({},d,v?{children:[].concat(r(d.children.slice(0,g)),[v],r(d.children.slice(g+1)))}:{children:[].concat(r(d.children.slice(0,g)),r(d.children.slice(g+1)))});p+=1+i({node:d.children[g],ignoreCollapsed:s})}return"RESULT_MISS"}({node:{children:t},currentTreeIndex:-1,pathIndex:-1,isPseudoRoot:!0});if("RESULT_MISS"===u)throw new Error("No node found at the given path.");return u.children}Object.defineProperty(t,"__esModule",{value:!0});var c=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e};t.getDescendantCount=i,t.getVisibleNodeCount=function(e){return e.treeData.reduce((function(e,t){return e+function e(t){return t.children&&!0===t.expanded&&"function"!=typeof t.children?1+t.children.reduce((function(t,n){return t+e(n)}),0):1}(t)}),0)},t.getVisibleNodeInfoAtIndex=function(e){var t=e.treeData,n=e.index,r=e.getNodeKey;if(!t||t.length<1)return null;var i=o({targetIndex:n,getNodeKey:r,node:{children:t,expanded:!0},currentIndex:-1,path:[],lowerSiblingCounts:[],isPseudoRoot:!0});return i.node?i:null},t.walk=a,t.map=l,t.toggleExpandedForAll=function(e){var t=e.treeData,n=e.expanded,r=void 0===n||n;return l({treeData:t,callback:function(e){var t=e.node;return c({},t,{expanded:r})},getNodeKey:function(e){return e.treeIndex},ignoreCollapsed:!1})},t.changeNodeAtPath=s,t.removeNodeAtPath=function(e){var t=e.treeData,n=e.path,r=e.getNodeKey,o=e.ignoreCollapsed;return s({treeData:t,path:n,getNodeKey:r,ignoreCollapsed:void 0===o||o,newNode:null})},t.removeNode=function(e){var t=e.treeData,n=e.path,r=e.getNodeKey,o=e.ignoreCollapsed,i=null,a=null;return{treeData:s({treeData:t,path:n,getNodeKey:r,ignoreCollapsed:void 0===o||o,newNode:function(e){var t=e.node,n=e.treeIndex;return i=t,a=n,null}}),node:i,treeIndex:a}},t.getNodeAtPath=function(e){var t=e.treeData,n=e.path,r=e.getNodeKey,o=e.ignoreCollapsed,i=void 0===o||o,a=null;try{s({treeData:t,path:n,getNodeKey:r,ignoreCollapsed:i,newNode:function(e){var t=e.node,n=e.treeIndex;return a={node:t,treeIndex:n},t}})}catch(e){}return a},t.addNodeUnderParent=function(e){var t=e.treeData,n=e.newNode,o=e.parentKey,a=void 0===o?null:o,s=e.getNodeKey,u=e.ignoreCollapsed,d=void 0===u||u,f=e.expandParent,h=void 0!==f&&f;if(null===a)return{treeData:[].concat(r(t||[]),[n]),treeIndex:(t||[]).length};var p=null,g=!1,v=l({treeData:t,getNodeKey:s,ignoreCollapsed:d,callback:function(e){var t=e.node,o=e.treeIndex,l=e.path,s=l?l[l.length-1]:null;if(g||s!==a)return t;g=!0;var u=c({},t);if(h&&(u.expanded=!0),!u.children)return p=o+1,c({},u,{children:[n]});if("function"==typeof u.children)throw new Error("Cannot add to children defined by a function");for(var f=o+1,v=0;v<u.children.length;v+=1)f+=1+i({node:u.children[v],ignoreCollapsed:d});return p=f,c({},u,{children:[].concat(r(u.children),[n])})}});if(!g)throw new Error("No node found with the given key.");return{treeData:v,treeIndex:p}},t.insertNode=function(e){var t=e.treeData,n=e.depth,o=e.minimumTreeIndex,a=e.newNode,l=e.getNodeKey,s=void 0===l?function(){}:l,u=e.ignoreCollapsed,d=void 0===u||u,f=e.expandParent,h=void 0!==f&&f;if(!t&&0===n)return{treeData:[a],treeIndex:0,path:[s({node:a,treeIndex:0})],parentNode:null};var p=function e(t){var n=t.targetDepth,o=t.minimumTreeIndex,a=t.newNode,l=t.ignoreCollapsed,s=t.expandParent,u=t.isPseudoRoot,d=void 0!==u&&u,f=t.isLastChild,h=t.node,p=t.currentIndex,g=t.currentDepth,v=t.getNodeKey,m=t.path,y=void 0===m?[]:m,_=function(e){return d?[]:[].concat(r(y),[v({node:e,treeIndex:p})])};if(p>=o-1||f&&(!h.children||!h.children.length)){if("function"==typeof h.children)throw new Error("Cannot add to children defined by a function");var b=c({},h,s?{expanded:!0}:{},{children:h.children?[a].concat(r(h.children)):[a]});return{node:b,nextIndex:p+2,insertedTreeIndex:p+1,parentPath:_(b),parentNode:d?null:b}}if(g>=n-1){if(!h.children||"function"==typeof h.children||!0!==h.expanded&&l&&!d)return{node:h,nextIndex:p+1};for(var w=p+1,S=null,x=null,C=0;C<h.children.length;C+=1){if(w>=o){S=w,x=C;break}w+=1+i({node:h.children[C],ignoreCollapsed:l})}if(null===x){if(w<o&&!f)return{node:h,nextIndex:w};S=w,x=h.children.length}var O=c({},h,{children:[].concat(r(h.children.slice(0,x)),[a],r(h.children.slice(x)))});return{node:O,nextIndex:w,insertedTreeIndex:S,parentPath:_(O),parentNode:d?null:O}}if(!h.children||"function"==typeof h.children||!0!==h.expanded&&l&&!d)return{node:h,nextIndex:p+1};var T=null,I=null,D=null,k=p+1,R=h.children;"function"!=typeof R&&(R=R.map((function(t,r){if(null!==T)return t;var i=e({targetDepth:n,minimumTreeIndex:o,newNode:a,ignoreCollapsed:l,expandParent:s,isLastChild:f&&r===R.length-1,node:t,currentIndex:k,currentDepth:g+1,getNodeKey:v,path:[]});return"insertedTreeIndex"in i&&(T=i.insertedTreeIndex,D=i.parentNode,I=i.parentPath),k=i.nextIndex,i.node})));var P=c({},h,{children:R}),E={node:P,nextIndex:k};return null!==T&&(E.insertedTreeIndex=T,E.parentPath=[].concat(r(_(P)),r(I)),E.parentNode=D),E}({targetDepth:n,minimumTreeIndex:o,newNode:a,ignoreCollapsed:d,expandParent:h,getNodeKey:s,isPseudoRoot:!0,isLastChild:!0,node:{children:t},currentIndex:-1,currentDepth:-1});if(!("insertedTreeIndex"in p))throw new Error("No suitable position found to insert.");var g=p.insertedTreeIndex;return{treeData:p.node.children,treeIndex:g,path:[].concat(r(p.parentPath),[s({node:a,treeIndex:g})]),parentNode:p.parentNode}},t.getFlatDataFromTree=function(e){var t=e.treeData,n=e.getNodeKey,r=e.ignoreCollapsed,o=void 0===r||r;if(!t||t.length<1)return[];var i=[];return a({treeData:t,getNodeKey:n,ignoreCollapsed:o,callback:function(e){i.push(e)}}),i},t.getTreeFromFlatData=function(e){var t=e.flatData,n=e.getKey,r=void 0===n?function(e){return e.id}:n,o=e.getParentKey,i=void 0===o?function(e){return e.parentId}:o,a=e.rootKey,l=void 0===a?"0":a;if(!t)return[];var s={};return t.forEach((function(e){var t=i(e);t in s?s[t].push(e):s[t]=[e]})),l in s?s[l].map((function(e){return function e(t){var n=r(t);return n in s?c({},t,{children:s[n].map((function(t){return e(t)}))}):c({},t)}(e)})):[]},t.isDescendant=function e(t,n){return!!t.children&&"function"!=typeof t.children&&t.children.some((function(t){return t===n||e(t,n)}))},t.getDepth=function e(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return t.children?"function"==typeof t.children?n+1:t.children.reduce((function(t,r){return Math.max(t,e(r,n+1))}),n):n},t.find=function(e){var t=e.getNodeKey,n=e.treeData,o=e.searchQuery,i=e.searchMethod,a=e.searchFocusOffset,l=e.expandAllMatchPaths,s=void 0!==l&&l,u=e.expandFocusMatchPaths,d=void 0===u||u,f=0,h=function e(n){var l=n.isPseudoRoot,u=void 0!==l&&l,h=n.node,p=n.currentIndex,g=n.path,v=[],m=!1,y=!1,_=u?[]:[].concat(r(void 0===g?[]:g),[t({node:h,treeIndex:p})]),b=u?null:{path:_,treeIndex:p},w=h.children&&"function"!=typeof h.children&&h.children.length>0;!u&&i(c({},b,{node:h,searchQuery:o}))&&(f===a&&(y=!0),f+=1,m=!0);var S=p,x=c({},h);return w&&(x.children=x.children.map((function(t){var n=e({node:t,currentIndex:S+1,path:_});return n.node.expanded?S=n.treeIndex:S+=1,(n.matches.length>0||n.hasFocusMatch)&&(v=[].concat(r(v),r(n.matches)),n.hasFocusMatch&&(y=!0),(s&&n.matches.length>0||(s||d)&&n.hasFocusMatch)&&(x.expanded=!0)),n.node}))),u||x.expanded||(v=v.map((function(e){return c({},e,{treeIndex:null})}))),m&&(v=[c({},b,{node:x})].concat(r(v))),{node:v.length>0?x:h,matches:v,hasFocusMatch:y,treeIndex:S}}({node:{children:n},isPseudoRoot:!0,currentIndex:-1});return{matches:h.matches,treeData:h.node.children}}},function(e,t){e.exports=n(0)},function(e,t){e.exports=n(2)},function(e,t){e.exports=function(e){var t=[];return t.toString=function(){return this.map((function(t){var n=function(e,t){var n,r=e[1]||"",o=e[3];if(!o)return r;if(t&&"function"==typeof btoa){var i=(n=o,"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,"+btoa(unescape(encodeURIComponent(JSON.stringify(n))))+" */");return[r].concat(o.sources.map((function(e){return"/*# sourceURL="+o.sourceRoot+e+" */"}))).concat([i]).join("\n")}return[r].join("\n")}(t,e);return t[2]?"@media "+t[2]+"{"+n+"}":n})).join("")},t.i=function(e,n){"string"==typeof e&&(e=[[null,e,""]]);for(var r={},o=0;o<this.length;o++){var i=this[o][0];"number"==typeof i&&(r[i]=!0)}for(o=0;o<e.length;o++){var a=e[o];"number"==typeof a[0]&&r[a[0]]||(n&&!a[2]?a[2]=n:n&&(a[2]="("+a[2]+") and ("+n+")"),t.push(a))}},t}},function(e,t,n){function r(e,t){for(var n=0;n<e.length;n++){var r=e[n],o=g[r.id];if(o){o.refs++;for(var i=0;i<o.parts.length;i++)o.parts[i](r.parts[i]);for(;i<r.parts.length;i++)o.parts.push(c(r.parts[i],t))}else{var a=[];for(i=0;i<r.parts.length;i++)a.push(c(r.parts[i],t));g[r.id]={id:r.id,refs:1,parts:a}}}}function o(e,t){for(var n=[],r={},o=0;o<e.length;o++){var i=e[o],a=t.base?i[0]+t.base:i[0],l={css:i[1],media:i[2],sourceMap:i[3]};r[a]?r[a].parts.push(l):n.push(r[a]={id:a,parts:[l]})}return n}function i(e,t){var n=m(e.insertInto);if(!n)throw new Error("Couldn't find a style target. This probably means that the value for the 'insertInto' parameter is invalid.");var r=b[b.length-1];if("top"===e.insertAt)r?r.nextSibling?n.insertBefore(t,r.nextSibling):n.appendChild(t):n.insertBefore(t,n.firstChild),b.push(t);else if("bottom"===e.insertAt)n.appendChild(t);else{if("object"!=typeof e.insertAt||!e.insertAt.before)throw new Error("[Style Loader]\n\n Invalid value for parameter 'insertAt' ('options.insertAt') found.\n Must be 'top', 'bottom', or Object.\n (https://github.com/webpack-contrib/style-loader#insertat)\n");var o=m(e.insertInto+" "+e.insertAt.before);n.insertBefore(t,o)}}function a(e){if(null===e.parentNode)return!1;e.parentNode.removeChild(e);var t=b.indexOf(e);t>=0&&b.splice(t,1)}function l(e){var t=document.createElement("style");return e.attrs.type="text/css",s(t,e.attrs),i(e,t),t}function s(e,t){Object.keys(t).forEach((function(n){e.setAttribute(n,t[n])}))}function c(e,t){var n,r,o,c;if(t.transform&&e.css){if(!(c=t.transform(e.css)))return function(){};e.css=c}if(t.singleton){var h=_++;n=y||(y=l(t)),r=u.bind(null,n,h,!1),o=u.bind(null,n,h,!0)}else e.sourceMap&&"function"==typeof URL&&"function"==typeof URL.createObjectURL&&"function"==typeof URL.revokeObjectURL&&"function"==typeof Blob&&"function"==typeof btoa?(n=function(e){var t=document.createElement("link");return e.attrs.type="text/css",e.attrs.rel="stylesheet",s(t,e.attrs),i(e,t),t}(t),r=f.bind(null,n,t),o=function(){a(n),n.href&&URL.revokeObjectURL(n.href)}):(n=l(t),r=d.bind(null,n),o=function(){a(n)});return r(e),function(t){if(t){if(t.css===e.css&&t.media===e.media&&t.sourceMap===e.sourceMap)return;r(e=t)}else o()}}function u(e,t,n,r){var o=n?"":r.css;if(e.styleSheet)e.styleSheet.cssText=x(t,o);else{var i=document.createTextNode(o),a=e.childNodes;a[t]&&e.removeChild(a[t]),a.length?e.insertBefore(i,a[t]):e.appendChild(i)}}function d(e,t){var n=t.css,r=t.media;if(r&&e.setAttribute("media",r),e.styleSheet)e.styleSheet.cssText=n;else{for(;e.firstChild;)e.removeChild(e.firstChild);e.appendChild(document.createTextNode(n))}}function f(e,t,n){var r=n.css,o=n.sourceMap,i=void 0===t.convertToAbsoluteUrls&&o;(t.convertToAbsoluteUrls||i)&&(r=w(r)),o&&(r+="\n/*# sourceMappingURL=data:application/json;base64,"+btoa(unescape(encodeURIComponent(JSON.stringify(o))))+" */");var a=new Blob([r],{type:"text/css"}),l=e.href;e.href=URL.createObjectURL(a),l&&URL.revokeObjectURL(l)}var h,p,g={},v=(h=function(){return window&&document&&document.all&&!window.atob},function(){return void 0===p&&(p=h.apply(this,arguments)),p}),m=function(e){var t={};return function(n){if(void 0===t[n]){var r=e.call(this,n);if(r instanceof window.HTMLIFrameElement)try{r=r.contentDocument.head}catch(e){r=null}t[n]=r}return t[n]}}((function(e){return document.querySelector(e)})),y=null,_=0,b=[],w=n(14);e.exports=function(e,t){if("undefined"!=typeof DEBUG&&DEBUG&&"object"!=typeof document)throw new Error("The style-loader cannot be used in a non-browser environment");(t=t||{}).attrs="object"==typeof t.attrs?t.attrs:{},t.singleton||"boolean"==typeof t.singleton||(t.singleton=v()),t.insertInto||(t.insertInto="head"),t.insertAt||(t.insertAt="bottom");var n=o(e,t);return r(n,t),function(e){for(var i=[],a=0;a<n.length;a++){var l=n[a];(s=g[l.id]).refs--,i.push(s)}for(e&&r(o(e,t),t),a=0;a<i.length;a++){var s;if(0===(s=i[a]).refs){for(var c=0;c<s.parts.length;c++)s.parts[c]();delete g[s.id]}}}};var S,x=(S=[],function(e,t){return S[e]=t,S.filter(Boolean).join("\n")})},function(e,t,n){"use strict";function r(e,t,n,r,i){return"function"==typeof n[e]?String(n[e]({node:n,path:r,treeIndex:i})).indexOf(t)>-1:"object"===o(n[e])?function e(t){return"string"==typeof t?t:"object"!==(void 0===t?"undefined":o(t))||!t.props||!t.props.children||"string"!=typeof t.props.children&&"object"!==o(t.props.children)?"":"string"==typeof t.props.children?t.props.children:t.props.children.map((function(t){return e(t)})).join("")}(n[e]).indexOf(t)>-1:n[e]&&String(n[e]).indexOf(t)>-1}Object.defineProperty(t,"__esModule",{value:!0});var o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e};t.defaultGetNodeKey=function(e){return e.treeIndex},t.defaultSearchMethod=function(e){var t=e.node,n=e.path,o=e.treeIndex,i=e.searchQuery;return r("title",i,t,n,o)||r("subtitle",i,t,n,o)}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.memoizedGetDescendantCount=t.memoizedGetFlatDataFromTree=t.memoizedInsertNode=void 0;var r=n(0),o=function(e){var t=[],n=[],r=null;return function(o){var i=Object.keys(o).sort(),a=i.map((function(e){return o[e]}));return(a.length!==t.length||a.some((function(e,n){return e!==t[n]}))||i.some((function(e,t){return e!==n[t]})))&&(t=a,n=i,r=e(o)),r}};t.memoizedInsertNode=o(r.insertNode),t.memoizedGetFlatDataFromTree=o(r.getFlatDataFromTree),t.memoizedGetDescendantCount=o(r.getDescendantCount)},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.SortableTreeWithoutDndContext=void 0;var r=n(5);Object.keys(r).forEach((function(e){"default"!==e&&"__esModule"!==e&&Object.defineProperty(t,e,{enumerable:!0,get:function(){return r[e]}})}));var o=n(0);Object.keys(o).forEach((function(e){"default"!==e&&"__esModule"!==e&&Object.defineProperty(t,e,{enumerable:!0,get:function(){return o[e]}})}));var i,a=n(8),l=(i=a)&&i.__esModule?i:{default:i};t.default=l.default,t.SortableTreeWithoutDndContext=a.SortableTreeWithoutDndContext},function(e,t,n){"use strict";function r(e){return e&&e.__esModule?e:{default:e}}Object.defineProperty(t,"__esModule",{value:!0}),t.SortableTreeWithoutDndContext=void 0;var o=function(){function e(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,n,r){return n&&e(t.prototype,n),r&&e(t,r),t}}(),i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},a=n(1),l=r(a),s=r(n(2)),c=n(9),u=r(n(10)),d=n(11),f=r(d);n(12);var h=r(n(15)),p=r(n(18)),g=r(n(22)),v=r(n(23)),m=n(0),y=n(6),_=n(26),b=n(5),w=r(n(27)),S=r(n(31)),x=1,C=function(e){var t=i({},e,{style:i({},e.theme.style,e.style),innerStyle:i({},e.theme.innerStyle,e.innerStyle),reactVirtualizedListProps:i({},e.theme.reactVirtualizedListProps,e.reactVirtualizedListProps)}),n={nodeContentRenderer:p.default,placeholderRenderer:v.default,rowHeight:62,scaffoldBlockPxWidth:44,slideRegionSize:100,treeNodeRenderer:h.default};return Object.keys(n).forEach((function(r){null===e[r]&&(t[r]=void 0!==e.theme[r]?e.theme[r]:n[r])})),t},O=function(e){function t(e){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,t);var n=function(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}(this,(t.__proto__||Object.getPrototypeOf(t)).call(this,e)),r=C(e),o=r.dndType,i=r.nodeContentRenderer,a=r.treeNodeRenderer,l=r.isVirtualized,s=r.slideRegionSize;return n.dndManager=new w.default(n),n.treeId="rst__"+x,x+=1,n.dndType=o||n.treeId,n.nodeContentRenderer=n.dndManager.wrapSource(i),n.treePlaceholderRenderer=n.dndManager.wrapPlaceholder(g.default),n.treeNodeRenderer=n.dndManager.wrapTarget(a),l&&(n.scrollZoneVirtualList=(0,f.default)(c.List),n.vStrength=(0,d.createVerticalStrength)(s),n.hStrength=(0,d.createHorizontalStrength)(s)),n.state={draggingTreeData:null,draggedNode:null,draggedMinimumTreeIndex:null,draggedDepth:null,searchMatches:[],searchFocusTreeIndex:null,dragging:!1},n.toggleChildrenVisibility=n.toggleChildrenVisibility.bind(n),n.moveNode=n.moveNode.bind(n),n.startDrag=n.startDrag.bind(n),n.dragHover=n.dragHover.bind(n),n.endDrag=n.endDrag.bind(n),n.drop=n.drop.bind(n),n.handleDndMonitorChange=n.handleDndMonitorChange.bind(n),n}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(t,e),o(t,[{key:"componentDidMount",value:function(){this.loadLazyChildren(),this.search(this.props),this.clearMonitorSubscription=this.context.dragDropManager.getMonitor().subscribeToStateChange(this.handleDndMonitorChange)}},{key:"componentWillReceiveProps",value:function(e){this.props.treeData!==e.treeData?(this.ignoreOneTreeUpdate?this.ignoreOneTreeUpdate=!1:(this.setState({searchFocusTreeIndex:null}),this.loadLazyChildren(e),this.search(e,!1,!1)),this.setState({draggingTreeData:null,draggedNode:null,draggedMinimumTreeIndex:null,draggedDepth:null,dragging:!1})):(0,u.default)(this.props.searchQuery,e.searchQuery)?this.props.searchFocusOffset!==e.searchFocusOffset&&this.search(e,!0,!0,!0):this.search(e)}},{key:"componentDidUpdate",value:function(e,t){this.state.dragging!==t.dragging&&this.props.onDragStateChanged&&this.props.onDragStateChanged({isDragging:this.state.dragging,draggedNode:this.state.draggedNode})}},{key:"componentWillUnmount",value:function(){this.clearMonitorSubscription()}},{key:"getRows",value:function(e){return(0,y.memoizedGetFlatDataFromTree)({ignoreCollapsed:!0,getNodeKey:this.props.getNodeKey,treeData:e})}},{key:"handleDndMonitorChange",value:function(){!this.context.dragDropManager.getMonitor().isDragging()&&this.state.draggingTreeData&&this.endDrag()}},{key:"toggleChildrenVisibility",value:function(e){var t=e.node,n=e.path,r=(0,m.changeNodeAtPath)({treeData:this.props.treeData,path:n,newNode:function(e){var t=e.node;return i({},t,{expanded:!t.expanded})},getNodeKey:this.props.getNodeKey});this.props.onChange(r),this.props.onVisibilityToggle({treeData:r,node:t,expanded:!t.expanded,path:n})}},{key:"moveNode",value:function(e){var t=e.node,n=e.path,r=e.treeIndex,o=e.depth,i=e.minimumTreeIndex,a=(0,m.insertNode)({treeData:this.state.draggingTreeData,newNode:t,depth:o,minimumTreeIndex:i,expandParent:!0,getNodeKey:this.props.getNodeKey}),l=a.treeData,s=a.treeIndex,c=a.path,u=a.parentNode;this.props.onChange(l),this.props.onMoveNode({treeData:l,node:t,treeIndex:s,path:c,nextPath:c,nextTreeIndex:s,prevPath:n,prevTreeIndex:r,nextParentNode:u})}},{key:"search",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.props,t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1],n=!(arguments.length>2&&void 0!==arguments[2])||arguments[2],r=arguments.length>3&&void 0!==arguments[3]&&arguments[3],o=e.treeData,i=e.onChange,a=e.searchFinishCallback,l=e.searchQuery,s=e.searchMethod,c=e.searchFocusOffset;if((null==l||""===String(l))&&!s)return this.setState({searchMatches:[]}),void(a&&a([]));var u=(0,m.find)({getNodeKey:this.props.getNodeKey,treeData:o,searchQuery:l,searchMethod:s||b.defaultSearchMethod,searchFocusOffset:c,expandAllMatchPaths:n&&!r,expandFocusMatchPaths:!!n}),d=u.treeData,f=u.matches;n&&(this.ignoreOneTreeUpdate=!0,i(d)),a&&a(f);var h=null;t&&null!==c&&c<f.length&&(h=f[c].treeIndex),this.setState({searchMatches:f,searchFocusTreeIndex:h})}},{key:"startDrag",value:function(e){var t=this,n=e.path;this.setState((function(){var e=(0,m.removeNode)({treeData:t.props.treeData,path:n,getNodeKey:t.props.getNodeKey}),r=e.treeData,o=e.node,i=e.treeIndex;return{draggingTreeData:r,draggedNode:o,draggedDepth:n.length-1,draggedMinimumTreeIndex:i,dragging:!0}}))}},{key:"dragHover",value:function(e){var t=e.node,n=e.depth,r=e.minimumTreeIndex;if(this.state.draggedDepth!==n||this.state.draggedMinimumTreeIndex!==r){var o=this.state.draggingTreeData||this.props.treeData,a=(0,y.memoizedInsertNode)({treeData:o,newNode:t,depth:n,minimumTreeIndex:r,expandParent:!0,getNodeKey:this.props.getNodeKey}),l=this.getRows(a.treeData)[a.treeIndex].path;this.setState({draggedNode:t,draggedDepth:n,draggedMinimumTreeIndex:r,draggingTreeData:(0,m.changeNodeAtPath)({treeData:o,path:l.slice(0,-1),newNode:function(e){var t=e.node;return i({},t,{expanded:!0})},getNodeKey:this.props.getNodeKey}),searchFocusTreeIndex:null,dragging:!0})}}},{key:"endDrag",value:function(e){if(e){if(e.treeId!==this.treeId){var t=e.node,n=e.path,r=e.treeIndex,o=this.props.shouldCopyOnOutsideDrop;"function"==typeof o&&(o=o({node:t,prevTreeIndex:r,prevPath:n}));var a=this.state.draggingTreeData||this.props.treeData;o&&(a=(0,m.changeNodeAtPath)({treeData:this.props.treeData,path:n,newNode:function(e){var t=e.node;return i({},t)},getNodeKey:this.props.getNodeKey})),this.props.onChange(a),this.props.onMoveNode({treeData:a,node:t,treeIndex:null,path:null,nextPath:null,nextTreeIndex:null,prevPath:n,prevTreeIndex:r})}}else this.setState({draggingTreeData:null,draggedNode:null,draggedMinimumTreeIndex:null,draggedDepth:null,dragging:!1})}},{key:"drop",value:function(e){this.moveNode(e)}},{key:"loadLazyChildren",value:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.props;(0,m.walk)({treeData:t.treeData,getNodeKey:this.props.getNodeKey,callback:function(n){var r=n.node,o=n.path,a=n.lowerSiblingCounts,l=n.treeIndex;r.children&&"function"==typeof r.children&&(r.expanded||t.loadCollapsedLazyChildren)&&r.children({node:r,path:o,lowerSiblingCounts:a,treeIndex:l,done:function(t){return e.props.onChange((0,m.changeNodeAtPath)({treeData:e.props.treeData,path:o,newNode:function(e){var n=e.node;return n===r?i({},n,{children:t}):n},getNodeKey:e.props.getNodeKey}))}})}})}},{key:"renderRow",value:function(e,t){var n=e.node,r=e.parentNode,o=e.path,a=e.lowerSiblingCounts,s=e.treeIndex,c=t.listIndex,u=t.style,d=t.getPrevRow,f=t.matchKeys,h=t.swapFrom,p=t.swapDepth,g=t.swapLength,v=C(this.props),m=v.canDrag,y=v.generateNodeProps,_=v.scaffoldBlockPxWidth,b=v.searchFocusOffset,w=this.treeNodeRenderer,S=this.nodeContentRenderer,x=o[o.length-1],O=x in f,T=O&&f[x]===b,I={node:n,parentNode:r,path:o,lowerSiblingCounts:a,treeIndex:s,isSearchMatch:O,isSearchFocus:T},D=y?y(I):{},k="function"!=typeof m?m:m(I),R={treeIndex:s,scaffoldBlockPxWidth:_,node:n,path:o,treeId:this.treeId};return l.default.createElement(w,i({style:u,key:x,listIndex:c,getPrevRow:d,lowerSiblingCounts:a,swapFrom:h,swapLength:g,swapDepth:p},R),l.default.createElement(S,i({parentNode:r,isSearchMatch:O,isSearchFocus:T,canDrag:k,toggleChildrenVisibility:this.toggleChildrenVisibility},R,D)))}},{key:"render",value:function(){var e=this,t=C(this.props),n=t.style,r=t.className,o=t.innerStyle,a=t.rowHeight,s=t.isVirtualized,u=t.placeholderRenderer,d=t.reactVirtualizedListProps,f=t.getNodeKey,h=this.state,p=h.searchMatches,g=h.searchFocusTreeIndex,v=h.draggedNode,m=h.draggedDepth,b=h.draggedMinimumTreeIndex,w=this.state.draggingTreeData||this.props.treeData,x=void 0,O=null,T=null;if(v&&null!==b){var I=(0,y.memoizedInsertNode)({treeData:w,newNode:v,depth:m,minimumTreeIndex:b,expandParent:!0,getNodeKey:f}),D=b;O=I.treeIndex,T=1+(0,y.memoizedGetDescendantCount)({node:v}),x=(0,_.slideRows)(this.getRows(I.treeData),O,D,T)}else x=this.getRows(w);var k={};p.forEach((function(e,t){var n=e.path;k[n[n.length-1]]=t}));var R=null!==g?{scrollToIndex:g}:{},P=n,E=void 0;if(x.length<1){var M=this.treePlaceholderRenderer,j=u;E=l.default.createElement(M,{treeId:this.treeId,drop:this.drop},l.default.createElement(j,null))}else if(s){P=i({height:"100%"},P);var z=this.scrollZoneVirtualList;E=l.default.createElement(c.AutoSizer,null,(function(t){var n=t.height,r=t.width;return l.default.createElement(z,i({},R,{verticalStrength:e.vStrength,horizontalStrength:e.hStrength,speed:30,scrollToAlignment:"start",className:S.default.virtualScrollOverride,width:r,onScroll:function(t){var n=t.scrollTop;e.scrollTop=n},height:n,style:o,rowCount:x.length,estimatedRowSize:"function"!=typeof a?a:void 0,rowHeight:"function"!=typeof a?a:function(e){var t=e.index;return a({index:t,treeIndex:t,node:x[t].node,path:x[t].path})},rowRenderer:function(t){var n=t.index,r=t.style;return e.renderRow(x[n],{listIndex:n,style:r,getPrevRow:function(){return x[n-1]||null},matchKeys:k,swapFrom:O,swapDepth:m,swapLength:T})}},d))}))}else E=x.map((function(t,n){return e.renderRow(t,{listIndex:n,style:{height:"function"!=typeof a?a:a({index:n,treeIndex:n,node:t.node,path:t.path})},getPrevRow:function(){return x[n-1]||null},matchKeys:k,swapFrom:O,swapDepth:m,swapLength:T})}));return l.default.createElement("div",{className:S.default.tree+(r?" "+r:""),style:P},E)}}]),t}(a.Component);O.propTypes={treeData:s.default.arrayOf(s.default.object).isRequired,style:s.default.shape({}),className:s.default.string,innerStyle:s.default.shape({}),rowHeight:s.default.oneOfType([s.default.number,s.default.func]),slideRegionSize:s.default.number,reactVirtualizedListProps:s.default.shape({}),scaffoldBlockPxWidth:s.default.number,maxDepth:s.default.number,searchMethod:s.default.func,searchQuery:s.default.any,searchFocusOffset:s.default.number,searchFinishCallback:s.default.func,generateNodeProps:s.default.func,isVirtualized:s.default.bool,treeNodeRenderer:s.default.func,nodeContentRenderer:s.default.func,placeholderRenderer:s.default.func,theme:s.default.shape({style:s.default.shape({}),innerStyle:s.default.shape({}),reactVirtualizedListProps:s.default.shape({}),scaffoldBlockPxWidth:s.default.number,slideRegionSize:s.default.number,rowHeight:s.default.oneOfType([s.default.number,s.default.func]),treeNodeRenderer:s.default.func,nodeContentRenderer:s.default.func,placeholderRenderer:s.default.func}),getNodeKey:s.default.func,onChange:s.default.func.isRequired,onMoveNode:s.default.func,canDrag:s.default.oneOfType([s.default.func,s.default.bool]),canDrop:s.default.func,shouldCopyOnOutsideDrop:s.default.oneOfType([s.default.func,s.default.bool]),onVisibilityToggle:s.default.func,dndType:s.default.string,onDragStateChanged:s.default.func},O.defaultProps={canDrag:!0,canDrop:null,className:"",dndType:null,generateNodeProps:null,getNodeKey:b.defaultGetNodeKey,innerStyle:{},isVirtualized:!0,maxDepth:null,treeNodeRenderer:null,nodeContentRenderer:null,onMoveNode:function(){},onVisibilityToggle:function(){},placeholderRenderer:null,reactVirtualizedListProps:{},rowHeight:null,scaffoldBlockPxWidth:null,searchFinishCallback:null,searchFocusOffset:null,searchMethod:null,searchQuery:null,shouldCopyOnOutsideDrop:!1,slideRegionSize:null,style:{},theme:{},onDragStateChanged:function(){}},O.contextTypes={dragDropManager:s.default.shape({})},t.SortableTreeWithoutDndContext=O,t.default=w.default.wrapRoot(O)},function(e,t){e.exports=n(291)},function(e,t){e.exports=n(248)},function(e,t){e.exports=n(249)},function(e,t,n){var r=n(13);"string"==typeof r&&(r=[[e.i,r,""]]);n(4)(r,{insertAt:"top",hmr:!0,transform:void 0}),r.locals&&(e.exports=r.locals)},function(e,t,n){(t=e.exports=n(3)(!1)).push([e.i,"/* Collection default theme */\n\n._2Ys25aFCIH7ZWp76lcHbjs {\n}\n\n._1wpSOFYFr7x9PMM0KcYTxm {\n}\n\n/* Grid default theme */\n\n._2SUC15-WVgF4XhvHuU81GS {\n}\n\n._3q1PBGJ8fGciELoHlm7I3C {\n}\n\n/* Table default theme */\n\n._1yPBpbwEbdtD7UxLvhZhM {\n}\n\n._1itUjAnMCYR-25x1rmTdtF {\n}\n\n._33g8s-zht4xwLlXSWWWwa3 {\n  font-weight: 700;\n  text-transform: uppercase;\n  display: -webkit-box;\n  display: -ms-flexbox;\n  display: flex;\n  -webkit-box-orient: horizontal;\n  -webkit-box-direction: normal;\n      -ms-flex-direction: row;\n          flex-direction: row;\n  -webkit-box-align: center;\n      -ms-flex-align: center;\n          align-items: center;\n}\n._3ZiV_g6aRjm62hwIWbJU38 {\n  display: -webkit-box;\n  display: -ms-flexbox;\n  display: flex;\n  -webkit-box-orient: horizontal;\n  -webkit-box-direction: normal;\n      -ms-flex-direction: row;\n          flex-direction: row;\n  -webkit-box-align: center;\n      -ms-flex-align: center;\n          align-items: center;\n}\n\n._3eKreK7WxwxtZyKjYLiygd {\n  display: inline-block;\n  max-width: 100%;\n  white-space: nowrap;\n  text-overflow: ellipsis;\n  overflow: hidden;\n}\n\n._19Z4wAfIa_DTwtOUYjz8t,\n._2TseG-JCD0K5o_VT5VIN_w {\n  margin-right: 10px;\n  min-width: 0px;\n}\n._2TseG-JCD0K5o_VT5VIN_w {\n  text-overflow: ellipsis;\n  white-space: nowrap;\n}\n\n._19Z4wAfIa_DTwtOUYjz8t:first-of-type,\n._2TseG-JCD0K5o_VT5VIN_w:first-of-type {\n  margin-left: 10px;\n}\n._1RR8EsDxozD7xvl8boaBiB {\n  cursor: pointer;\n}\n\n._1u1DZQe3YaUugu3cLrtqng {\n  display: -webkit-box;\n  display: -ms-flexbox;\n  display: flex;\n  -webkit-box-align: center;\n      -ms-flex-align: center;\n          align-items: center;\n}\n._15mdGqvC02VcdkTaoIuS06 {\n  -webkit-box-flex: 0;\n      -ms-flex: 0 0 24px;\n          flex: 0 0 24px;\n  height: 1em;\n  width: 1em;\n  fill: currentColor;\n}\n\n/* List default theme */\n\n._3HeSXQaspPzpYJlLHQlyit {\n}",""]),t.locals={ReactVirtualized__Collection:"_2Ys25aFCIH7ZWp76lcHbjs",ReactVirtualized__Collection__innerScrollContainer:"_1wpSOFYFr7x9PMM0KcYTxm",ReactVirtualized__Grid:"_2SUC15-WVgF4XhvHuU81GS",ReactVirtualized__Grid__innerScrollContainer:"_3q1PBGJ8fGciELoHlm7I3C",ReactVirtualized__Table:"_1yPBpbwEbdtD7UxLvhZhM",ReactVirtualized__Table__Grid:"_1itUjAnMCYR-25x1rmTdtF",ReactVirtualized__Table__headerRow:"_33g8s-zht4xwLlXSWWWwa3",ReactVirtualized__Table__row:"_3ZiV_g6aRjm62hwIWbJU38",ReactVirtualized__Table__headerTruncatedText:"_3eKreK7WxwxtZyKjYLiygd",ReactVirtualized__Table__headerColumn:"_19Z4wAfIa_DTwtOUYjz8t",ReactVirtualized__Table__rowColumn:"_2TseG-JCD0K5o_VT5VIN_w",ReactVirtualized__Table__sortableHeaderColumn:"_1RR8EsDxozD7xvl8boaBiB",ReactVirtualized__Table__sortableHeaderIconContainer:"_1u1DZQe3YaUugu3cLrtqng",ReactVirtualized__Table__sortableHeaderIcon:"_15mdGqvC02VcdkTaoIuS06",ReactVirtualized__List:"_3HeSXQaspPzpYJlLHQlyit"}},function(e,t){e.exports=function(e){var t="undefined"!=typeof window&&window.location;if(!t)throw new Error("fixUrls requires window.location");if(!e||"string"!=typeof e)return e;var n=t.protocol+"//"+t.host,r=n+t.pathname.replace(/\/[^\/]*$/,"/");return e.replace(/url\s*\(((?:[^)(]|\((?:[^)(]+|\([^)(]*\))*\))*)\)/gi,(function(e,t){var o,i=t.trim().replace(/^"(.*)"$/,(function(e,t){return t})).replace(/^'(.*)'$/,(function(e,t){return t}));return/^(#|data:|http:\/\/|https:\/\/|file:\/\/\/)/i.test(i)?e:(o=0===i.indexOf("//")?i:0===i.indexOf("/")?n+i:r+i.replace(/^\.\//,""),"url("+JSON.stringify(o)+")")}))}},function(e,t,n){"use strict";function r(e){return e&&e.__esModule?e:{default:e}}function o(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function i(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}Object.defineProperty(t,"__esModule",{value:!0});var a=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},l=function(){function e(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,n,r){return n&&e(t.prototype,n),r&&e(t,r),t}}(),s=n(1),c=r(s),u=r(n(2)),d=r(n(16)),f=function(e){function t(){return o(this,t),i(this,(t.__proto__||Object.getPrototypeOf(t)).apply(this,arguments))}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(t,e),l(t,[{key:"render",value:function(){var e=this.props,t=e.children,n=e.listIndex,r=e.swapFrom,o=e.swapLength,i=e.swapDepth,l=e.scaffoldBlockPxWidth,u=e.lowerSiblingCounts,f=e.connectDropTarget,h=e.isOver,p=e.draggedNode,g=e.canDrop,v=e.treeIndex,m=(e.treeId,e.getPrevRow,e.node,e.path,function(e,t){var n={};for(var r in e)t.indexOf(r)>=0||Object.prototype.hasOwnProperty.call(e,r)&&(n[r]=e[r]);return n}(e,["children","listIndex","swapFrom","swapLength","swapDepth","scaffoldBlockPxWidth","lowerSiblingCounts","connectDropTarget","isOver","draggedNode","canDrop","treeIndex","treeId","getPrevRow","node","path"])),y=u.length,_=[];return u.forEach((function(e,t){var a,s="";e>0?s=0===n?d.default.lineHalfHorizontalRight+" "+d.default.lineHalfVerticalBottom:t===y-1?d.default.lineHalfHorizontalRight+" "+d.default.lineFullVertical:d.default.lineFullVertical:0===n?s=d.default.lineHalfHorizontalRight:t===y-1&&(s=d.default.lineHalfVerticalTop+" "+d.default.lineHalfHorizontalRight),_.push(c.default.createElement("div",{key:"pre_"+(1+t),style:{width:l},className:d.default.lineBlock+" "+s})),v!==n&&t===i&&(a=n===r+o-1?d.default.highlightBottomLeftCorner:v===r?d.default.highlightTopLeftCorner:d.default.highlightLineVertical,_.push(c.default.createElement("div",{key:t,style:{width:l,left:l*t},className:d.default.absoluteLineBlock+" "+a})))})),f(c.default.createElement("div",a({},m,{className:d.default.node}),_,c.default.createElement("div",{className:d.default.nodeContent,style:{left:l*y}},s.Children.map(t,(function(e){return(0,s.cloneElement)(e,{isOver:h,canDrop:g,draggedNode:p})})))))}}]),t}(s.Component);f.defaultProps={swapFrom:null,swapDepth:null,swapLength:null,canDrop:!1,draggedNode:null},f.propTypes={treeIndex:u.default.number.isRequired,treeId:u.default.string.isRequired,swapFrom:u.default.number,swapDepth:u.default.number,swapLength:u.default.number,scaffoldBlockPxWidth:u.default.number.isRequired,lowerSiblingCounts:u.default.arrayOf(u.default.number).isRequired,listIndex:u.default.number.isRequired,children:u.default.node.isRequired,connectDropTarget:u.default.func.isRequired,isOver:u.default.bool.isRequired,canDrop:u.default.bool,draggedNode:u.default.shape({}),getPrevRow:u.default.func.isRequired,node:u.default.shape({}).isRequired,path:u.default.arrayOf(u.default.oneOfType([u.default.string,u.default.number])).isRequired},t.default=f},function(e,t,n){var r=n(17);"string"==typeof r&&(r=[[e.i,r,""]]);n(4)(r,{insertAt:"top",hmr:!0,transform:void 0}),r.locals&&(e.exports=r.locals)},function(e,t,n){(t=e.exports=n(3)(!1)).push([e.i,".rst__node {\n  min-width: 100%;\n  white-space: nowrap;\n  position: relative;\n  text-align: left; }\n\n.rst__nodeContent {\n  position: absolute;\n  top: 0;\n  bottom: 0; }\n\n/* ==========================================================================\n   Scaffold\n\n    Line-overlaid blocks used for showing the tree structure\n   ========================================================================== */\n.rst__lineBlock, .rst__absoluteLineBlock {\n  height: 100%;\n  position: relative;\n  display: inline-block; }\n\n.rst__absoluteLineBlock {\n  position: absolute;\n  top: 0; }\n\n.rst__lineHalfHorizontalRight::before, .rst__lineFullVertical::after, .rst__lineHalfVerticalTop::after, .rst__lineHalfVerticalBottom::after {\n  position: absolute;\n  content: '';\n  background-color: black; }\n\n/**\n * +-----+\n * |     |\n * |  +--+\n * |     |\n * +-----+\n */\n.rst__lineHalfHorizontalRight::before {\n  height: 1px;\n  top: 50%;\n  right: 0;\n  width: 50%; }\n\n/**\n * +--+--+\n * |  |  |\n * |  |  |\n * |  |  |\n * +--+--+\n */\n.rst__lineFullVertical::after, .rst__lineHalfVerticalTop::after, .rst__lineHalfVerticalBottom::after {\n  width: 1px;\n  left: 50%;\n  top: 0;\n  height: 100%; }\n\n/**\n * +-----+\n * |  |  |\n * |  +  |\n * |     |\n * +-----+\n */\n.rst__lineHalfVerticalTop::after, .rst__lineHalfVerticalBottom::after {\n  top: 0;\n  height: 50%; }\n\n/**\n * +-----+\n * |     |\n * |  +  |\n * |  |  |\n * +-----+\n */\n.rst__lineHalfVerticalBottom::after {\n  top: auto;\n  bottom: 0; }\n\n/* Highlight line for pointing to dragged row destination\n   ========================================================================== */\n/**\n * +--+--+\n * |  |  |\n * |  |  |\n * |  |  |\n * +--+--+\n */\n.rst__highlightLineVertical {\n  z-index: 3; }\n  .rst__highlightLineVertical::before {\n    position: absolute;\n    content: '';\n    background-color: #36c2f6;\n    width: 8px;\n    margin-left: -4px;\n    left: 50%;\n    top: 0;\n    height: 100%; }\n\n@-webkit-keyframes rst__arrow-pulse {\n  0% {\n    -webkit-transform: translate(0, 0);\n            transform: translate(0, 0);\n    opacity: 0; }\n  30% {\n    -webkit-transform: translate(0, 300%);\n            transform: translate(0, 300%);\n    opacity: 1; }\n  70% {\n    -webkit-transform: translate(0, 700%);\n            transform: translate(0, 700%);\n    opacity: 1; }\n  100% {\n    -webkit-transform: translate(0, 1000%);\n            transform: translate(0, 1000%);\n    opacity: 0; } }\n\n@keyframes rst__arrow-pulse {\n  0% {\n    -webkit-transform: translate(0, 0);\n            transform: translate(0, 0);\n    opacity: 0; }\n  30% {\n    -webkit-transform: translate(0, 300%);\n            transform: translate(0, 300%);\n    opacity: 1; }\n  70% {\n    -webkit-transform: translate(0, 700%);\n            transform: translate(0, 700%);\n    opacity: 1; }\n  100% {\n    -webkit-transform: translate(0, 1000%);\n            transform: translate(0, 1000%);\n    opacity: 0; } }\n  .rst__highlightLineVertical::after {\n    content: '';\n    position: absolute;\n    height: 0;\n    margin-left: -4px;\n    left: 50%;\n    top: 0;\n    border-left: 4px solid transparent;\n    border-right: 4px solid transparent;\n    border-top: 4px solid white;\n    -webkit-animation: rst__arrow-pulse 1s infinite linear both;\n            animation: rst__arrow-pulse 1s infinite linear both; }\n\n/**\n * +-----+\n * |     |\n * |  +--+\n * |  |  |\n * +--+--+\n */\n.rst__highlightTopLeftCorner::before {\n  z-index: 3;\n  content: '';\n  position: absolute;\n  border-top: solid 8px #36c2f6;\n  border-left: solid 8px #36c2f6;\n  -webkit-box-sizing: border-box;\n          box-sizing: border-box;\n  height: calc(50% + 4px);\n  top: 50%;\n  margin-top: -4px;\n  right: 0;\n  width: calc(50% + 4px); }\n\n/**\n * +--+--+\n * |  |  |\n * |  |  |\n * |  +->|\n * +-----+\n */\n.rst__highlightBottomLeftCorner {\n  z-index: 3; }\n  .rst__highlightBottomLeftCorner::before {\n    content: '';\n    position: absolute;\n    border-bottom: solid 8px #36c2f6;\n    border-left: solid 8px #36c2f6;\n    -webkit-box-sizing: border-box;\n            box-sizing: border-box;\n    height: calc(100% + 4px);\n    top: 0;\n    right: 12px;\n    width: calc(50% - 8px); }\n  .rst__highlightBottomLeftCorner::after {\n    content: '';\n    position: absolute;\n    height: 0;\n    right: 0;\n    top: 100%;\n    margin-top: -12px;\n    border-top: 12px solid transparent;\n    border-bottom: 12px solid transparent;\n    border-left: 12px solid #36c2f6; }\n",""]),t.locals={node:"rst__node",nodeContent:"rst__nodeContent",lineBlock:"rst__lineBlock",absoluteLineBlock:"rst__absoluteLineBlock",lineHalfHorizontalRight:"rst__lineHalfHorizontalRight",lineFullVertical:"rst__lineFullVertical",lineHalfVerticalTop:"rst__lineHalfVerticalTop",lineHalfVerticalBottom:"rst__lineHalfVerticalBottom",highlightLineVertical:"rst__highlightLineVertical","arrow-pulse":"rst__arrow-pulse",highlightTopLeftCorner:"rst__highlightTopLeftCorner",highlightBottomLeftCorner:"rst__highlightBottomLeftCorner"}},function(e,t,n){"use strict";function r(e){return e&&e.__esModule?e:{default:e}}function o(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function i(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}Object.defineProperty(t,"__esModule",{value:!0});var a=function(){function e(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,n,r){return n&&e(t.prototype,n),r&&e(t,r),t}}(),l=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},s=n(1),c=r(s),u=r(n(2)),d=n(19),f=r(n(20)),h=n(0),p=f.default;d.getIEVersion<10&&(p=l({},f.default,{row:p.row+" "+p.row_NoFlex,rowContents:p.rowContents+" "+p.rowContents_NoFlex,rowLabel:p.rowLabel+" "+p.rowLabel_NoFlex,rowToolbar:p.rowToolbar+" "+p.rowToolbar_NoFlex}));var g=function(e){function t(){return o(this,t),i(this,(t.__proto__||Object.getPrototypeOf(t)).apply(this,arguments))}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(t,e),a(t,[{key:"render",value:function(){var e=this.props,t=e.scaffoldBlockPxWidth,n=e.toggleChildrenVisibility,r=e.connectDragPreview,o=e.connectDragSource,i=e.isDragging,a=e.canDrop,s=e.canDrag,u=e.node,d=e.title,f=e.subtitle,g=e.draggedNode,v=e.path,m=e.treeIndex,y=e.isSearchMatch,_=e.isSearchFocus,b=e.buttons,w=e.className,S=e.style,x=e.didDrop,C=(e.treeId,e.isOver,e.parentNode,function(e,t){var n={};for(var r in e)t.indexOf(r)>=0||Object.prototype.hasOwnProperty.call(e,r)&&(n[r]=e[r]);return n}(e,["scaffoldBlockPxWidth","toggleChildrenVisibility","connectDragPreview","connectDragSource","isDragging","canDrop","canDrag","node","title","subtitle","draggedNode","path","treeIndex","isSearchMatch","isSearchFocus","buttons","className","style","didDrop","treeId","isOver","parentNode"])),O=d||u.title,T=f||u.subtitle,I=void 0;s&&(I="function"==typeof u.children&&u.expanded?c.default.createElement("div",{className:p.loadingHandle},c.default.createElement("div",{className:p.loadingCircle},c.default.createElement("div",{className:p.loadingCirclePoint}),c.default.createElement("div",{className:p.loadingCirclePoint}),c.default.createElement("div",{className:p.loadingCirclePoint}),c.default.createElement("div",{className:p.loadingCirclePoint}),c.default.createElement("div",{className:p.loadingCirclePoint}),c.default.createElement("div",{className:p.loadingCirclePoint}),c.default.createElement("div",{className:p.loadingCirclePoint}),c.default.createElement("div",{className:p.loadingCirclePoint}),c.default.createElement("div",{className:p.loadingCirclePoint}),c.default.createElement("div",{className:p.loadingCirclePoint}),c.default.createElement("div",{className:p.loadingCirclePoint}),c.default.createElement("div",{className:p.loadingCirclePoint}))):o(c.default.createElement("div",{className:p.moveHandle}),{dropEffect:"copy"}));var D=g&&(0,h.isDescendant)(g,u),k=!x&&i;return c.default.createElement("div",l({style:{height:"100%"}},C),n&&u.children&&(u.children.length>0||"function"==typeof u.children)&&c.default.createElement("div",null,c.default.createElement("button",{type:"button","aria-label":u.expanded?"Collapse":"Expand",className:u.expanded?p.collapseButton:p.expandButton,style:{left:-.5*t},onClick:function(){return n({node:u,path:v,treeIndex:m})}}),u.expanded&&!i&&c.default.createElement("div",{style:{width:t},className:p.lineChildren})),c.default.createElement("div",{className:p.rowWrapper},r(c.default.createElement("div",{className:p.row+(k?" "+p.rowLandingPad:"")+(k&&!a?" "+p.rowCancelPad:"")+(y?" "+p.rowSearchMatch:"")+(_?" "+p.rowSearchFocus:"")+(w?" "+w:""),style:l({opacity:D?.5:1},S)},I,c.default.createElement("div",{className:p.rowContents+(s?"":" "+p.rowContentsDragDisabled)},c.default.createElement("div",{className:p.rowLabel},c.default.createElement("span",{className:p.rowTitle+(u.subtitle?" "+p.rowTitleWithSubtitle:"")},"function"==typeof O?O({node:u,path:v,treeIndex:m}):O),T&&c.default.createElement("span",{className:p.rowSubtitle},"function"==typeof T?T({node:u,path:v,treeIndex:m}):T)),c.default.createElement("div",{className:p.rowToolbar},b.map((function(e,t){return c.default.createElement("div",{key:t,className:p.toolbarButton},e)}))))))))}}]),t}(s.Component);g.defaultProps={isSearchMatch:!1,isSearchFocus:!1,canDrag:!1,toggleChildrenVisibility:null,buttons:[],className:"",style:{},parentNode:null,draggedNode:null,canDrop:!1,title:null,subtitle:null},g.propTypes={node:u.default.shape({}).isRequired,title:u.default.oneOfType([u.default.func,u.default.node]),subtitle:u.default.oneOfType([u.default.func,u.default.node]),path:u.default.arrayOf(u.default.oneOfType([u.default.string,u.default.number])).isRequired,treeIndex:u.default.number.isRequired,treeId:u.default.string.isRequired,isSearchMatch:u.default.bool,isSearchFocus:u.default.bool,canDrag:u.default.bool,scaffoldBlockPxWidth:u.default.number.isRequired,toggleChildrenVisibility:u.default.func,buttons:u.default.arrayOf(u.default.node),className:u.default.string,style:u.default.shape({}),connectDragPreview:u.default.func.isRequired,connectDragSource:u.default.func.isRequired,parentNode:u.default.shape({}),isDragging:u.default.bool.isRequired,didDrop:u.default.bool.isRequired,draggedNode:u.default.shape({}),isOver:u.default.bool.isRequired,canDrop:u.default.bool},t.default=g},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.getIEVersion=function(){var e=navigator.userAgent.match(/(?:MSIE |Trident\/.*; rv:)(\d+)/);return e?parseInt(e[1],10):void 0}},function(e,t,n){var r=n(21);"string"==typeof r&&(r=[[e.i,r,""]]);n(4)(r,{insertAt:"top",hmr:!0,transform:void 0}),r.locals&&(e.exports=r.locals)},function(e,t,n){(t=e.exports=n(3)(!1)).push([e.i,".rst__rowWrapper {\n  padding: 10px 10px 10px 0;\n  height: 100%;\n  -webkit-box-sizing: border-box;\n          box-sizing: border-box; }\n\n.rst__row {\n  height: 100%;\n  white-space: nowrap;\n  display: -webkit-box;\n  display: -ms-flexbox;\n  display: flex; }\n  .rst__row > * {\n    -webkit-box-sizing: border-box;\n            box-sizing: border-box; }\n\n/**\n * The outline of where the element will go if dropped, displayed while dragging\n */\n.rst__rowLandingPad, .rst__rowCancelPad {\n  border: none !important;\n  -webkit-box-shadow: none !important;\n          box-shadow: none !important;\n  outline: none !important; }\n  .rst__rowLandingPad > *, .rst__rowCancelPad > * {\n    opacity: 0 !important; }\n  .rst__rowLandingPad::before, .rst__rowCancelPad::before {\n    background-color: lightblue;\n    border: 3px dashed white;\n    content: '';\n    position: absolute;\n    top: 0;\n    right: 0;\n    bottom: 0;\n    left: 0;\n    z-index: -1; }\n\n/**\n * Alternate appearance of the landing pad when the dragged location is invalid\n */\n.rst__rowCancelPad::before {\n  background-color: #e6a8ad; }\n\n/**\n * Nodes matching the search conditions are highlighted\n */\n.rst__rowSearchMatch {\n  outline: solid 3px #0080ff; }\n\n/**\n * The node that matches the search conditions and is currently focused\n */\n.rst__rowSearchFocus {\n  outline: solid 3px #fc6421; }\n\n.rst__rowContents, .rst__rowLabel, .rst__rowToolbar, .rst__moveHandle, .rst__loadingHandle, .rst__toolbarButton, .rst__rowLabel_NoFlex, .rst__rowToolbar_NoFlex {\n  display: inline-block;\n  vertical-align: middle; }\n\n.rst__rowContents {\n  position: relative;\n  height: 100%;\n  border: solid #bbb 1px;\n  border-left: none;\n  -webkit-box-shadow: 0 2px 2px -2px;\n          box-shadow: 0 2px 2px -2px;\n  padding: 0 5px 0 10px;\n  border-radius: 2px;\n  min-width: 230px;\n  -webkit-box-flex: 1;\n      -ms-flex: 1 0 auto;\n          flex: 1 0 auto;\n  display: -webkit-box;\n  display: -ms-flexbox;\n  display: flex;\n  -webkit-box-align: center;\n      -ms-flex-align: center;\n          align-items: center;\n  -webkit-box-pack: justify;\n      -ms-flex-pack: justify;\n          justify-content: space-between;\n  background-color: white; }\n\n.rst__rowContentsDragDisabled {\n  border-left: solid #bbb 1px; }\n\n.rst__rowLabel {\n  -webkit-box-flex: 0;\n      -ms-flex: 0 1 auto;\n          flex: 0 1 auto;\n  padding-right: 20px; }\n\n.rst__rowToolbar {\n  -webkit-box-flex: 0;\n      -ms-flex: 0 1 auto;\n          flex: 0 1 auto;\n  display: -webkit-box;\n  display: -ms-flexbox;\n  display: flex; }\n\n.rst__moveHandle, .rst__loadingHandle {\n  height: 100%;\n  width: 44px;\n  background: #d9d9d9 url(\"data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSI0MiIgaGVpZ2h0PSI0MiI+PGcgc3Ryb2tlPSIjRkZGIiBzdHJva2Utd2lkdGg9IjIuOSIgPjxwYXRoIGQ9Ik0xNCAxNS43aDE0LjQiLz48cGF0aCBkPSJNMTQgMjEuNGgxNC40Ii8+PHBhdGggZD0iTTE0IDI3LjFoMTQuNCIvPjwvZz4KPC9zdmc+\") no-repeat center;\n  border: solid #aaa 1px;\n  -webkit-box-shadow: 0 2px 2px -2px;\n          box-shadow: 0 2px 2px -2px;\n  cursor: move;\n  border-radius: 1px;\n  z-index: 1; }\n\n.rst__loadingHandle {\n  cursor: default;\n  background: #d9d9d9; }\n\n@-webkit-keyframes rst__pointFade {\n  0%,\n  19.999%,\n  100% {\n    opacity: 0; }\n  20% {\n    opacity: 1; } }\n\n@keyframes rst__pointFade {\n  0%,\n  19.999%,\n  100% {\n    opacity: 0; }\n  20% {\n    opacity: 1; } }\n\n.rst__loadingCircle {\n  width: 80%;\n  height: 80%;\n  margin: 10%;\n  position: relative; }\n\n.rst__loadingCirclePoint {\n  width: 100%;\n  height: 100%;\n  position: absolute;\n  left: 0;\n  top: 0; }\n  .rst__loadingCirclePoint:before {\n    content: '';\n    display: block;\n    margin: 0 auto;\n    width: 11%;\n    height: 30%;\n    background-color: #fff;\n    border-radius: 30%;\n    -webkit-animation: rst__pointFade 800ms infinite ease-in-out both;\n            animation: rst__pointFade 800ms infinite ease-in-out both; }\n  .rst__loadingCirclePoint:nth-of-type(1) {\n    -webkit-transform: rotate(0deg);\n        -ms-transform: rotate(0deg);\n            transform: rotate(0deg); }\n  .rst__loadingCirclePoint:nth-of-type(7) {\n    -webkit-transform: rotate(180deg);\n        -ms-transform: rotate(180deg);\n            transform: rotate(180deg); }\n  .rst__loadingCirclePoint:nth-of-type(1):before, .rst__loadingCirclePoint:nth-of-type(7):before {\n    -webkit-animation-delay: -800ms;\n            animation-delay: -800ms; }\n  .rst__loadingCirclePoint:nth-of-type(2) {\n    -webkit-transform: rotate(30deg);\n        -ms-transform: rotate(30deg);\n            transform: rotate(30deg); }\n  .rst__loadingCirclePoint:nth-of-type(8) {\n    -webkit-transform: rotate(210deg);\n        -ms-transform: rotate(210deg);\n            transform: rotate(210deg); }\n  .rst__loadingCirclePoint:nth-of-type(2):before, .rst__loadingCirclePoint:nth-of-type(8):before {\n    -webkit-animation-delay: -666.66667ms;\n            animation-delay: -666.66667ms; }\n  .rst__loadingCirclePoint:nth-of-type(3) {\n    -webkit-transform: rotate(60deg);\n        -ms-transform: rotate(60deg);\n            transform: rotate(60deg); }\n  .rst__loadingCirclePoint:nth-of-type(9) {\n    -webkit-transform: rotate(240deg);\n        -ms-transform: rotate(240deg);\n            transform: rotate(240deg); }\n  .rst__loadingCirclePoint:nth-of-type(3):before, .rst__loadingCirclePoint:nth-of-type(9):before {\n    -webkit-animation-delay: -533.33333ms;\n            animation-delay: -533.33333ms; }\n  .rst__loadingCirclePoint:nth-of-type(4) {\n    -webkit-transform: rotate(90deg);\n        -ms-transform: rotate(90deg);\n            transform: rotate(90deg); }\n  .rst__loadingCirclePoint:nth-of-type(10) {\n    -webkit-transform: rotate(270deg);\n        -ms-transform: rotate(270deg);\n            transform: rotate(270deg); }\n  .rst__loadingCirclePoint:nth-of-type(4):before, .rst__loadingCirclePoint:nth-of-type(10):before {\n    -webkit-animation-delay: -400ms;\n            animation-delay: -400ms; }\n  .rst__loadingCirclePoint:nth-of-type(5) {\n    -webkit-transform: rotate(120deg);\n        -ms-transform: rotate(120deg);\n            transform: rotate(120deg); }\n  .rst__loadingCirclePoint:nth-of-type(11) {\n    -webkit-transform: rotate(300deg);\n        -ms-transform: rotate(300deg);\n            transform: rotate(300deg); }\n  .rst__loadingCirclePoint:nth-of-type(5):before, .rst__loadingCirclePoint:nth-of-type(11):before {\n    -webkit-animation-delay: -266.66667ms;\n            animation-delay: -266.66667ms; }\n  .rst__loadingCirclePoint:nth-of-type(6) {\n    -webkit-transform: rotate(150deg);\n        -ms-transform: rotate(150deg);\n            transform: rotate(150deg); }\n  .rst__loadingCirclePoint:nth-of-type(12) {\n    -webkit-transform: rotate(330deg);\n        -ms-transform: rotate(330deg);\n            transform: rotate(330deg); }\n  .rst__loadingCirclePoint:nth-of-type(6):before, .rst__loadingCirclePoint:nth-of-type(12):before {\n    -webkit-animation-delay: -133.33333ms;\n            animation-delay: -133.33333ms; }\n  .rst__loadingCirclePoint:nth-of-type(7) {\n    -webkit-transform: rotate(180deg);\n        -ms-transform: rotate(180deg);\n            transform: rotate(180deg); }\n  .rst__loadingCirclePoint:nth-of-type(13) {\n    -webkit-transform: rotate(360deg);\n        -ms-transform: rotate(360deg);\n            transform: rotate(360deg); }\n  .rst__loadingCirclePoint:nth-of-type(7):before, .rst__loadingCirclePoint:nth-of-type(13):before {\n    -webkit-animation-delay: 0ms;\n            animation-delay: 0ms; }\n\n.rst__rowTitle {\n  font-weight: bold; }\n\n.rst__rowTitleWithSubtitle {\n  font-size: 85%;\n  display: block;\n  height: 0.8rem; }\n\n.rst__rowSubtitle {\n  font-size: 70%;\n  line-height: 1; }\n\n.rst__collapseButton,\n.rst__expandButton {\n  -webkit-appearance: none;\n     -moz-appearance: none;\n          appearance: none;\n  border: none;\n  position: absolute;\n  border-radius: 100%;\n  -webkit-box-shadow: 0 0 0 1px #000;\n          box-shadow: 0 0 0 1px #000;\n  width: 16px;\n  height: 16px;\n  padding: 0;\n  top: 50%;\n  -webkit-transform: translate(-50%, -50%);\n      -ms-transform: translate(-50%, -50%);\n          transform: translate(-50%, -50%);\n  cursor: pointer; }\n  .rst__collapseButton:focus,\n  .rst__expandButton:focus {\n    outline: none;\n    -webkit-box-shadow: 0 0 0 1px #000, 0 0 1px 3px #83bef9;\n            box-shadow: 0 0 0 1px #000, 0 0 1px 3px #83bef9; }\n  .rst__collapseButton:hover:not(:active),\n  .rst__expandButton:hover:not(:active) {\n    background-size: 24px;\n    height: 20px;\n    width: 20px; }\n\n.rst__collapseButton {\n  background: #fff url(\"data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxOCIgaGVpZ2h0PSIxOCI+PGNpcmNsZSBjeD0iOSIgY3k9IjkiIHI9IjgiIGZpbGw9IiNGRkYiLz48ZyBzdHJva2U9IiM5ODk4OTgiIHN0cm9rZS13aWR0aD0iMS45IiA+PHBhdGggZD0iTTQuNSA5aDkiLz48L2c+Cjwvc3ZnPg==\") no-repeat center; }\n\n.rst__expandButton {\n  background: #fff url(\"data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxOCIgaGVpZ2h0PSIxOCI+PGNpcmNsZSBjeD0iOSIgY3k9IjkiIHI9IjgiIGZpbGw9IiNGRkYiLz48ZyBzdHJva2U9IiM5ODk4OTgiIHN0cm9rZS13aWR0aD0iMS45IiA+PHBhdGggZD0iTTQuNSA5aDkiLz48cGF0aCBkPSJNOSA0LjV2OSIvPjwvZz4KPC9zdmc+\") no-repeat center; }\n\n/**\n  * Classes for IE9 and below\n  */\n.rst__row_NoFlex::before, .rst__rowContents_NoFlex::before {\n  content: '';\n  display: inline-block;\n  vertical-align: middle;\n  height: 100%; }\n\n.rst__rowContents_NoFlex {\n  display: inline-block; }\n  .rst__rowContents_NoFlex::after {\n    content: '';\n    display: inline-block;\n    width: 100%; }\n\n.rst__rowLabel_NoFlex {\n  width: 50%; }\n\n.rst__rowToolbar_NoFlex {\n  text-align: right;\n  width: 50%; }\n\n/**\n * Line for under a node with children\n */\n.rst__lineChildren {\n  height: 100%;\n  display: inline-block;\n  position: absolute; }\n  .rst__lineChildren::after {\n    content: '';\n    position: absolute;\n    background-color: black;\n    width: 1px;\n    left: 50%;\n    bottom: 0;\n    height: 10px; }\n",""]),t.locals={rowWrapper:"rst__rowWrapper",row:"rst__row",rowLandingPad:"rst__rowLandingPad",rowCancelPad:"rst__rowCancelPad",rowSearchMatch:"rst__rowSearchMatch",rowSearchFocus:"rst__rowSearchFocus",rowContents:"rst__rowContents",rowLabel:"rst__rowLabel",rowToolbar:"rst__rowToolbar",moveHandle:"rst__moveHandle",loadingHandle:"rst__loadingHandle",toolbarButton:"rst__toolbarButton",rowLabel_NoFlex:"rst__rowLabel_NoFlex",rowToolbar_NoFlex:"rst__rowToolbar_NoFlex",rowContentsDragDisabled:"rst__rowContentsDragDisabled",loadingCircle:"rst__loadingCircle",loadingCirclePoint:"rst__loadingCirclePoint",pointFade:"rst__pointFade",rowTitle:"rst__rowTitle",rowTitleWithSubtitle:"rst__rowTitleWithSubtitle",rowSubtitle:"rst__rowSubtitle",collapseButton:"rst__collapseButton",expandButton:"rst__expandButton",row_NoFlex:"rst__row_NoFlex",rowContents_NoFlex:"rst__rowContents_NoFlex",lineChildren:"rst__lineChildren"}},function(e,t,n){"use strict";function r(e){return e&&e.__esModule?e:{default:e}}function o(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function i(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}Object.defineProperty(t,"__esModule",{value:!0});var a=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},l=function(){function e(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,n,r){return n&&e(t.prototype,n),r&&e(t,r),t}}(),s=n(1),c=r(s),u=r(n(2)),d=function(e){function t(){return o(this,t),i(this,(t.__proto__||Object.getPrototypeOf(t)).apply(this,arguments))}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(t,e),l(t,[{key:"render",value:function(){var e=this.props,t=e.children,n=e.connectDropTarget,r=(e.treeId,e.drop,function(e,t){var n={};for(var r in e)t.indexOf(r)>=0||Object.prototype.hasOwnProperty.call(e,r)&&(n[r]=e[r]);return n}(e,["children","connectDropTarget","treeId","drop"]));return n(c.default.createElement("div",null,s.Children.map(t,(function(e){return(0,s.cloneElement)(e,a({},r))}))))}}]),t}(s.Component);d.defaultProps={canDrop:!1,draggedNode:null},d.propTypes={children:u.default.node.isRequired,connectDropTarget:u.default.func.isRequired,isOver:u.default.bool.isRequired,canDrop:u.default.bool,draggedNode:u.default.shape({}),treeId:u.default.string.isRequired,drop:u.default.func.isRequired},t.default=d},function(e,t,n){"use strict";function r(e){return e&&e.__esModule?e:{default:e}}Object.defineProperty(t,"__esModule",{value:!0});var o=r(n(1)),i=r(n(2)),a=r(n(24)),l=function(e){var t=e.isOver,n=e.canDrop;return o.default.createElement("div",{className:a.default.placeholder+(n?" "+a.default.placeholderLandingPad:"")+(n&&!t?" "+a.default.placeholderCancelPad:"")})};l.defaultProps={isOver:!1,canDrop:!1},l.propTypes={isOver:i.default.bool,canDrop:i.default.bool},t.default=l},function(e,t,n){var r=n(25);"string"==typeof r&&(r=[[e.i,r,""]]);n(4)(r,{insertAt:"top",hmr:!0,transform:void 0}),r.locals&&(e.exports=r.locals)},function(e,t,n){(t=e.exports=n(3)(!1)).push([e.i,".rst__placeholder {\n  position: relative;\n  height: 68px;\n  max-width: 300px;\n  padding: 10px; }\n  .rst__placeholder,\n  .rst__placeholder > * {\n    -webkit-box-sizing: border-box;\n            box-sizing: border-box; }\n  .rst__placeholder::before {\n    border: 3px dashed #d9d9d9;\n    content: '';\n    position: absolute;\n    top: 5px;\n    right: 5px;\n    bottom: 5px;\n    left: 5px;\n    z-index: -1; }\n\n/**\n * The outline of where the element will go if dropped, displayed while dragging\n */\n.rst__placeholderLandingPad, .rst__placeholderCancelPad {\n  border: none !important;\n  -webkit-box-shadow: none !important;\n          box-shadow: none !important;\n  outline: none !important; }\n  .rst__placeholderLandingPad *, .rst__placeholderCancelPad * {\n    opacity: 0 !important; }\n  .rst__placeholderLandingPad::before, .rst__placeholderCancelPad::before {\n    background-color: lightblue;\n    border-color: white; }\n\n/**\n * Alternate appearance of the landing pad when the dragged location is invalid\n */\n.rst__placeholderCancelPad::before {\n  background-color: #e6a8ad; }\n",""]),t.locals={placeholder:"rst__placeholder",placeholderLandingPad:"rst__placeholderLandingPad",placeholderCancelPad:"rst__placeholderCancelPad"}},function(e,t,n){"use strict";function r(e){if(Array.isArray(e)){for(var t=0,n=Array(e.length);t<e.length;t++)n[t]=e[t];return n}return Array.from(e)}Object.defineProperty(t,"__esModule",{value:!0}),t.slideRows=function(e,t,n){var o=arguments.length>3&&void 0!==arguments[3]?arguments[3]:1,i=[].concat(r(e.slice(0,t)),r(e.slice(t+o)));return[].concat(r(i.slice(0,n)),r(e.slice(t,t+o)),r(i.slice(n)))}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r,o=function(){function e(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,n,r){return n&&e(t.prototype,n),r&&e(t,r),t}}(),i=n(28),a=(r=n(29))&&r.__esModule?r:{default:r},l=n(30),s=n(0),c=n(6),u=function(){function e(t){(function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")})(this,e),this.treeRef=t}return o(e,[{key:"getTargetDepth",value:function(e,t,n){var r=0,o=e.getPrevRow();o&&(r=Math.min(o.path.length,e.path.length));var i=void 0,a=(t.getItem().path||[]).length;if(t.getItem().treeId!==this.treeId)if(a=0,n){var c=(0,l.findDOMNode)(n).getBoundingClientRect(),u=t.getSourceClientOffset().x-c.left;i=Math.round(u/e.scaffoldBlockPxWidth)}else i=e.path.length;else i=Math.round(t.getDifferenceFromInitialOffset().x/e.scaffoldBlockPxWidth);var d=Math.min(r,Math.max(0,a+i-1));if(void 0!==this.maxDepth&&null!==this.maxDepth){var f=t.getItem().node,h=(0,s.getDepth)(f);d=Math.max(0,Math.min(d,this.maxDepth-h-1))}return d}},{key:"canDrop",value:function(e,t){if(!t.isOver())return!1;var n=e.getPrevRow(),r=n?n.path:[],o=n?n.node:{},i=this.getTargetDepth(e,t,null);if(i>=r.length&&"function"==typeof o.children)return!1;if("function"==typeof this.customCanDrop){var a=t.getItem().node,l=(0,c.memoizedInsertNode)({treeData:this.treeData,newNode:a,depth:i,getNodeKey:this.getNodeKey,minimumTreeIndex:e.listIndex,expandParent:!0});return this.customCanDrop({node:a,prevPath:t.getItem().path,prevParent:t.getItem().parentNode,prevTreeIndex:t.getItem().treeIndex,nextPath:l.path,nextParent:l.parentNode,nextTreeIndex:l.treeIndex})}return!0}},{key:"wrapSource",value:function(e){var t=this,n={beginDrag:function(e){return t.startDrag(e),{node:e.node,parentNode:e.parentNode,path:e.path,treeIndex:e.treeIndex,treeId:e.treeId}},endDrag:function(e,n){t.endDrag(n.getDropResult())},isDragging:function(e,t){var n=t.getItem().node;return e.node===n}};return(0,i.DragSource)(this.dndType,n,(function(e,t){return{connectDragSource:e.dragSource(),connectDragPreview:e.dragPreview(),isDragging:t.isDragging(),didDrop:t.didDrop()}}))(e)}},{key:"wrapTarget",value:function(e){var t=this,n={drop:function(e,n,r){var o={node:n.getItem().node,path:n.getItem().path,treeIndex:n.getItem().treeIndex,treeId:t.treeId,minimumTreeIndex:e.treeIndex,depth:t.getTargetDepth(e,n,r)};return t.drop(o),o},hover:function(e,n,r){var o=t.getTargetDepth(e,n,r),i=n.getItem().node;(e.node!==i||o!==e.path.length-1)&&t.dragHover({node:i,path:n.getItem().path,minimumTreeIndex:e.listIndex,depth:o})},canDrop:this.canDrop.bind(this)};return(0,i.DropTarget)(this.dndType,n,(function(e,t){var n=t.getItem();return{connectDropTarget:e.dropTarget(),isOver:t.isOver(),canDrop:t.canDrop(),draggedNode:n?n.node:null}}))(e)}},{key:"wrapPlaceholder",value:function(e){var t=this,n={drop:function(e,n){var r=n.getItem(),o={node:r.node,path:r.path,treeIndex:r.treeIndex,treeId:t.treeId,minimumTreeIndex:0,depth:0};return t.drop(o),o}};return(0,i.DropTarget)(this.dndType,n,(function(e,t){var n=t.getItem();return{connectDropTarget:e.dropTarget(),isOver:t.isOver(),canDrop:t.canDrop(),draggedNode:n?n.node:null}}))(e)}},{key:"startDrag",get:function(){return this.treeRef.startDrag}},{key:"dragHover",get:function(){return this.treeRef.dragHover}},{key:"endDrag",get:function(){return this.treeRef.endDrag}},{key:"drop",get:function(){return this.treeRef.drop}},{key:"treeId",get:function(){return this.treeRef.treeId}},{key:"dndType",get:function(){return this.treeRef.dndType}},{key:"treeData",get:function(){return this.treeRef.state.draggingTreeData||this.treeRef.props.treeData}},{key:"getNodeKey",get:function(){return this.treeRef.props.getNodeKey}},{key:"customCanDrop",get:function(){return this.treeRef.props.canDrop}},{key:"maxDepth",get:function(){return this.treeRef.props.maxDepth}}],[{key:"wrapRoot",value:function(e){return(0,i.DragDropContext)(a.default)(e)}}]),e}();t.default=u},function(e,t){e.exports=n(256)},function(e,t){e.exports=n(270)},function(e,t){e.exports=n(13)},function(e,t,n){var r=n(32);"string"==typeof r&&(r=[[e.i,r,""]]);n(4)(r,{insertAt:"top",hmr:!0,transform:void 0}),r.locals&&(e.exports=r.locals)},function(e,t,n){(t=e.exports=n(3)(!1)).push([e.i,"/**\n  * The container holding the VirtualScroll\n  */\n.rst__tree {\n  /*! This comment keeps Sass from deleting the empty rule */ }\n\n/**\n * Extra class applied to VirtualScroll through className prop\n */\n.rst__virtualScrollOverride {\n  overflow: auto !important; }\n  .rst__virtualScrollOverride * {\n    -webkit-box-sizing: border-box;\n            box-sizing: border-box; }\n\n.ReactVirtualized__Grid__innerScrollContainer {\n  overflow: visible !important; }\n\n.ReactVirtualized__Grid {\n  outline: none; }\n",""]),t.locals={tree:"rst__tree",virtualScrollOverride:"rst__virtualScrollOverride"}}])},function(e,t,n){var r=n(46),o=n(117),i=n(118),a=r?r.toStringTag:void 0;e.exports=function(e){return null==e?void 0===e?"[object Undefined]":"[object Null]":a&&a in Object(e)?o(e):i(e)}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.END_DRAG=t.DROP=t.HOVER=t.PUBLISH_DRAG_SOURCE=t.BEGIN_DRAG=void 0;var r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e};t.beginDrag=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{publishSource:!0,clientOffset:null},n=t.publishSource,r=t.clientOffset,l=t.getSourceClientOffset;(0,o.default)((0,i.default)(e),"Expected sourceIds to be an array.");var s=this.getMonitor(),u=this.getRegistry();(0,o.default)(!s.isDragging(),"Cannot call beginDrag while dragging.");for(var d=0;d<e.length;d++)(0,o.default)(u.getSource(e[d]),"Expected sourceIds to be registered.");for(var f=null,h=e.length-1;h>=0;h--)if(s.canDragSource(e[h])){f=e[h];break}if(null!==f){var p=null;r&&((0,o.default)("function"==typeof l,"When clientOffset is provided, getSourceClientOffset must be a function."),p=l(f));var g=u.getSource(f),v=g.beginDrag(s,f);(0,o.default)((0,a.default)(v),"Item must be an object."),u.pinSource(f);var m=u.getSourceType(f);return{type:c,itemType:m,item:v,sourceId:f,clientOffset:r,sourceClientOffset:p,isSourcePublic:n}}},t.publishDragSource=function(){if(this.getMonitor().isDragging())return{type:u}},t.hover=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=t.clientOffset,r=void 0===n?null:n;(0,o.default)((0,i.default)(e),"Expected targetIds to be an array.");var a=e.slice(0),s=this.getMonitor(),c=this.getRegistry();(0,o.default)(s.isDragging(),"Cannot call hover while not dragging."),(0,o.default)(!s.didDrop(),"Cannot call hover after drop.");for(var u=0;u<a.length;u++){var f=a[u];(0,o.default)(a.lastIndexOf(f)===u,"Expected targetIds to be unique in the passed array.");var h=c.getTarget(f);(0,o.default)(h,"Expected targetIds to be registered.")}for(var p=s.getItemType(),g=a.length-1;g>=0;g--){var v=a[g],m=c.getTargetType(v);(0,l.default)(m,p)||a.splice(g,1)}for(var y=0;y<a.length;y++){var _=a[y],b=c.getTarget(_);b.hover(s,_)}return{type:d,targetIds:a,clientOffset:r}},t.drop=function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=this.getMonitor(),i=this.getRegistry();(0,o.default)(n.isDragging(),"Cannot call drop while not dragging."),(0,o.default)(!n.didDrop(),"Cannot call drop twice during one drag operation.");var l=n.getTargetIds().filter(n.canDropOnTarget,n);l.reverse(),l.forEach((function(l,s){var c=i.getTarget(l).drop(n,l);(0,o.default)(void 0===c||(0,a.default)(c),"Drop result must either be an object or undefined."),void 0===c&&(c=0===s?{}:n.getDropResult()),e.store.dispatch({type:f,dropResult:r({},t,c)})}))},t.endDrag=function(){var e=this.getMonitor(),t=this.getRegistry();(0,o.default)(e.isDragging(),"Cannot call endDrag while not dragging.");var n=e.getSourceId();return t.getSource(n,!0).endDrag(e,n),t.unpinSource(),{type:h}};var o=s(n(14)),i=s(n(20)),a=s(n(27)),l=s(n(67));function s(e){return e&&e.__esModule?e:{default:e}}var c=t.BEGIN_DRAG="dnd-core/BEGIN_DRAG",u=t.PUBLISH_DRAG_SOURCE="dnd-core/PUBLISH_DRAG_SOURCE",d=t.HOVER="dnd-core/HOVER",f=t.DROP="dnd-core/DROP",h=t.END_DRAG="dnd-core/END_DRAG"},function(e,t,n){var r=n(37)(Object,"create");e.exports=r},function(e,t,n){var r=n(128),o=n(132);e.exports=function(e,t){var n=o(e,t);return r(n)?n:void 0}},function(e,t,n){var r=n(48);e.exports=function(e,t){for(var n=e.length;n--;)if(r(e[n][0],t))return n;return-1}},function(e,t,n){var r=n(145);e.exports=function(e,t){var n=e.__data__;return r(t)?n["string"==typeof t?"string":"hash"]:n.map}},function(e,t,n){var r=n(54),o=n(26);e.exports=function(e){return o(e)&&r(e)}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.addSource=function(e){return{type:r,sourceId:e}},t.addTarget=function(e){return{type:o,targetId:e}},t.removeSource=function(e){return{type:i,sourceId:e}},t.removeTarget=function(e){return{type:a,targetId:e}};var r=t.ADD_SOURCE="dnd-core/ADD_SOURCE",o=t.ADD_TARGET="dnd-core/ADD_TARGET",i=t.REMOVE_SOURCE="dnd-core/REMOVE_SOURCE",a=t.REMOVE_TARGET="dnd-core/REMOVE_TARGET"},function(e,t,n){"use strict";function r(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"text";e.nonce||(e.nonce=window.cf7svisual.nonce);var n=Object.keys(e).map((function(t){return encodeURIComponent(t)+"="+encodeURIComponent(e[t])})).join("&"),r={method:"POST",credentials:"same-origin",headers:{"Content-Type":"application/x-www-form-urlencoded; charset=UTF-8"},body:n},o=fetch(window.cf7svisual.ajaxurl,r).then((function(e){return"json"===t?e.json():e.text()})).then((function(e){if(e)return e;throw new Error("Error. Returned data: ".concat(e))})).catch((function(e){console.error("Error: ".concat(e))}));return Promise.resolve(o)}n.d(t,"a",(function(){return r})),n(11)},function(e,t,n){var r=n(16).default;e.exports=function(e,t){if("object"!=r(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var o=n.call(e,t||"default");if("object"!=r(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)},e.exports.__esModule=!0,e.exports.default=e.exports},,,function(e,t,n){var r=n(25).Symbol;e.exports=r},function(e,t,n){var r=n(69),o=n(149),i=n(150);function a(e){var t=-1,n=null==e?0:e.length;for(this.__data__=new r;++t<n;)this.add(e[t])}a.prototype.add=a.prototype.push=o,a.prototype.has=i,e.exports=a},function(e,t){e.exports=function(e,t){return e===t||e!=e&&t!=t}},function(e,t,n){var r=n(151);e.exports=function(e,t){return!(null==e||!e.length)&&r(e,t,0)>-1}},function(e,t){e.exports=function(e,t,n){for(var r=-1,o=null==e?0:e.length;++r<o;)if(n(t,e[r]))return!0;return!1}},function(e,t){e.exports=function(e,t){for(var n=-1,r=null==e?0:e.length,o=Array(r);++n<r;)o[n]=t(e[n],n,e);return o}},function(e,t){e.exports=function(e){return function(t){return e(t)}}},function(e,t){e.exports=function(e,t){return e.has(t)}},function(e,t,n){var r=n(70),o=n(72);e.exports=function(e){return null!=e&&o(e.length)&&!r(e)}},function(e,t,n){"use strict";t.__esModule=!0,t.default=function(e){return Boolean(e&&"function"==typeof e.dispose)},e.exports=t.default},function(e,t){e.exports=function(e){return e.webpackPolyfill||(e.deprecate=function(){},e.paths=[],e.children||(e.children=[]),Object.defineProperty(e,"loaded",{enumerable:!0,get:function(){return e.l}}),Object.defineProperty(e,"id",{enumerable:!0,get:function(){return e.i}}),e.webpackPolyfill=1),e}},function(e,t){function n(t,r){return e.exports=n=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},e.exports.__esModule=!0,e.exports.default=e.exports,n(t,r)}e.exports=n,e.exports.__esModule=!0,e.exports.default=e.exports},function(e,t,n){var r=n(68),o=n(28),i=n(40),a=o((function(e,t){return i(e)?r(e,t):[]}));e.exports=a},,function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e,t){}},function(e,t,n){"use strict";(function(e){function r(t,n){var r,o,i,a=void 0!==(r=void 0!==n?n:"undefined"!=typeof window?window:"undefined"!=typeof self?self:e).document&&r.document.attachEvent;if(!a){var l=(i=r.requestAnimationFrame||r.mozRequestAnimationFrame||r.webkitRequestAnimationFrame||function(e){return r.setTimeout(e,20)},function(e){return i(e)}),s=(o=r.cancelAnimationFrame||r.mozCancelAnimationFrame||r.webkitCancelAnimationFrame||r.clearTimeout,function(e){return o(e)}),c=function(e){var t=e.__resizeTriggers__,n=t.firstElementChild,r=t.lastElementChild,o=n.firstElementChild;r.scrollLeft=r.scrollWidth,r.scrollTop=r.scrollHeight,o.style.width=n.offsetWidth+1+"px",o.style.height=n.offsetHeight+1+"px",n.scrollLeft=n.scrollWidth,n.scrollTop=n.scrollHeight},u=function(e){if(!(e.target.className&&"function"==typeof e.target.className.indexOf&&e.target.className.indexOf("contract-trigger")<0&&e.target.className.indexOf("expand-trigger")<0)){var t=this;c(this),this.__resizeRAF__&&s(this.__resizeRAF__),this.__resizeRAF__=l((function(){(function(e){return e.offsetWidth!=e.__resizeLast__.width||e.offsetHeight!=e.__resizeLast__.height})(t)&&(t.__resizeLast__.width=t.offsetWidth,t.__resizeLast__.height=t.offsetHeight,t.__resizeListeners__.forEach((function(n){n.call(t,e)})))}))}},d=!1,f="",h="animationstart",p="Webkit Moz O ms".split(" "),g="webkitAnimationStart animationstart oAnimationStart MSAnimationStart".split(" "),v=r.document.createElement("fakeelement");if(void 0!==v.style.animationName&&(d=!0),!1===d)for(var m=0;m<p.length;m++)if(void 0!==v.style[p[m]+"AnimationName"]){f="-"+p[m].toLowerCase()+"-",h=g[m],d=!0;break}var y="resizeanim",_="@"+f+"keyframes "+y+" { from { opacity: 0; } to { opacity: 0; } } ",b=f+"animation: 1ms "+y+"; "}return{addResizeListener:function(e,n){if(a)e.attachEvent("onresize",n);else{if(!e.__resizeTriggers__){var o=e.ownerDocument,i=r.getComputedStyle(e);i&&"static"==i.position&&(e.style.position="relative"),function(e){if(!e.getElementById("detectElementResize")){var n=(_||"")+".resize-triggers { "+(b||"")+'visibility: hidden; opacity: 0; } .resize-triggers, .resize-triggers > div, .contract-trigger:before { content: " "; display: block; position: absolute; top: 0; left: 0; height: 100%; width: 100%; overflow: hidden; z-index: -1; } .resize-triggers > div { background: #eee; overflow: auto; } .contract-trigger:before { width: 200%; height: 200%; }',r=e.head||e.getElementsByTagName("head")[0],o=e.createElement("style");o.id="detectElementResize",o.type="text/css",null!=t&&o.setAttribute("nonce",t),o.styleSheet?o.styleSheet.cssText=n:o.appendChild(e.createTextNode(n)),r.appendChild(o)}}(o),e.__resizeLast__={},e.__resizeListeners__=[],(e.__resizeTriggers__=o.createElement("div")).className="resize-triggers";var l='<div class="expand-trigger"><div></div></div><div class="contract-trigger"></div>';if(window.trustedTypes){var s=trustedTypes.createPolicy("react-virtualized-auto-sizer",{createHTML:function(){return l}});e.__resizeTriggers__.innerHTML=s.createHTML("")}else e.__resizeTriggers__.innerHTML=l;e.appendChild(e.__resizeTriggers__),c(e),e.addEventListener("scroll",u,!0),h&&(e.__resizeTriggers__.__animationListener__=function(t){t.animationName==y&&c(e)},e.__resizeTriggers__.addEventListener(h,e.__resizeTriggers__.__animationListener__))}e.__resizeListeners__.push(n)}},removeResizeListener:function(e,t){if(a)e.detachEvent("onresize",t);else if(e.__resizeListeners__.splice(e.__resizeListeners__.indexOf(t),1),!e.__resizeListeners__.length){e.removeEventListener("scroll",u,!0),e.__resizeTriggers__.__animationListener__&&(e.__resizeTriggers__.removeEventListener(h,e.__resizeTriggers__.__animationListener__),e.__resizeTriggers__.__animationListener__=null);try{e.__resizeTriggers__=!e.removeChild(e.__resizeTriggers__)}catch(e){}}}}}n.d(t,"a",(function(){return r}))}).call(this,n(22))},,function(e,t){e.exports=function(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=Array(t);n<t;n++)r[n]=e[n];return r},e.exports.__esModule=!0,e.exports.default=e.exports},function(e,t,n){var r=n(63);e.exports=function(e,t){if(e){if("string"==typeof e)return r(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?r(e,t):void 0}},e.exports.__esModule=!0,e.exports.default=e.exports},function(e,t,n){(function(t){var n="object"==typeof t&&t&&t.Object===Object&&t;e.exports=n}).call(this,n(22))},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e};t.default=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:i,t=arguments[1];switch(t.type){case o.BEGIN_DRAG:return{initialSourceClientOffset:t.sourceClientOffset,initialClientOffset:t.clientOffset,clientOffset:t.clientOffset};case o.HOVER:return a(e.clientOffset,t.clientOffset)?e:r({},e,{clientOffset:t.clientOffset});case o.END_DRAG:case o.DROP:return i;default:return e}},t.getSourceClientOffset=function(e){var t=e.clientOffset,n=e.initialClientOffset,r=e.initialSourceClientOffset;return t&&n&&r?{x:t.x+r.x-n.x,y:t.y+r.y-n.y}:null},t.getDifferenceFromInitialOffset=function(e){var t=e.clientOffset,n=e.initialClientOffset;return t&&n?{x:t.x-n.x,y:t.y-n.y}:null};var o=n(35),i={initialSourceClientOffset:null,initialClientOffset:null,clientOffset:null};function a(e,t){return e===t||e&&t&&e.x===t.x&&e.y===t.y}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e,t){return(0,o.default)(e)?e.some((function(e){return e===t})):e===t};var r,o=(r=n(20))&&r.__esModule?r:{default:r}},function(e,t,n){var r=n(47),o=n(49),i=n(50),a=n(51),l=n(52),s=n(53);e.exports=function(e,t,n,c){var u=-1,d=o,f=!0,h=e.length,p=[],g=t.length;if(!h)return p;n&&(t=a(t,l(n))),c?(d=i,f=!1):t.length>=200&&(d=s,f=!1,t=new r(t));e:for(;++u<h;){var v=e[u],m=null==n?v:n(v);if(v=c||0!==v?v:0,f&&m==m){for(var y=g;y--;)if(t[y]===m)continue e;p.push(v)}else d(t,m,c)||p.push(v)}return p}},function(e,t,n){var r=n(125),o=n(144),i=n(146),a=n(147),l=n(148);function s(e){var t=-1,n=null==e?0:e.length;for(this.clear();++t<n;){var r=e[t];this.set(r[0],r[1])}}s.prototype.clear=r,s.prototype.delete=o,s.prototype.get=i,s.prototype.has=a,s.prototype.set=l,e.exports=s},function(e,t,n){var r=n(34),o=n(27);e.exports=function(e){if(!o(e))return!1;var t=r(e);return"[object Function]"==t||"[object GeneratorFunction]"==t||"[object AsyncFunction]"==t||"[object Proxy]"==t}},function(e,t){e.exports=function(e){return e}},function(e,t){e.exports=function(e){return"number"==typeof e&&e>-1&&e%1==0&&e<=9007199254740991}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(){var e=arguments[1],t=arguments[2];switch(e.type){case i.HOVER:break;case a.ADD_SOURCE:case a.ADD_TARGET:case a.REMOVE_TARGET:case a.REMOVE_SOURCE:return s;case i.BEGIN_DRAG:case i.PUBLISH_DRAG_SOURCE:case i.END_DRAG:case i.DROP:default:return c}var n=e.targetIds,o=t.targetIds,l=(0,r.default)(n,o),u=!1;if(0===l.length){for(var d=0;d<n.length;d++)if(n[d]!==o[d]){u=!0;break}}else u=!0;if(!u)return s;var f=o[o.length-1],h=n[n.length-1];return f!==h&&(f&&l.push(f),h&&l.push(h)),l},t.areDirty=function(e,t){return e!==s&&(e===c||void 0===t||(0,o.default)(t,e).length>0)};var r=l(n(163)),o=l(n(171)),i=n(35),a=n(41);function l(e){return e&&e.__esModule?e:{default:e}}var s=[],c=[]},function(e,t,n){var r=n(166),o=n(167);e.exports=function e(t,n,i,a,l){var s=-1,c=t.length;for(i||(i=o),l||(l=[]);++s<c;){var u=t[s];n>0&&i(u)?n>1?e(u,n-1,i,a,l):r(l,u):a||(l[l.length]=u)}return l}},function(e,t,n){var r=n(168),o=n(26),i=Object.prototype,a=i.hasOwnProperty,l=i.propertyIsEnumerable,s=r(function(){return arguments}())?r:function(e){return o(e)&&a.call(e,"callee")&&!l.call(e,"callee")};e.exports=s},function(e,t,n){var r=n(47),o=n(49),i=n(50),a=n(53),l=n(169),s=n(78);e.exports=function(e,t,n){var c=-1,u=o,d=e.length,f=!0,h=[],p=h;if(n)f=!1,u=i;else if(d>=200){var g=t?null:l(e);if(g)return s(g);f=!1,u=a,p=new r}else p=t?[]:h;e:for(;++c<d;){var v=e[c],m=t?t(v):v;if(v=n||0!==v?v:0,f&&m==m){for(var y=p.length;y--;)if(p[y]===m)continue e;t&&p.push(m),h.push(v)}else u(p,m,n)||(p!==h&&p.push(m),h.push(v))}return h}},function(e,t){e.exports=function(){}},function(e,t){e.exports=function(e){var t=-1,n=Array(e.size);return e.forEach((function(e){n[++t]=e})),n}},function(e,t){var n=/^(?:0|[1-9]\d*)$/;e.exports=function(e,t){var r=typeof e;return!!(t=null==t?9007199254740991:t)&&("number"==r||"symbol"!=r&&n.test(e))&&e>-1&&e%1==0&&e<t}},,,,function(e,t,n){"use strict";function r(e){var t,n=e.Symbol;return"function"==typeof n?n.observable?t=n.observable:(t=n("observable"),n.observable=t):t="@@observable",t}n.d(t,"a",(function(){return r}))},,,,,function(e,t,n){"use strict";var r={childContextTypes:!0,contextTypes:!0,defaultProps:!0,displayName:!0,getDefaultProps:!0,getDerivedStateFromProps:!0,mixins:!0,propTypes:!0,type:!0},o={name:!0,length:!0,prototype:!0,caller:!0,callee:!0,arguments:!0,arity:!0},i=Object.defineProperty,a=Object.getOwnPropertyNames,l=Object.getOwnPropertySymbols,s=Object.getOwnPropertyDescriptor,c=Object.getPrototypeOf,u=c&&c(Object);e.exports=function e(t,n,d){if("string"!=typeof n){if(u){var f=c(n);f&&f!==u&&e(t,f,d)}var h=a(n);l&&(h=h.concat(l(n)));for(var p=0;p<h.length;++p){var g=h[p];if(!(r[g]||o[g]||d&&d[g])){var v=s(n,g);try{i(t,g,v)}catch(e){}}}return t}return t}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e,t){if(e===t)return!0;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(var o=Object.prototype.hasOwnProperty,i=0;i<n.length;i+=1){if(!o.call(t,n[i])||e[n[i]]!==t[n[i]])return!1;if(e[n[i]]!==t[n[i]])return!1}return!0}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.FILE="__NATIVE_FILE__",t.URL="__NATIVE_URL__",t.TEXT="__NATIVE_TEXT__"},,,,,function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=n(115);Object.defineProperty(t,"DragDropManager",{enumerable:!0,get:function(){return l(r).default}});var o=n(180);Object.defineProperty(t,"DragSource",{enumerable:!0,get:function(){return l(o).default}});var i=n(181);Object.defineProperty(t,"DropTarget",{enumerable:!0,get:function(){return l(i).default}});var a=n(182);function l(e){return e&&e.__esModule?e:{default:e}}Object.defineProperty(t,"createTestBackend",{enumerable:!0,get:function(){return l(a).default}})},function(e,t,n){"use strict";function r(e){return e&&e.__esModule?e:{default:e}}t.__esModule=!0;var o=r(n(55));t.isDisposable=o.default;var i=r(n(185));t.Disposable=i.default;var a=r(n(186));t.CompositeDisposable=a.default;var l=r(n(187));t.SerialDisposable=l.default},function(e,t,n){var r=n(28),o=n(48),i=n(191),a=n(192),l=Object.prototype,s=l.hasOwnProperty,c=r((function(e,t){e=Object(e);var n=-1,r=t.length,c=r>2?t[2]:void 0;for(c&&i(t[0],t[1],c)&&(r=1);++n<r;)for(var u=t[n],d=a(u),f=-1,h=d.length;++f<h;){var p=d[f],g=e[p];(void 0===g||o(g,l[p])&&!s.call(e,p))&&(e[p]=u[p])}return e}));e.exports=c},function(e,t,n){var r=n(74),o=n(28),i=n(76),a=n(40),l=o((function(e){return i(r(e,1,a,!0))}));e.exports=l},function(e,t,n){var r=n(69);function o(e,t){if("function"!=typeof e||null!=t&&"function"!=typeof t)throw new TypeError("Expected a function");var n=function(){var r=arguments,o=t?t.apply(this,r):r[0],i=n.cache;if(i.has(o))return i.get(o);var a=e.apply(this,r);return n.cache=i.set(o,a)||i,a};return n.cache=new(o.Cache||r),n}o.Cache=r,e.exports=o},,,,,,,,,,function(e,t,n){var r=n(63);e.exports=function(e){if(Array.isArray(e))return r(e)},e.exports.__esModule=!0,e.exports.default=e.exports},function(e,t){e.exports=function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)},e.exports.__esModule=!0,e.exports.default=e.exports},function(e,t){e.exports=function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")},e.exports.__esModule=!0,e.exports.default=e.exports},,function(e,t,n){"use strict";var r=n(114);function o(){}function i(){}i.resetWarningCache=o,e.exports=function(){function e(e,t,n,o,i,a){if(a!==r){var l=new Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw l.name="Invariant Violation",l}}function t(){return e}e.isRequired=e;var n={array:e,bigint:e,bool:e,func:e,number:e,object:e,string:e,symbol:e,any:e,arrayOf:t,element:e,elementType:e,instanceOf:t,node:e,objectOf:t,oneOf:t,oneOfType:t,shape:t,exact:t,checkPropTypes:i,resetWarningCache:o};return n.PropTypes=n,n}},function(e,t,n){"use strict";e.exports="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=function(){function e(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,n,r){return n&&e(t.prototype,n),r&&e(t,r),t}}(),o=s(n(116)),i=s(n(123)),a=function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n]);return t.default=e,t}(n(35)),l=s(n(175));function s(e){return e&&e.__esModule?e:{default:e}}function c(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}var u=function(){function e(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};c(this,e);var r=(0,o.default)(i.default);this.context=n,this.store=r,this.monitor=new l.default(r),this.registry=this.monitor.registry,this.backend=t(this),r.subscribe(this.handleRefCountChange.bind(this))}return r(e,[{key:"handleRefCountChange",value:function(){var e=this.store.getState().refCount>0;e&&!this.isSetUp?(this.backend.setup(),this.isSetUp=!0):!e&&this.isSetUp&&(this.backend.teardown(),this.isSetUp=!1)}},{key:"getContext",value:function(){return this.context}},{key:"getMonitor",value:function(){return this.monitor}},{key:"getBackend",value:function(){return this.backend}},{key:"getRegistry",value:function(){return this.registry}},{key:"getActions",value:function(){var e=this,t=this.store.dispatch;return Object.keys(a).filter((function(e){return"function"==typeof a[e]})).reduce((function(n,r){var o,i=a[r];return n[r]=(o=i,function(){for(var n=arguments.length,r=Array(n),i=0;i<n;i++)r[i]=arguments[i];var a=o.apply(e,r);void 0!==a&&t(a)}),n}),{})}}]),e}();t.default=u},function(e,t,n){"use strict";t.__esModule=!0,t.ActionTypes=void 0,t.default=function e(t,n,i){var l;if("function"==typeof n&&void 0===i&&(i=n,n=void 0),void 0!==i){if("function"!=typeof i)throw new Error("Expected the enhancer to be a function.");return i(e)(t,n)}if("function"!=typeof t)throw new Error("Expected the reducer to be a function.");var s=t,c=n,u=[],d=u,f=!1;function h(){d===u&&(d=u.slice())}function p(){return c}function g(e){if("function"!=typeof e)throw new Error("Expected listener to be a function.");var t=!0;return h(),d.push(e),function(){if(t){t=!1,h();var n=d.indexOf(e);d.splice(n,1)}}}function v(e){if(!(0,r.default)(e))throw new Error("Actions must be plain objects. Use custom middleware for async actions.");if(void 0===e.type)throw new Error('Actions may not have an undefined "type" property. Have you misspelled a constant?');if(f)throw new Error("Reducers may not dispatch actions.");try{f=!0,c=s(c,e)}finally{f=!1}for(var t=u=d,n=0;n<t.length;n++)(0,t[n])();return e}return v({type:a.INIT}),(l={dispatch:v,subscribe:g,getState:p,replaceReducer:function(e){if("function"!=typeof e)throw new Error("Expected the nextReducer to be a function.");s=e,v({type:a.INIT})}})[o.default]=function(){var e,t=g;return(e={subscribe:function(e){if("object"!=typeof e)throw new TypeError("Expected the observer to be an object.");function n(){e.next&&e.next(p())}return n(),{unsubscribe:t(n)}}})[o.default]=function(){return this},e},l};var r=i(n(19)),o=i(n(121));function i(e){return e&&e.__esModule?e:{default:e}}var a=t.ActionTypes={INIT:"@@redux/INIT"}},function(e,t,n){var r=n(46),o=Object.prototype,i=o.hasOwnProperty,a=o.toString,l=r?r.toStringTag:void 0;e.exports=function(e){var t=i.call(e,l),n=e[l];try{e[l]=void 0;var r=!0}catch(e){}var o=a.call(e);return r&&(t?e[l]=n:delete e[l]),o}},function(e,t){var n=Object.prototype.toString;e.exports=function(e){return n.call(e)}},function(e,t,n){var r=n(120)(Object.getPrototypeOf,Object);e.exports=r},function(e,t){e.exports=function(e,t){return function(n){return e(t(n))}}},function(e,t,n){"use strict";n.r(t),function(e,r){var o,i=n(83);o="undefined"!=typeof self?self:"undefined"!=typeof window?window:void 0!==e?e:r;var a=Object(i.a)(o);t.default=a}.call(this,n(22),n(122)(e))},function(e,t){e.exports=function(e){if(!e.webpackPolyfill){var t=Object.create(e);t.children||(t.children=[]),Object.defineProperty(t,"loaded",{enumerable:!0,get:function(){return t.l}}),Object.defineProperty(t,"id",{enumerable:!0,get:function(){return t.i}}),Object.defineProperty(t,"exports",{enumerable:!0}),t.webpackPolyfill=1}return t}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=arguments[1];return{dirtyHandlerIds:(0,a.default)(e.dirtyHandlerIds,t,e.dragOperation),dragOffset:(0,r.default)(e.dragOffset,t),refCount:(0,i.default)(e.refCount,t),dragOperation:(0,o.default)(e.dragOperation,t),stateId:(0,l.default)(e.stateId)}};var r=s(n(66)),o=s(n(124)),i=s(n(162)),a=s(n(73)),l=s(n(174));function s(e){return e&&e.__esModule?e:{default:e}}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e};t.default=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:s,t=arguments[1];switch(t.type){case a.BEGIN_DRAG:return r({},e,{itemType:t.itemType,item:t.item,sourceId:t.sourceId,isSourcePublic:t.isSourcePublic,dropResult:null,didDrop:!1});case a.PUBLISH_DRAG_SOURCE:return r({},e,{isSourcePublic:!0});case a.HOVER:return r({},e,{targetIds:t.targetIds});case l.REMOVE_TARGET:return-1===e.targetIds.indexOf(t.targetId)?e:r({},e,{targetIds:(0,i.default)(e.targetIds,t.targetId)});case a.DROP:return r({},e,{dropResult:t.dropResult,didDrop:!0,targetIds:[]});case a.END_DRAG:return r({},e,{itemType:null,item:null,sourceId:null,dropResult:null,didDrop:!1,isSourcePublic:null,targetIds:[]});default:return e}};var o,i=(o=n(58))&&o.__esModule?o:{default:o},a=n(35),l=n(41),s={itemType:null,item:null,sourceId:null,targetIds:[],dropResult:null,didDrop:!1,isSourcePublic:null}},function(e,t,n){var r=n(126),o=n(137),i=n(143);e.exports=function(){this.size=0,this.__data__={hash:new r,map:new(i||o),string:new r}}},function(e,t,n){var r=n(127),o=n(133),i=n(134),a=n(135),l=n(136);function s(e){var t=-1,n=null==e?0:e.length;for(this.clear();++t<n;){var r=e[t];this.set(r[0],r[1])}}s.prototype.clear=r,s.prototype.delete=o,s.prototype.get=i,s.prototype.has=a,s.prototype.set=l,e.exports=s},function(e,t,n){var r=n(36);e.exports=function(){this.__data__=r?r(null):{},this.size=0}},function(e,t,n){var r=n(70),o=n(129),i=n(27),a=n(131),l=/^\[object .+?Constructor\]$/,s=Function.prototype,c=Object.prototype,u=s.toString,d=c.hasOwnProperty,f=RegExp("^"+u.call(d).replace(/[\\^$.*+?()[\]{}|]/g,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$");e.exports=function(e){return!(!i(e)||o(e))&&(r(e)?f:l).test(a(e))}},function(e,t,n){var r,o=n(130),i=(r=/[^.]+$/.exec(o&&o.keys&&o.keys.IE_PROTO||""))?"Symbol(src)_1."+r:"";e.exports=function(e){return!!i&&i in e}},function(e,t,n){var r=n(25)["__core-js_shared__"];e.exports=r},function(e,t){var n=Function.prototype.toString;e.exports=function(e){if(null!=e){try{return n.call(e)}catch(e){}try{return e+""}catch(e){}}return""}},function(e,t){e.exports=function(e,t){return null==e?void 0:e[t]}},function(e,t){e.exports=function(e){var t=this.has(e)&&delete this.__data__[e];return this.size-=t?1:0,t}},function(e,t,n){var r=n(36),o=Object.prototype.hasOwnProperty;e.exports=function(e){var t=this.__data__;if(r){var n=t[e];return"__lodash_hash_undefined__"===n?void 0:n}return o.call(t,e)?t[e]:void 0}},function(e,t,n){var r=n(36),o=Object.prototype.hasOwnProperty;e.exports=function(e){var t=this.__data__;return r?void 0!==t[e]:o.call(t,e)}},function(e,t,n){var r=n(36);e.exports=function(e,t){var n=this.__data__;return this.size+=this.has(e)?0:1,n[e]=r&&void 0===t?"__lodash_hash_undefined__":t,this}},function(e,t,n){var r=n(138),o=n(139),i=n(140),a=n(141),l=n(142);function s(e){var t=-1,n=null==e?0:e.length;for(this.clear();++t<n;){var r=e[t];this.set(r[0],r[1])}}s.prototype.clear=r,s.prototype.delete=o,s.prototype.get=i,s.prototype.has=a,s.prototype.set=l,e.exports=s},function(e,t){e.exports=function(){this.__data__=[],this.size=0}},function(e,t,n){var r=n(38),o=Array.prototype.splice;e.exports=function(e){var t=this.__data__,n=r(t,e);return!(n<0||(n==t.length-1?t.pop():o.call(t,n,1),--this.size,0))}},function(e,t,n){var r=n(38);e.exports=function(e){var t=this.__data__,n=r(t,e);return n<0?void 0:t[n][1]}},function(e,t,n){var r=n(38);e.exports=function(e){return r(this.__data__,e)>-1}},function(e,t,n){var r=n(38);e.exports=function(e,t){var n=this.__data__,o=r(n,e);return o<0?(++this.size,n.push([e,t])):n[o][1]=t,this}},function(e,t,n){var r=n(37)(n(25),"Map");e.exports=r},function(e,t,n){var r=n(39);e.exports=function(e){var t=r(this,e).delete(e);return this.size-=t?1:0,t}},function(e,t){e.exports=function(e){var t=typeof e;return"string"==t||"number"==t||"symbol"==t||"boolean"==t?"__proto__"!==e:null===e}},function(e,t,n){var r=n(39);e.exports=function(e){return r(this,e).get(e)}},function(e,t,n){var r=n(39);e.exports=function(e){return r(this,e).has(e)}},function(e,t,n){var r=n(39);e.exports=function(e,t){var n=r(this,e),o=n.size;return n.set(e,t),this.size+=n.size==o?0:1,this}},function(e,t){e.exports=function(e){return this.__data__.set(e,"__lodash_hash_undefined__"),this}},function(e,t){e.exports=function(e){return this.__data__.has(e)}},function(e,t,n){var r=n(152),o=n(153),i=n(154);e.exports=function(e,t,n){return t==t?i(e,t,n):r(e,o,n)}},function(e,t){e.exports=function(e,t,n,r){for(var o=e.length,i=n+(r?1:-1);r?i--:++i<o;)if(t(e[i],i,e))return i;return-1}},function(e,t){e.exports=function(e){return e!=e}},function(e,t){e.exports=function(e,t,n){for(var r=n-1,o=e.length;++r<o;)if(e[r]===t)return r;return-1}},function(e,t,n){var r=n(156),o=Math.max;e.exports=function(e,t,n){return t=o(void 0===t?e.length-1:t,0),function(){for(var i=arguments,a=-1,l=o(i.length-t,0),s=Array(l);++a<l;)s[a]=i[t+a];a=-1;for(var c=Array(t+1);++a<t;)c[a]=i[a];return c[t]=n(s),r(e,this,c)}}},function(e,t){e.exports=function(e,t,n){switch(n.length){case 0:return e.call(t);case 1:return e.call(t,n[0]);case 2:return e.call(t,n[0],n[1]);case 3:return e.call(t,n[0],n[1],n[2])}return e.apply(t,n)}},function(e,t,n){var r=n(158),o=n(161)(r);e.exports=o},function(e,t,n){var r=n(159),o=n(160),i=n(71),a=o?function(e,t){return o(e,"toString",{configurable:!0,enumerable:!1,value:r(t),writable:!0})}:i;e.exports=a},function(e,t){e.exports=function(e){return function(){return e}}},function(e,t,n){var r=n(37),o=function(){try{var e=r(Object,"defineProperty");return e({},"",{}),e}catch(e){}}();e.exports=o},function(e,t){var n=Date.now;e.exports=function(e){var t=0,r=0;return function(){var o=n(),i=16-(o-r);if(r=o,i>0){if(++t>=800)return arguments[0]}else t=0;return e.apply(void 0,arguments)}}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,t=arguments[1];switch(t.type){case r.ADD_SOURCE:case r.ADD_TARGET:return e+1;case r.REMOVE_SOURCE:case r.REMOVE_TARGET:return e-1;default:return e}};var r=n(41)},function(e,t,n){var r=n(164),o=n(28),i=n(165),a=n(40),l=o((function(e){return i(r(e,a))}));e.exports=l},function(e,t){e.exports=function(e,t){for(var n=-1,r=null==e?0:e.length,o=0,i=[];++n<r;){var a=e[n];t(a,n,e)&&(i[o++]=a)}return i}},function(e,t,n){var r=n(68),o=n(74),i=n(76);e.exports=function(e,t,n){var a=e.length;if(a<2)return a?i(e[0]):[];for(var l=-1,s=Array(a);++l<a;)for(var c=e[l],u=-1;++u<a;)u!=l&&(s[l]=r(s[l]||c,e[u],t,n));return i(o(s,1),t,n)}},function(e,t){e.exports=function(e,t){for(var n=-1,r=t.length,o=e.length;++n<r;)e[o+n]=t[n];return e}},function(e,t,n){var r=n(46),o=n(75),i=n(20),a=r?r.isConcatSpreadable:void 0;e.exports=function(e){return i(e)||o(e)||!!(a&&e&&e[a])}},function(e,t,n){var r=n(34),o=n(26);e.exports=function(e){return o(e)&&"[object Arguments]"==r(e)}},function(e,t,n){var r=n(170),o=n(77),i=n(78),a=r&&1/i(new r([,-0]))[1]==1/0?function(e){return new r(e)}:o;e.exports=a},function(e,t,n){var r=n(37)(n(25),"Set");e.exports=r},function(e,t,n){var r=n(51),o=n(172),i=n(28),a=n(173),l=i((function(e){var t=r(e,a);return t.length&&t[0]===e[0]?o(t):[]}));e.exports=l},function(e,t,n){var r=n(47),o=n(49),i=n(50),a=n(51),l=n(52),s=n(53),c=Math.min;e.exports=function(e,t,n){for(var u=n?i:o,d=e[0].length,f=e.length,h=f,p=Array(f),g=1/0,v=[];h--;){var m=e[h];h&&t&&(m=a(m,l(t))),g=c(m.length,g),p[h]=!n&&(t||d>=120&&m.length>=120)?new r(h&&m):void 0}m=e[0];var y=-1,_=p[0];e:for(;++y<d&&v.length<g;){var b=m[y],w=t?t(b):b;if(b=n||0!==b?b:0,!(_?s(_,w):u(v,w,n))){for(h=f;--h;){var S=p[h];if(!(S?s(S,w):u(e[h],w,n)))continue e}_&&_.push(w),v.push(b)}}return v}},function(e,t,n){var r=n(40);e.exports=function(e){return r(e)?e:[]}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0;return e+1}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=function(){function e(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,n,r){return n&&e(t.prototype,n),r&&e(t,r),t}}(),o=u(n(14)),i=u(n(20)),a=u(n(67)),l=u(n(176)),s=n(66),c=n(73);function u(e){return e&&e.__esModule?e:{default:e}}var d=function(){function e(t){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.store=t,this.registry=new l.default(t)}return r(e,[{key:"subscribeToStateChange",value:function(e){var t=this,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=n.handlerIds;(0,o.default)("function"==typeof e,"listener must be a function."),(0,o.default)(void 0===r||(0,i.default)(r),"handlerIds, when specified, must be an array of strings.");var a=this.store.getState().stateId,l=function(){var n=t.store.getState(),o=n.stateId;try{o===a||o===a+1&&!(0,c.areDirty)(n.dirtyHandlerIds,r)||e()}finally{a=o}};return this.store.subscribe(l)}},{key:"subscribeToOffsetChange",value:function(e){var t=this;(0,o.default)("function"==typeof e,"listener must be a function.");var n=this.store.getState().dragOffset;return this.store.subscribe((function(){var r=t.store.getState().dragOffset;r!==n&&(n=r,e())}))}},{key:"canDragSource",value:function(e){var t=this.registry.getSource(e);return(0,o.default)(t,"Expected to find a valid source."),!this.isDragging()&&t.canDrag(this,e)}},{key:"canDropOnTarget",value:function(e){var t=this.registry.getTarget(e);if((0,o.default)(t,"Expected to find a valid target."),!this.isDragging()||this.didDrop())return!1;var n=this.registry.getTargetType(e),r=this.getItemType();return(0,a.default)(n,r)&&t.canDrop(this,e)}},{key:"isDragging",value:function(){return Boolean(this.getItemType())}},{key:"isDraggingSource",value:function(e){var t=this.registry.getSource(e,!0);return(0,o.default)(t,"Expected to find a valid source."),!(!this.isDragging()||!this.isSourcePublic())&&this.registry.getSourceType(e)===this.getItemType()&&t.isDragging(this,e)}},{key:"isOverTarget",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{shallow:!1},n=t.shallow;if(!this.isDragging())return!1;var r=this.registry.getTargetType(e),o=this.getItemType();if(!(0,a.default)(r,o))return!1;var i=this.getTargetIds();if(!i.length)return!1;var l=i.indexOf(e);return n?l===i.length-1:l>-1}},{key:"getItemType",value:function(){return this.store.getState().dragOperation.itemType}},{key:"getItem",value:function(){return this.store.getState().dragOperation.item}},{key:"getSourceId",value:function(){return this.store.getState().dragOperation.sourceId}},{key:"getTargetIds",value:function(){return this.store.getState().dragOperation.targetIds}},{key:"getDropResult",value:function(){return this.store.getState().dragOperation.dropResult}},{key:"didDrop",value:function(){return this.store.getState().dragOperation.didDrop}},{key:"isSourcePublic",value:function(){return this.store.getState().dragOperation.isSourcePublic}},{key:"getInitialClientOffset",value:function(){return this.store.getState().dragOffset.initialClientOffset}},{key:"getInitialSourceClientOffset",value:function(){return this.store.getState().dragOffset.initialSourceClientOffset}},{key:"getClientOffset",value:function(){return this.store.getState().dragOffset.clientOffset}},{key:"getSourceClientOffset",value:function(){return(0,s.getSourceClientOffset)(this.store.getState().dragOffset)}},{key:"getDifferenceFromInitialOffset",value:function(){return(0,s.getDifferenceFromInitialOffset)(this.store.getState().dragOffset)}}]),e}();t.default=d},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=function(){function e(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,n,r){return n&&e(t.prototype,n),r&&e(t,r),t}}(),o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},i=u(n(14)),a=u(n(20)),l=u(n(177)),s=n(41),c=u(n(179));function u(e){return e&&e.__esModule?e:{default:e}}function d(e,t){t&&(0,a.default)(e)?e.forEach((function(e){return d(e,!1)})):(0,i.default)("string"==typeof e||"symbol"===(void 0===e?"undefined":o(e)),t?"Type can only be a string, a symbol, or an array of either.":"Type can only be a string or a symbol.")}function f(e){switch(e[0]){case"S":return"SOURCE";case"T":return"TARGET";default:(0,i.default)(!1,"Cannot parse handler ID: "+e)}}var h=function(){function e(t){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.store=t,this.types={},this.handlers={},this.pinnedSourceId=null,this.pinnedSource=null}return r(e,[{key:"addSource",value:function(e,t){d(e),function(e){(0,i.default)("function"==typeof e.canDrag,"Expected canDrag to be a function."),(0,i.default)("function"==typeof e.beginDrag,"Expected beginDrag to be a function."),(0,i.default)("function"==typeof e.endDrag,"Expected endDrag to be a function.")}(t);var n=this.addHandler("SOURCE",e,t);return this.store.dispatch((0,s.addSource)(n)),n}},{key:"addTarget",value:function(e,t){d(e,!0),function(e){(0,i.default)("function"==typeof e.canDrop,"Expected canDrop to be a function."),(0,i.default)("function"==typeof e.hover,"Expected hover to be a function."),(0,i.default)("function"==typeof e.drop,"Expected beginDrag to be a function.")}(t);var n=this.addHandler("TARGET",e,t);return this.store.dispatch((0,s.addTarget)(n)),n}},{key:"addHandler",value:function(e,t,n){var r=function(e){var t=(0,c.default)().toString();switch(e){case"SOURCE":return"S"+t;case"TARGET":return"T"+t;default:(0,i.default)(!1,"Unknown role: "+e)}}(e);return this.types[r]=t,this.handlers[r]=n,r}},{key:"containsHandler",value:function(e){var t=this;return Object.keys(this.handlers).some((function(n){return t.handlers[n]===e}))}},{key:"getSource",value:function(e,t){return(0,i.default)(this.isSourceId(e),"Expected a valid source ID."),t&&e===this.pinnedSourceId?this.pinnedSource:this.handlers[e]}},{key:"getTarget",value:function(e){return(0,i.default)(this.isTargetId(e),"Expected a valid target ID."),this.handlers[e]}},{key:"getSourceType",value:function(e){return(0,i.default)(this.isSourceId(e),"Expected a valid source ID."),this.types[e]}},{key:"getTargetType",value:function(e){return(0,i.default)(this.isTargetId(e),"Expected a valid target ID."),this.types[e]}},{key:"isSourceId",value:function(e){return"SOURCE"===f(e)}},{key:"isTargetId",value:function(e){return"TARGET"===f(e)}},{key:"removeSource",value:function(e){var t=this;(0,i.default)(this.getSource(e),"Expected an existing source."),this.store.dispatch((0,s.removeSource)(e)),(0,l.default)((function(){delete t.handlers[e],delete t.types[e]}))}},{key:"removeTarget",value:function(e){var t=this;(0,i.default)(this.getTarget(e),"Expected an existing target."),this.store.dispatch((0,s.removeTarget)(e)),(0,l.default)((function(){delete t.handlers[e],delete t.types[e]}))}},{key:"pinSource",value:function(e){var t=this.getSource(e);(0,i.default)(t,"Expected an existing source."),this.pinnedSourceId=e,this.pinnedSource=t}},{key:"unpinSource",value:function(){(0,i.default)(this.pinnedSource,"No source is pinned at the time."),this.pinnedSourceId=null,this.pinnedSource=null}}]),e}();t.default=h},function(e,t,n){"use strict";var r=n(178),o=[],i=[],a=r.makeRequestCallFromTimer((function(){if(i.length)throw i.shift()}));function l(e){var t;(t=o.length?o.pop():new s).task=e,r(t)}function s(){this.task=null}e.exports=l,s.prototype.call=function(){try{this.task.call()}catch(e){l.onerror?l.onerror(e):(i.push(e),a())}finally{this.task=null,o[o.length]=this}}},function(e,t,n){"use strict";(function(t){function n(e){o.length||r(),o[o.length]=e}e.exports=n;var r,o=[],i=0;function a(){for(;i<o.length;){var e=i;if(i+=1,o[e].call(),i>1024){for(var t=0,n=o.length-i;t<n;t++)o[t]=o[t+i];o.length-=i,i=0}}o.length=0,i=0}var l,s,c,u=void 0!==t?t:self,d=u.MutationObserver||u.WebKitMutationObserver;function f(e){return function(){var t=setTimeout(r,0),n=setInterval(r,50);function r(){clearTimeout(t),clearInterval(n),e()}}}"function"==typeof d?(l=1,s=new d(a),c=document.createTextNode(""),s.observe(c,{characterData:!0}),r=function(){l=-l,c.data=l}):r=f(a),n.requestFlush=r,n.makeRequestCallFromTimer=f}).call(this,n(22))},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(){return r++};var r=0},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=function(){function e(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,n,r){return n&&e(t.prototype,n),r&&e(t,r),t}}(),o=function(){function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e)}return r(e,[{key:"canDrag",value:function(){return!0}},{key:"isDragging",value:function(e,t){return t===e.getSourceId()}},{key:"endDrag",value:function(){}}]),e}();t.default=o},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=function(){function e(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,n,r){return n&&e(t.prototype,n),r&&e(t,r),t}}(),o=function(){function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e)}return r(e,[{key:"canDrop",value:function(){return!0}},{key:"hover",value:function(){}},{key:"drop",value:function(){}}]),e}();t.default=o},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=function(){function e(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,n,r){return n&&e(t.prototype,n),r&&e(t,r),t}}();t.default=function(e){return new a(e)};var o,i=(o=n(77))&&o.__esModule?o:{default:o},a=function(){function e(t){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.actions=t.getActions()}return r(e,[{key:"setup",value:function(){this.didCallSetup=!0}},{key:"teardown",value:function(){this.didCallTeardown=!0}},{key:"connectDragSource",value:function(){return i.default}},{key:"connectDragPreview",value:function(){return i.default}},{key:"connectDropTarget",value:function(){return i.default}},{key:"simulateBeginDrag",value:function(e,t){this.actions.beginDrag(e,t)}},{key:"simulatePublishDragSource",value:function(){this.actions.publishDragSource()}},{key:"simulateHover",value:function(e,t){this.actions.hover(e,t)}},{key:"simulateDrop",value:function(){this.actions.drop()}},{key:"simulateEndDrag",value:function(){this.actions.endDrag()}}]),e}()},,,function(e,t,n){"use strict";t.__esModule=!0;var r=function(){function e(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,n,r){return n&&e(t.prototype,n),r&&e(t,r),t}}(),o=function(){},i=function(){function e(t){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.isDisposed=!1,this.action=t||o}return r(e,null,[{key:"empty",value:{dispose:o},enumerable:!0}]),e.prototype.dispose=function(){this.isDisposed||(this.action.call(null),this.isDisposed=!0)},e}();t.default=i,e.exports=t.default},function(e,t,n){"use strict";function r(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}t.__esModule=!0;var o,i=(o=n(55))&&o.__esModule?o:{default:o},a=function(){function e(){for(var t=arguments.length,n=Array(t),o=0;o<t;o++)n[o]=arguments[o];r(this,e),Array.isArray(n[0])&&1===n.length&&(n=n[0]);for(var a=0;a<n.length;a++)if(!i.default(n[a]))throw new Error("Expected a disposable");this.disposables=n,this.isDisposed=!1}return e.prototype.add=function(e){this.isDisposed?e.dispose():this.disposables.push(e)},e.prototype.remove=function(e){if(this.isDisposed)return!1;var t=this.disposables.indexOf(e);return-1!==t&&(this.disposables.splice(t,1),e.dispose(),!0)},e.prototype.dispose=function(){if(!this.isDisposed){for(var e=this.disposables.length,t=new Array(e),n=0;n<e;n++)t[n]=this.disposables[n];for(this.isDisposed=!0,this.disposables=[],this.length=0,n=0;n<e;n++)t[n].dispose()}},e}();t.default=a,e.exports=t.default},function(e,t,n){"use strict";t.__esModule=!0;var r,o=(r=n(55))&&r.__esModule?r:{default:r},i=function(){function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.isDisposed=!1,this.current=null}return e.prototype.getDisposable=function(){return this.current},e.prototype.setDisposable=function(){var e=arguments.length<=0||void 0===arguments[0]?null:arguments[0];if(null!=e&&!o.default(e))throw new Error("Expected either an empty value or a valid disposable");var t=this.isDisposed,n=void 0;t||(n=this.current,this.current=e),n&&n.dispose(),t&&e&&e.dispose()},e.prototype.dispose=function(){if(!this.isDisposed){this.isDisposed=!0;var e=this.current;this.current=null,e&&e.dispose()}},e}();t.default=i,e.exports=t.default},,,,function(e,t,n){var r=n(48),o=n(54),i=n(79),a=n(27);e.exports=function(e,t,n){if(!a(n))return!1;var l=typeof t;return!!("number"==l?o(n)&&i(t,n.length):"string"==l&&t in n)&&r(n[t],e)}},function(e,t,n){var r=n(193),o=n(200),i=n(54);e.exports=function(e){return i(e)?r(e,!0):o(e)}},function(e,t,n){var r=n(194),o=n(75),i=n(20),a=n(195),l=n(79),s=n(197),c=Object.prototype.hasOwnProperty;e.exports=function(e,t){var n=i(e),u=!n&&o(e),d=!n&&!u&&a(e),f=!n&&!u&&!d&&s(e),h=n||u||d||f,p=h?r(e.length,String):[],g=p.length;for(var v in e)!t&&!c.call(e,v)||h&&("length"==v||d&&("offset"==v||"parent"==v)||f&&("buffer"==v||"byteLength"==v||"byteOffset"==v)||l(v,g))||p.push(v);return p}},function(e,t){e.exports=function(e,t){for(var n=-1,r=Array(e);++n<e;)r[n]=t(n);return r}},function(e,t,n){(function(e){var r=n(25),o=n(196),i=t&&!t.nodeType&&t,a=i&&"object"==typeof e&&e&&!e.nodeType&&e,l=a&&a.exports===i?r.Buffer:void 0,s=(l?l.isBuffer:void 0)||o;e.exports=s}).call(this,n(56)(e))},function(e,t){e.exports=function(){return!1}},function(e,t,n){var r=n(198),o=n(52),i=n(199),a=i&&i.isTypedArray,l=a?o(a):r;e.exports=l},function(e,t,n){var r=n(34),o=n(72),i=n(26),a={};a["[object Float32Array]"]=a["[object Float64Array]"]=a["[object Int8Array]"]=a["[object Int16Array]"]=a["[object Int32Array]"]=a["[object Uint8Array]"]=a["[object Uint8ClampedArray]"]=a["[object Uint16Array]"]=a["[object Uint32Array]"]=!0,a["[object Arguments]"]=a["[object Array]"]=a["[object ArrayBuffer]"]=a["[object Boolean]"]=a["[object DataView]"]=a["[object Date]"]=a["[object Error]"]=a["[object Function]"]=a["[object Map]"]=a["[object Number]"]=a["[object Object]"]=a["[object RegExp]"]=a["[object Set]"]=a["[object String]"]=a["[object WeakMap]"]=!1,e.exports=function(e){return i(e)&&o(e.length)&&!!a[r(e)]}},function(e,t,n){(function(e){var r=n(65),o=t&&!t.nodeType&&t,i=o&&"object"==typeof e&&e&&!e.nodeType&&e,a=i&&i.exports===o&&r.process,l=function(){try{return i&&i.require&&i.require("util").types||a&&a.binding&&a.binding("util")}catch(e){}}();e.exports=l}).call(this,n(56)(e))},function(e,t,n){var r=n(27),o=n(201),i=n(202),a=Object.prototype.hasOwnProperty;e.exports=function(e){if(!r(e))return i(e);var t=o(e),n=[];for(var l in e)("constructor"!=l||!t&&a.call(e,l))&&n.push(l);return n}},function(e,t){var n=Object.prototype;e.exports=function(e){var t=e&&e.constructor;return e===("function"==typeof t&&t.prototype||n)}},function(e,t){e.exports=function(e){var t=[];if(null!=e)for(var n in Object(e))t.push(n);return t}},,function(e,t){e.exports=function(e){if(Array.isArray(e))return e},e.exports.__esModule=!0,e.exports.default=e.exports},function(e,t){e.exports=function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,o,i,a,l=[],s=!0,c=!1;try{if(i=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;s=!1}else for(;!(s=(r=i.call(n)).done)&&(l.push(r.value),l.length!==t);s=!0);}catch(e){c=!0,o=e}finally{try{if(!s&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(c)throw o}}return l}},e.exports.__esModule=!0,e.exports.default=e.exports},function(e,t){e.exports=function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")},e.exports.__esModule=!0,e.exports.default=e.exports},function(e,t){var n,r,o=e.exports={};function i(){throw new Error("setTimeout has not been defined")}function a(){throw new Error("clearTimeout has not been defined")}function l(e){if(n===setTimeout)return setTimeout(e,0);if((n===i||!n)&&setTimeout)return n=setTimeout,setTimeout(e,0);try{return n(e,0)}catch(t){try{return n.call(null,e,0)}catch(t){return n.call(this,e,0)}}}!function(){try{n="function"==typeof setTimeout?setTimeout:i}catch(e){n=i}try{r="function"==typeof clearTimeout?clearTimeout:a}catch(e){r=a}}();var s,c=[],u=!1,d=-1;function f(){u&&s&&(u=!1,s.length?c=s.concat(c):d=-1,c.length&&h())}function h(){if(!u){var e=l(f);u=!0;for(var t=c.length;t;){for(s=c,c=[];++d<t;)s&&s[d].run();d=-1,t=c.length}s=null,u=!1,function(e){if(r===clearTimeout)return clearTimeout(e);if((r===a||!r)&&clearTimeout)return r=clearTimeout,clearTimeout(e);try{r(e)}catch(t){try{return r.call(null,e)}catch(t){return r.call(this,e)}}}(e)}}function p(e,t){this.fun=e,this.array=t}function g(){}o.nextTick=function(e){var t=new Array(arguments.length-1);if(arguments.length>1)for(var n=1;n<arguments.length;n++)t[n-1]=arguments[n];c.push(new p(e,t)),1!==c.length||u||l(h)},p.prototype.run=function(){this.fun.apply(null,this.array)},o.title="browser",o.browser=!0,o.env={},o.argv=[],o.version="",o.versions={},o.on=g,o.addListener=g,o.once=g,o.off=g,o.removeListener=g,o.removeAllListeners=g,o.emit=g,o.prependListener=g,o.prependOnceListener=g,o.listeners=function(e){return[]},o.binding=function(e){throw new Error("process.binding is not supported")},o.cwd=function(){return"/"},o.chdir=function(e){throw new Error("process.chdir is not supported")},o.umask=function(){return 0}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.unpackBackendForEs5Users=t.createChildContext=t.CHILD_CONTEXT_TYPES=void 0;var r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},o=function(){function e(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,n,r){return n&&e(t.prototype,n),r&&e(t,r),t}}(),i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e};t.default=function(e){f.default.apply(void 0,["DragDropContext","backend"].concat(Array.prototype.slice.call(arguments)));var t=_(e),n=y(t);return function(e){var t,i,s=e.displayName||e.name||"Component",c=(i=t=function(t){function i(){return p(this,i),g(this,(i.__proto__||Object.getPrototypeOf(i)).apply(this,arguments))}return v(i,t),o(i,[{key:"getDecoratedComponentInstance",value:function(){return(0,u.default)(this.child,"In order to access an instance of the decorated component it can not be a stateless component."),this.child}},{key:"getManager",value:function(){return n.dragDropManager}},{key:"getChildContext",value:function(){return n}},{key:"render",value:function(){var t=this;return l.default.createElement(e,r({},this.props,{ref:function(e){t.child=e}}))}}]),i}(a.Component),t.DecoratedComponent=e,t.displayName="DragDropContext("+s+")",t.childContextTypes=m,i);return(0,d.default)(c,e)}};var a=n(0),l=h(a),s=h(n(2)),c=n(95),u=h(n(14)),d=h(n(88)),f=h(n(60));function h(e){return e&&e.__esModule?e:{default:e}}function p(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function g(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}function v(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}var m=t.CHILD_CONTEXT_TYPES={dragDropManager:s.default.object.isRequired},y=t.createChildContext=function(e,t){return{dragDropManager:new c.DragDropManager(e,t)}},_=t.unpackBackendForEs5Users=function(e){var t=e;return"object"===(void 0===t?"undefined":i(t))&&"function"==typeof t.default&&(t=t.default),(0,u.default)("function"==typeof t,"Expected the backend to be a function or an ES6 module exporting a default function. Read more: http://react-dnd.github.io/react-dnd/docs-drag-drop-context.html"),t}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e};t.default=function(e,t){if(e===t)return!0;if("object"!==(void 0===e?"undefined":r(e))||null===e||"object"!==(void 0===t?"undefined":r(t))||null===t)return!1;var n=Object.keys(e),o=Object.keys(t);if(n.length!==o.length)return!1;for(var i=Object.prototype.hasOwnProperty,a=0;a<n.length;a+=1){if(!i.call(t,n[a]))return!1;var l=e[n[a]],s=t[n[a]];if(l!==s||"object"===(void 0===l?"undefined":r(l))||"object"===(void 0===s?"undefined":r(s)))return!1}return!0}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},i=function(){function e(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,n,r){return n&&e(t.prototype,n),r&&e(t,r),t}}();t.default=function(e){var t,n,p=e.DecoratedComponent,v=e.createHandler,m=e.createMonitor,y=e.createConnector,_=e.registerHandler,b=e.containerDisplayName,w=e.getType,S=e.collect,x=e.options.arePropsEqual,C=void 0===x?h.default:x,O=p.displayName||p.name||"Component",T=(n=t=function(e){function t(e,n){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,t);var r=function(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}(this,(t.__proto__||Object.getPrototypeOf(t)).call(this,e,n));return r.handleChange=r.handleChange.bind(r),r.handleChildRef=r.handleChildRef.bind(r),(0,u.default)("object"===o(r.context.dragDropManager),"Could not find the drag and drop manager in the context of %s. Make sure to wrap the top-level component of your app with DragDropContext. Read more: http://react-dnd.github.io/react-dnd/docs-troubleshooting.html#could-not-find-the-drag-and-drop-manager-in-the-context",O,O),r.manager=r.context.dragDropManager,r.handlerMonitor=m(r.manager),r.handlerConnector=y(r.manager.getBackend()),r.handler=v(r.handlerMonitor),r.disposable=new c.SerialDisposable,r.receiveProps(e),r.state=r.getCurrentState(),r.dispose(),r}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(t,e),i(t,[{key:"getHandlerId",value:function(){return this.handlerId}},{key:"getDecoratedComponentInstance",value:function(){return this.decoratedComponentInstance}},{key:"shouldComponentUpdate",value:function(e,t){return!C(e,this.props)||!(0,f.default)(t,this.state)}}]),i(t,[{key:"componentDidMount",value:function(){this.isCurrentlyMounted=!0,this.disposable=new c.SerialDisposable,this.currentType=null,this.receiveProps(this.props),this.handleChange()}},{key:"componentWillReceiveProps",value:function(e){C(e,this.props)||(this.receiveProps(e),this.handleChange())}},{key:"componentWillUnmount",value:function(){this.dispose(),this.isCurrentlyMounted=!1}},{key:"receiveProps",value:function(e){this.handler.receiveProps(e),this.receiveType(w(e))}},{key:"receiveType",value:function(e){if(e!==this.currentType){this.currentType=e;var t=_(e,this.handler,this.manager),n=t.handlerId,r=t.unregister;this.handlerId=n,this.handlerMonitor.receiveHandlerId(n),this.handlerConnector.receiveHandlerId(n);var o=this.manager.getMonitor().subscribeToStateChange(this.handleChange,{handlerIds:[n]});this.disposable.setDisposable(new c.CompositeDisposable(new c.Disposable(o),new c.Disposable(r)))}}},{key:"handleChange",value:function(){if(this.isCurrentlyMounted){var e=this.getCurrentState();(0,f.default)(e,this.state)||this.setState(e)}}},{key:"dispose",value:function(){this.disposable.dispose(),this.handlerConnector.receiveHandlerId(null)}},{key:"handleChildRef",value:function(e){this.decoratedComponentInstance=e,this.handler.receiveComponent(e)}},{key:"getCurrentState",value:function(){return S(this.handlerConnector.hooks,this.handlerMonitor)}},{key:"render",value:function(){return l.default.createElement(p,r({},this.props,this.state,{ref:g(p)?this.handleChildRef:null}))}}]),t}(a.Component),t.DecoratedComponent=p,t.displayName=b+"("+O+")",t.contextTypes={dragDropManager:s.default.object.isRequired},n);return(0,d.default)(T,p)};var a=n(0),l=p(a),s=p(n(2)),c=n(96),u=(p(n(19)),p(n(14))),d=p(n(88)),f=p(n(89)),h=p(n(209));function p(e){return e&&e.__esModule?e:{default:e}}var g=function(e){return Boolean(e&&e.prototype&&"function"==typeof e.prototype.render)}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){var t={};return Object.keys(e).forEach((function(n){var r,l=(r=e[n],function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;if((0,o.isValidElement)(e)){var n=e;a(n);var l=t?function(e){return r(e,t)}:r;return(0,i.default)(n,l)}var s=e;r(s,t)});t[n]=function(){return l}})),t};var r,o=n(0),i=(r=n(264))&&r.__esModule?r:{default:r};function a(e){if("string"!=typeof e.type){var t=e.type.displayName||e.type.name||"the component";throw new Error("Only native element nodes can now be passed to React DnD connectors.You can either wrap "+t+" into a <div>, or turn it into a drag source or a drop target itself.")}}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e,t){return t===e||null!==t&&null!==e&&(0,o.default)(t,e)};var r,o=(r=n(89))&&r.__esModule?r:{default:r}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e};t.default=function e(t,n){return"string"==typeof t||"symbol"===(void 0===t?"undefined":r(t))||n&&(0,i.default)(t)&&t.every((function(t){return e(t,!1)}))};var o,i=(o=n(20))&&o.__esModule?o:{default:o}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.isSafari=t.isFirefox=void 0;var r,o=(r=n(99))&&r.__esModule?r:{default:r};t.isFirefox=(0,o.default)((function(){return/firefox/i.test(navigator.userAgent)})),t.isSafari=(0,o.default)((function(){return Boolean(window.safari)}))},,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,function(e,t){e.exports=function(e,t){if(null==e)return{};var n={};for(var r in e)if({}.hasOwnProperty.call(e,r)){if(t.indexOf(r)>=0)continue;n[r]=e[r]}return n},e.exports.__esModule=!0,e.exports.default=e.exports},function(e,t,n){(function(e,n){var r="[object Arguments]",o="[object Map]",i="[object Object]",a="[object Set]",l=/^\[object .+?Constructor\]$/,s=/^(?:0|[1-9]\d*)$/,c={};c["[object Float32Array]"]=c["[object Float64Array]"]=c["[object Int8Array]"]=c["[object Int16Array]"]=c["[object Int32Array]"]=c["[object Uint8Array]"]=c["[object Uint8ClampedArray]"]=c["[object Uint16Array]"]=c["[object Uint32Array]"]=!0,c[r]=c["[object Array]"]=c["[object ArrayBuffer]"]=c["[object Boolean]"]=c["[object DataView]"]=c["[object Date]"]=c["[object Error]"]=c["[object Function]"]=c[o]=c["[object Number]"]=c[i]=c["[object RegExp]"]=c[a]=c["[object String]"]=c["[object WeakMap]"]=!1;var u="object"==typeof e&&e&&e.Object===Object&&e,d="object"==typeof self&&self&&self.Object===Object&&self,f=u||d||Function("return this")(),h=t&&!t.nodeType&&t,p=h&&"object"==typeof n&&n&&!n.nodeType&&n,g=p&&p.exports===h,v=g&&u.process,m=function(){try{return v&&v.binding&&v.binding("util")}catch(e){}}(),y=m&&m.isTypedArray;function _(e,t){for(var n=-1,r=null==e?0:e.length;++n<r;)if(t(e[n],n,e))return!0;return!1}function b(e){var t=-1,n=Array(e.size);return e.forEach((function(e,r){n[++t]=[r,e]})),n}function w(e){var t=-1,n=Array(e.size);return e.forEach((function(e){n[++t]=e})),n}var S,x,C,O=Array.prototype,T=Function.prototype,I=Object.prototype,D=f["__core-js_shared__"],k=T.toString,R=I.hasOwnProperty,P=(S=/[^.]+$/.exec(D&&D.keys&&D.keys.IE_PROTO||""))?"Symbol(src)_1."+S:"",E=I.toString,M=RegExp("^"+k.call(R).replace(/[\\^$.*+?()[\]{}|]/g,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$"),j=g?f.Buffer:void 0,z=f.Symbol,N=f.Uint8Array,L=I.propertyIsEnumerable,A=O.splice,H=z?z.toStringTag:void 0,G=Object.getOwnPropertySymbols,F=j?j.isBuffer:void 0,W=(x=Object.keys,C=Object,function(e){return x(C(e))}),U=pe(f,"DataView"),B=pe(f,"Map"),V=pe(f,"Promise"),K=pe(f,"Set"),q=pe(f,"WeakMap"),Y=pe(Object,"create"),Z=ye(U),X=ye(B),J=ye(V),$=ye(K),Q=ye(q),ee=z?z.prototype:void 0,te=ee?ee.valueOf:void 0;function ne(e){var t=-1,n=null==e?0:e.length;for(this.clear();++t<n;){var r=e[t];this.set(r[0],r[1])}}function re(e){var t=-1,n=null==e?0:e.length;for(this.clear();++t<n;){var r=e[t];this.set(r[0],r[1])}}function oe(e){var t=-1,n=null==e?0:e.length;for(this.clear();++t<n;){var r=e[t];this.set(r[0],r[1])}}function ie(e){var t=-1,n=null==e?0:e.length;for(this.__data__=new oe;++t<n;)this.add(e[t])}function ae(e){var t=this.__data__=new re(e);this.size=t.size}function le(e,t){for(var n=e.length;n--;)if(_e(e[n][0],t))return n;return-1}function se(e){return null==e?void 0===e?"[object Undefined]":"[object Null]":H&&H in Object(e)?function(e){var t=R.call(e,H),n=e[H];try{e[H]=void 0;var r=!0}catch(e){}var o=E.call(e);return r&&(t?e[H]=n:delete e[H]),o}(e):function(e){return E.call(e)}(e)}function ce(e){return Te(e)&&se(e)==r}function ue(e,t,n,l,s){return e===t||(null==e||null==t||!Te(e)&&!Te(t)?e!=e&&t!=t:function(e,t,n,l,s,c){var u=we(e),d=we(t),f=u?"[object Array]":ve(e),h=d?"[object Array]":ve(t),p=(f=f==r?i:f)==i,g=(h=h==r?i:h)==i,v=f==h;if(v&&Se(e)){if(!Se(t))return!1;u=!0,p=!1}if(v&&!p)return c||(c=new ae),u||Ie(e)?de(e,t,n,l,s,c):function(e,t,n,r,i,l,s){switch(n){case"[object DataView]":if(e.byteLength!=t.byteLength||e.byteOffset!=t.byteOffset)return!1;e=e.buffer,t=t.buffer;case"[object ArrayBuffer]":return!(e.byteLength!=t.byteLength||!l(new N(e),new N(t)));case"[object Boolean]":case"[object Date]":case"[object Number]":return _e(+e,+t);case"[object Error]":return e.name==t.name&&e.message==t.message;case"[object RegExp]":case"[object String]":return e==t+"";case o:var c=b;case a:var u=1&r;if(c||(c=w),e.size!=t.size&&!u)return!1;var d=s.get(e);if(d)return d==t;r|=2,s.set(e,t);var f=de(c(e),c(t),r,i,l,s);return s.delete(e),f;case"[object Symbol]":if(te)return te.call(e)==te.call(t)}return!1}(e,t,f,n,l,s,c);if(!(1&n)){var m=p&&R.call(e,"__wrapped__"),y=g&&R.call(t,"__wrapped__");if(m||y){var _=m?e.value():e,S=y?t.value():t;return c||(c=new ae),s(_,S,n,l,c)}}return!!v&&(c||(c=new ae),function(e,t,n,r,o,i){var a=1&n,l=fe(e),s=l.length;if(s!=fe(t).length&&!a)return!1;for(var c=s;c--;){var u=l[c];if(!(a?u in t:R.call(t,u)))return!1}var d=i.get(e);if(d&&i.get(t))return d==t;var f=!0;i.set(e,t),i.set(t,e);for(var h=a;++c<s;){var p=e[u=l[c]],g=t[u];if(r)var v=a?r(g,p,u,t,e,i):r(p,g,u,e,t,i);if(!(void 0===v?p===g||o(p,g,n,r,i):v)){f=!1;break}h||(h="constructor"==u)}if(f&&!h){var m=e.constructor,y=t.constructor;m==y||!("constructor"in e)||!("constructor"in t)||"function"==typeof m&&m instanceof m&&"function"==typeof y&&y instanceof y||(f=!1)}return i.delete(e),i.delete(t),f}(e,t,n,l,s,c))}(e,t,n,l,ue,s))}function de(e,t,n,r,o,i){var a=1&n,l=e.length,s=t.length;if(l!=s&&!(a&&s>l))return!1;var c=i.get(e);if(c&&i.get(t))return c==t;var u=-1,d=!0,f=2&n?new ie:void 0;for(i.set(e,t),i.set(t,e);++u<l;){var h=e[u],p=t[u];if(r)var g=a?r(p,h,u,t,e,i):r(h,p,u,e,t,i);if(void 0!==g){if(g)continue;d=!1;break}if(f){if(!_(t,(function(e,t){if(a=t,!f.has(a)&&(h===e||o(h,e,n,r,i)))return f.push(t);var a}))){d=!1;break}}else if(h!==p&&!o(h,p,n,r,i)){d=!1;break}}return i.delete(e),i.delete(t),d}function fe(e){return function(e,t,n){var r=t(e);return we(e)?r:function(e,t){for(var n=-1,r=t.length,o=e.length;++n<r;)e[o+n]=t[n];return e}(r,n(e))}(e,De,ge)}function he(e,t){var n,r,o=e.__data__;return("string"==(r=typeof(n=t))||"number"==r||"symbol"==r||"boolean"==r?"__proto__"!==n:null===n)?o["string"==typeof t?"string":"hash"]:o.map}function pe(e,t){var n=function(e,t){return null==e?void 0:e[t]}(e,t);return function(e){return!(!Oe(e)||function(e){return!!P&&P in e}(e))&&(xe(e)?M:l).test(ye(e))}(n)?n:void 0}ne.prototype.clear=function(){this.__data__=Y?Y(null):{},this.size=0},ne.prototype.delete=function(e){var t=this.has(e)&&delete this.__data__[e];return this.size-=t?1:0,t},ne.prototype.get=function(e){var t=this.__data__;if(Y){var n=t[e];return"__lodash_hash_undefined__"===n?void 0:n}return R.call(t,e)?t[e]:void 0},ne.prototype.has=function(e){var t=this.__data__;return Y?void 0!==t[e]:R.call(t,e)},ne.prototype.set=function(e,t){var n=this.__data__;return this.size+=this.has(e)?0:1,n[e]=Y&&void 0===t?"__lodash_hash_undefined__":t,this},re.prototype.clear=function(){this.__data__=[],this.size=0},re.prototype.delete=function(e){var t=this.__data__,n=le(t,e);return!(n<0||(n==t.length-1?t.pop():A.call(t,n,1),--this.size,0))},re.prototype.get=function(e){var t=this.__data__,n=le(t,e);return n<0?void 0:t[n][1]},re.prototype.has=function(e){return le(this.__data__,e)>-1},re.prototype.set=function(e,t){var n=this.__data__,r=le(n,e);return r<0?(++this.size,n.push([e,t])):n[r][1]=t,this},oe.prototype.clear=function(){this.size=0,this.__data__={hash:new ne,map:new(B||re),string:new ne}},oe.prototype.delete=function(e){var t=he(this,e).delete(e);return this.size-=t?1:0,t},oe.prototype.get=function(e){return he(this,e).get(e)},oe.prototype.has=function(e){return he(this,e).has(e)},oe.prototype.set=function(e,t){var n=he(this,e),r=n.size;return n.set(e,t),this.size+=n.size==r?0:1,this},ie.prototype.add=ie.prototype.push=function(e){return this.__data__.set(e,"__lodash_hash_undefined__"),this},ie.prototype.has=function(e){return this.__data__.has(e)},ae.prototype.clear=function(){this.__data__=new re,this.size=0},ae.prototype.delete=function(e){var t=this.__data__,n=t.delete(e);return this.size=t.size,n},ae.prototype.get=function(e){return this.__data__.get(e)},ae.prototype.has=function(e){return this.__data__.has(e)},ae.prototype.set=function(e,t){var n=this.__data__;if(n instanceof re){var r=n.__data__;if(!B||r.length<199)return r.push([e,t]),this.size=++n.size,this;n=this.__data__=new oe(r)}return n.set(e,t),this.size=n.size,this};var ge=G?function(e){return null==e?[]:(e=Object(e),function(t,n){for(var r=-1,o=null==t?0:t.length,i=0,a=[];++r<o;){var l=t[r];s=l,L.call(e,s)&&(a[i++]=l)}var s;return a}(G(e)))}:function(){return[]},ve=se;function me(e,t){return!!(t=null==t?9007199254740991:t)&&("number"==typeof e||s.test(e))&&e>-1&&e%1==0&&e<t}function ye(e){if(null!=e){try{return k.call(e)}catch(e){}try{return e+""}catch(e){}}return""}function _e(e,t){return e===t||e!=e&&t!=t}(U&&"[object DataView]"!=ve(new U(new ArrayBuffer(1)))||B&&ve(new B)!=o||V&&"[object Promise]"!=ve(V.resolve())||K&&ve(new K)!=a||q&&"[object WeakMap]"!=ve(new q))&&(ve=function(e){var t=se(e),n=t==i?e.constructor:void 0,r=n?ye(n):"";if(r)switch(r){case Z:return"[object DataView]";case X:return o;case J:return"[object Promise]";case $:return a;case Q:return"[object WeakMap]"}return t});var be=ce(function(){return arguments}())?ce:function(e){return Te(e)&&R.call(e,"callee")&&!L.call(e,"callee")},we=Array.isArray,Se=F||function(){return!1};function xe(e){if(!Oe(e))return!1;var t=se(e);return"[object Function]"==t||"[object GeneratorFunction]"==t||"[object AsyncFunction]"==t||"[object Proxy]"==t}function Ce(e){return"number"==typeof e&&e>-1&&e%1==0&&e<=9007199254740991}function Oe(e){var t=typeof e;return null!=e&&("object"==t||"function"==t)}function Te(e){return null!=e&&"object"==typeof e}var Ie=y?function(e){return function(t){return e(t)}}(y):function(e){return Te(e)&&Ce(e.length)&&!!c[se(e)]};function De(e){return null!=(t=e)&&Ce(t.length)&&!xe(t)?function(e,t){var n=we(e),r=!n&&be(e),o=!n&&!r&&Se(e),i=!n&&!r&&!o&&Ie(e),a=n||r||o||i,l=a?function(e,t){for(var n=-1,r=Array(e);++n<e;)r[n]=t(n);return r}(e.length,String):[],s=l.length;for(var c in e)!t&&!R.call(e,c)||a&&("length"==c||o&&("offset"==c||"parent"==c)||i&&("buffer"==c||"byteLength"==c||"byteOffset"==c)||me(c,s))||l.push(c);return l}(e):function(e){if(n=(t=e)&&t.constructor,t!==("function"==typeof n&&n.prototype||I))return W(e);var t,n,r=[];for(var o in Object(e))R.call(e,o)&&"constructor"!=o&&r.push(o);return r}(e);var t}n.exports=function(e,t){return ue(e,t)}}).call(this,n(22),n(56)(e))},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.defaultVerticalStrength=t.defaultHorizontalStrength=void 0;var r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},o=function(){function e(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,n,r){return n&&e(t.prototype,n),r&&e(t,r),t}}();t.createHorizontalStrength=g,t.createVerticalStrength=v,t.default=function(e){var t=function(t){function n(e,t){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,n);var r=function(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}(this,(n.__proto__||Object.getPrototypeOf(n)).call(this,e,t));return r.handleEvent=function(e){r.dragging&&!r.attached&&(r.attach(),r.updateScrolling(e))},r.updateScrolling=(0,c.default)((function(e){var t=r.container.getBoundingClientRect(),n={x:t.left,y:t.top,w:t.width,h:t.height},o=(0,h.getCoords)(e);r.scaleX=r.props.horizontalStrength(n,o),r.scaleY=r.props.verticalStrength(n,o),r.frame||!r.scaleX&&!r.scaleY||r.startScrolling()}),100,{trailing:!1}),r.scaleX=0,r.scaleY=0,r.frame=null,r.attached=!1,r.dragging=!1,r}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(n,t),o(n,[{key:"componentDidMount",value:function(){var e=this;this.container=(0,s.findDOMNode)(this.wrappedInstance),this.container.addEventListener("dragover",this.handleEvent),window.document.body.addEventListener("touchmove",this.handleEvent),this.clearMonitorSubscription=this.context.dragDropManager.getMonitor().subscribeToStateChange((function(){return e.handleMonitorChange()}))}},{key:"componentWillUnmount",value:function(){this.container.removeEventListener("dragover",this.handleEvent),window.document.body.removeEventListener("touchmove",this.handleEvent),this.clearMonitorSubscription(),this.stopScrolling()}},{key:"handleMonitorChange",value:function(){var e=this.context.dragDropManager.getMonitor().isDragging();!this.dragging&&e?this.dragging=!0:this.dragging&&!e&&(this.dragging=!1,this.stopScrolling())}},{key:"attach",value:function(){this.attached=!0,window.document.body.addEventListener("dragover",this.updateScrolling),window.document.body.addEventListener("touchmove",this.updateScrolling)}},{key:"detach",value:function(){this.attached=!1,window.document.body.removeEventListener("dragover",this.updateScrolling),window.document.body.removeEventListener("touchmove",this.updateScrolling)}},{key:"startScrolling",value:function(){var e=this,t=0;!function n(){var r=e.scaleX,o=e.scaleY,i=e.container,a=e.props,l=a.strengthMultiplier,s=a.onScrollChange;if(0!==l&&r+o!==0){if(t++%2){var c=i.scrollLeft,d=i.scrollTop,f=i.scrollWidth,p=i.scrollHeight,g=i.clientWidth,v=i.clientHeight;s(r?i.scrollLeft=(0,h.intBetween)(0,f-g,c+r*l):c,o?i.scrollTop=(0,h.intBetween)(0,p-v,d+o*l):d)}e.frame=(0,u.default)(n)}else e.stopScrolling()}()}},{key:"stopScrolling",value:function(){this.detach(),this.scaleX=0,this.scaleY=0,this.frame&&(u.default.cancel(this.frame),this.frame=null)}},{key:"render",value:function(){var t=this,n=this.props,o=(n.strengthMultiplier,n.verticalStrength,n.horizontalStrength,n.onScrollChange,function(e,t){var n={};for(var r in e)t.indexOf(r)>=0||Object.prototype.hasOwnProperty.call(e,r)&&(n[r]=e[r]);return n}(n,["strengthMultiplier","verticalStrength","horizontalStrength","onScrollChange"]));return a.default.createElement(e,r({ref:function(e){t.wrappedInstance=e}},o))}}]),n}(i.Component);return t.displayName="Scrolling("+(0,d.default)(e)+")",t.propTypes={onScrollChange:l.default.func,verticalStrength:l.default.func,horizontalStrength:l.default.func,strengthMultiplier:l.default.number},t.defaultProps={onScrollChange:h.noop,verticalStrength:y,horizontalStrength:m,strengthMultiplier:30},t.contextTypes={dragDropManager:l.default.object},(0,f.default)(t,e)};var i=n(0),a=p(i),l=p(n(2)),s=n(13),c=p(n(250)),u=p(n(251)),d=p(n(253)),f=p(n(254)),h=n(255);function p(e){return e&&e.__esModule?e:{default:e}}function g(e){return function(t,n){var r=t.x,o=t.w,i=t.y,a=t.h,l=Math.min(o/2,e);if(n.x>=r&&n.x<=r+o&&n.y>=i&&n.y<=i+a){if(n.x<r+l)return(n.x-r-l)/l;if(n.x>r+o-l)return-(r+o-n.x-l)/l}return 0}}function v(e){return function(t,n){var r=t.y,o=t.h,i=t.x,a=t.w,l=Math.min(o/2,e);if(n.y>=r&&n.y<=r+o&&n.x>=i&&n.x<=i+a){if(n.y<r+l)return(n.y-r-l)/l;if(n.y>r+o-l)return-(r+o-n.y-l)/l}return 0}}var m=t.defaultHorizontalStrength=g(150),y=t.defaultVerticalStrength=v(150)},function(e,t,n){(function(t){var n=/^\s+|\s+$/g,r=/^[-+]0x[0-9a-f]+$/i,o=/^0b[01]+$/i,i=/^0o[0-7]+$/i,a=parseInt,l="object"==typeof t&&t&&t.Object===Object&&t,s="object"==typeof self&&self&&self.Object===Object&&self,c=l||s||Function("return this")(),u=Object.prototype.toString,d=Math.max,f=Math.min,h=function(){return c.Date.now()};function p(e){var t=typeof e;return!!e&&("object"==t||"function"==t)}function g(e){if("number"==typeof e)return e;if(function(e){return"symbol"==typeof e||function(e){return!!e&&"object"==typeof e}(e)&&"[object Symbol]"==u.call(e)}(e))return NaN;if(p(e)){var t="function"==typeof e.valueOf?e.valueOf():e;e=p(t)?t+"":t}if("string"!=typeof e)return 0===e?e:+e;e=e.replace(n,"");var l=o.test(e);return l||i.test(e)?a(e.slice(2),l?2:8):r.test(e)?NaN:+e}e.exports=function(e,t,n){var r=!0,o=!0;if("function"!=typeof e)throw new TypeError("Expected a function");return p(n)&&(r="leading"in n?!!n.leading:r,o="trailing"in n?!!n.trailing:o),function(e,t,n){var r,o,i,a,l,s,c=0,u=!1,v=!1,m=!0;if("function"!=typeof e)throw new TypeError("Expected a function");function y(t){var n=r,i=o;return r=o=void 0,c=t,a=e.apply(i,n)}function _(e){return c=e,l=setTimeout(w,t),u?y(e):a}function b(e){var n=e-s;return void 0===s||n>=t||n<0||v&&e-c>=i}function w(){var e=h();if(b(e))return S(e);l=setTimeout(w,function(e){var n=t-(e-s);return v?f(n,i-(e-c)):n}(e))}function S(e){return l=void 0,m&&r?y(e):(r=o=void 0,a)}function x(){var e=h(),n=b(e);if(r=arguments,o=this,s=e,n){if(void 0===l)return _(s);if(v)return l=setTimeout(w,t),y(s)}return void 0===l&&(l=setTimeout(w,t)),a}return t=g(t)||0,p(n)&&(u=!!n.leading,i=(v="maxWait"in n)?d(g(n.maxWait)||0,t):i,m="trailing"in n?!!n.trailing:m),x.cancel=function(){void 0!==l&&clearTimeout(l),c=0,r=s=o=l=void 0},x.flush=function(){return void 0===l?a:S(h())},x}(e,t,{leading:r,maxWait:t,trailing:o})}}).call(this,n(22))},function(e,t,n){(function(t){for(var r=n(252),o="undefined"==typeof window?t:window,i=["moz","webkit"],a="AnimationFrame",l=o["request"+a],s=o["cancel"+a]||o["cancelRequest"+a],c=0;!l&&c<i.length;c++)l=o[i[c]+"Request"+a],s=o[i[c]+"Cancel"+a]||o[i[c]+"CancelRequest"+a];if(!l||!s){var u=0,d=0,f=[];l=function(e){if(0===f.length){var t=r(),n=Math.max(0,1e3/60-(t-u));u=n+t,setTimeout((function(){var e=f.slice(0);f.length=0;for(var t=0;t<e.length;t++)if(!e[t].cancelled)try{e[t].callback(u)}catch(e){setTimeout((function(){throw e}),0)}}),Math.round(n))}return f.push({handle:++d,callback:e,cancelled:!1}),d},s=function(e){for(var t=0;t<f.length;t++)f[t].handle===e&&(f[t].cancelled=!0)}}e.exports=function(e){return l.call(o,e)},e.exports.cancel=function(){s.apply(o,arguments)},e.exports.polyfill=function(e){e||(e=o),e.requestAnimationFrame=l,e.cancelAnimationFrame=s}}).call(this,n(22))},function(e,t,n){(function(t){(function(){var n,r,o,i,a,l;"undefined"!=typeof performance&&null!==performance&&performance.now?e.exports=function(){return performance.now()}:null!=t&&t.hrtime?(e.exports=function(){return(n()-a)/1e6},r=t.hrtime,i=(n=function(){var e;return 1e9*(e=r())[0]+e[1]})(),l=1e9*t.uptime(),a=i-l):Date.now?(e.exports=function(){return Date.now()-o},o=Date.now()):(e.exports=function(){return(new Date).getTime()-o},o=(new Date).getTime())}).call(this)}).call(this,n(207))},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){return e.displayName||e.name||("string"==typeof e&&e.length>0?e:"Unknown")}},function(e,t,n){"use strict";var r={childContextTypes:!0,contextTypes:!0,defaultProps:!0,displayName:!0,getDefaultProps:!0,mixins:!0,propTypes:!0,type:!0},o={name:!0,length:!0,prototype:!0,caller:!0,arguments:!0,arity:!0},i="function"==typeof Object.getOwnPropertySymbols;e.exports=function(e,t,n){if("string"!=typeof t){var a=Object.getOwnPropertyNames(t);i&&(a=a.concat(Object.getOwnPropertySymbols(t)));for(var l=0;l<a.length;++l)if(!(r[a[l]]||o[a[l]]||n&&n[a[l]]))try{e[a[l]]=t[a[l]]}catch(e){}}return e}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.noop=function(){},t.intBetween=function(e,t,n){return Math.floor(Math.min(t,Math.max(e,n)))},t.getCoords=function(e){return"touchmove"===e.type?{x:e.changedTouches[0].clientX,y:e.changedTouches[0].clientY}:{x:e.clientX,y:e.clientY}}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=n(208);Object.defineProperty(t,"DragDropContext",{enumerable:!0,get:function(){return s(r).default}});var o=n(257);Object.defineProperty(t,"DragDropContextProvider",{enumerable:!0,get:function(){return s(o).default}});var i=n(258);Object.defineProperty(t,"DragLayer",{enumerable:!0,get:function(){return s(i).default}});var a=n(259);Object.defineProperty(t,"DragSource",{enumerable:!0,get:function(){return s(a).default}});var l=n(265);function s(e){return e&&e.__esModule?e:{default:e}}Object.defineProperty(t,"DropTarget",{enumerable:!0,get:function(){return s(l).default}})},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r,o,i,a=function(){function e(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,n,r){return n&&e(t.prototype,n),r&&e(t,r),t}}(),l=n(0),s=(i=n(2))&&i.__esModule?i:{default:i},c=n(208),u=(o=r=function(e){function t(e,n){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,t);var r=function(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}(this,(t.__proto__||Object.getPrototypeOf(t)).call(this,e,n));return r.backend=(0,c.unpackBackendForEs5Users)(e.backend),r.childContext=(0,c.createChildContext)(r.backend,{window:e&&e.window?e.window:n&&n.window?n.window:"undefined"!=typeof window?window:void 0}),r}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(t,e),a(t,[{key:"componentWillReceiveProps",value:function(e){if(e.backend!==this.props.backend||e.window!==this.props.window)throw new Error("DragDropContextProvider backend and window props must not change.")}},{key:"getChildContext",value:function(){return this.childContext}},{key:"render",value:function(){return l.Children.only(this.props.children)}}]),t}(l.Component),r.propTypes={backend:s.default.oneOfType([s.default.func,s.default.object]).isRequired,children:s.default.element.isRequired,window:s.default.object},r.defaultProps={window:void 0},r.childContextTypes=c.CHILD_CONTEXT_TYPES,r.displayName="DragDropContextProvider",r.contextTypes={window:s.default.object},o);t.default=u},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},i=function(){function e(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,n,r){return n&&e(t.prototype,n),r&&e(t,r),t}}();t.default=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return p.default.apply(void 0,["DragLayer","collect[, options]"].concat(Array.prototype.slice.call(arguments))),(0,d.default)("function"==typeof e,'Expected "collect" provided as the first argument to DragLayer to be a function that collects props to inject into the component. ',"Instead, received %s. Read more: http://react-dnd.github.io/react-dnd/docs-drag-layer.html",e),(0,d.default)((0,u.default)(t),'Expected "options" provided as the second argument to DragLayer to be a plain object when specified. Instead, received %s. Read more: http://react-dnd.github.io/react-dnd/docs-drag-layer.html',t),function(n){var u,p,g=t.arePropsEqual,_=void 0===g?h.default:g,b=n.displayName||n.name||"Component",w=(p=u=function(t){function a(e,t){v(this,a);var n=m(this,(a.__proto__||Object.getPrototypeOf(a)).call(this,e));return n.handleChange=n.handleChange.bind(n),n.manager=t.dragDropManager,(0,d.default)("object"===o(n.manager),"Could not find the drag and drop manager in the context of %s. Make sure to wrap the top-level component of your app with DragDropContext. Read more: http://react-dnd.github.io/react-dnd/docs-troubleshooting.html#could-not-find-the-drag-and-drop-manager-in-the-context",b,b),n.state=n.getCurrentState(),n}return y(a,t),i(a,[{key:"getDecoratedComponentInstance",value:function(){return(0,d.default)(this.child,"In order to access an instance of the decorated component it can not be a stateless component."),this.child}},{key:"shouldComponentUpdate",value:function(e,t){return!_(e,this.props)||!(0,f.default)(t,this.state)}}]),i(a,[{key:"componentDidMount",value:function(){this.isCurrentlyMounted=!0;var e=this.manager.getMonitor();this.unsubscribeFromOffsetChange=e.subscribeToOffsetChange(this.handleChange),this.unsubscribeFromStateChange=e.subscribeToStateChange(this.handleChange),this.handleChange()}},{key:"componentWillUnmount",value:function(){this.isCurrentlyMounted=!1,this.unsubscribeFromOffsetChange(),this.unsubscribeFromStateChange()}},{key:"handleChange",value:function(){if(this.isCurrentlyMounted){var e=this.getCurrentState();(0,f.default)(e,this.state)||this.setState(e)}}},{key:"getCurrentState",value:function(){var t=this.manager.getMonitor();return e(t)}},{key:"render",value:function(){var e=this;return l.default.createElement(n,r({},this.props,this.state,{ref:function(t){e.child=t}}))}}]),a}(a.Component),u.DecoratedComponent=n,u.displayName="DragLayer("+b+")",u.contextTypes={dragDropManager:s.default.object.isRequired},p);return(0,c.default)(w,n)}};var a=n(0),l=g(a),s=g(n(2)),c=g(n(88)),u=g(n(19)),d=g(n(14)),f=g(n(89)),h=g(n(209)),p=g(n(60));function g(e){return e&&e.__esModule?e:{default:e}}function v(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function m(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}function y(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e,t,n){var f=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{};i.default.apply(void 0,["DragSource","type, spec, collect[, options]"].concat(Array.prototype.slice.call(arguments)));var h=e;"function"!=typeof e&&((0,r.default)((0,d.default)(e),'Expected "type" provided as the first argument to DragSource to be a string, or a function that returns a string given the current props. Instead, received %s. Read more: http://react-dnd.github.io/react-dnd/docs-drag-source.html',e),h=function(){return e}),(0,r.default)((0,o.default)(t),'Expected "spec" provided as the second argument to DragSource to be a plain object. Instead, received %s. Read more: http://react-dnd.github.io/react-dnd/docs-drag-source.html',t);var p=(0,s.default)(t);return(0,r.default)("function"==typeof n,'Expected "collect" provided as the third argument to DragSource to be a function that returns a plain object of props to inject. Instead, received %s. Read more: http://react-dnd.github.io/react-dnd/docs-drag-source.html',n),(0,r.default)((0,o.default)(f),'Expected "options" provided as the fourth argument to DragSource to be a plain object when specified. Instead, received %s. Read more: http://react-dnd.github.io/react-dnd/docs-drag-source.html',n),function(e){return(0,a.default)({connectBackend:function(e,t){return e.connectDragSource(t)},containerDisplayName:"DragSource",createHandler:p,registerHandler:l.default,createMonitor:c.default,createConnector:u.default,DecoratedComponent:e,getType:h,collect:n,options:f})}};var r=f(n(14)),o=f(n(19)),i=f(n(60)),a=f(n(210)),l=f(n(260)),s=f(n(261)),c=f(n(262)),u=f(n(263)),d=f(n(213));function f(e){return e&&e.__esModule?e:{default:e}}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e,t,n){var r=n.getRegistry(),o=r.addSource(e,t);return{handlerId:o,unregister:function(){r.removeSource(o)}}}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=function(){function e(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,n,r){return n&&e(t.prototype,n),r&&e(t,r),t}}();t.default=function(e){Object.keys(e).forEach((function(t){(0,o.default)(a.indexOf(t)>-1,'Expected the drag source specification to only have some of the following keys: %s. Instead received a specification with an unexpected "%s" key. Read more: http://react-dnd.github.io/react-dnd/docs-drag-source.html',a.join(", "),t),(0,o.default)("function"==typeof e[t],"Expected %s in the drag source specification to be a function. Instead received a specification with %s: %s. Read more: http://react-dnd.github.io/react-dnd/docs-drag-source.html",t,t,e[t])})),l.forEach((function(t){(0,o.default)("function"==typeof e[t],"Expected %s in the drag source specification to be a function. Instead received a specification with %s: %s. Read more: http://react-dnd.github.io/react-dnd/docs-drag-source.html",t,t,e[t])}));var t=function(){function t(e){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,t),this.monitor=e,this.props=null,this.component=null}return r(t,[{key:"receiveProps",value:function(e){this.props=e}},{key:"receiveComponent",value:function(e){this.component=e}},{key:"canDrag",value:function(){return!e.canDrag||e.canDrag(this.props,this.monitor)}},{key:"isDragging",value:function(t,n){return e.isDragging?e.isDragging(this.props,this.monitor):n===t.getSourceId()}},{key:"beginDrag",value:function(){return e.beginDrag(this.props,this.monitor,this.component)}},{key:"endDrag",value:function(){e.endDrag&&e.endDrag(this.props,this.monitor,this.component)}}]),t}();return function(e){return new t(e)}};var o=i(n(14));function i(e){return e&&e.__esModule?e:{default:e}}i(n(19));var a=["canDrag","beginDrag","isDragging","endDrag"],l=["beginDrag"]},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=function(){function e(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,n,r){return n&&e(t.prototype,n),r&&e(t,r),t}}();t.default=function(e){return new s(e)};var o,i=(o=n(14))&&o.__esModule?o:{default:o},a=!1,l=!1,s=function(){function e(t){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.internalMonitor=t.getMonitor()}return r(e,[{key:"receiveHandlerId",value:function(e){this.sourceId=e}},{key:"canDrag",value:function(){(0,i.default)(!a,"You may not call monitor.canDrag() inside your canDrag() implementation. Read more: http://react-dnd.github.io/react-dnd/docs-drag-source-monitor.html");try{return a=!0,this.internalMonitor.canDragSource(this.sourceId)}finally{a=!1}}},{key:"isDragging",value:function(){(0,i.default)(!l,"You may not call monitor.isDragging() inside your isDragging() implementation. Read more: http://react-dnd.github.io/react-dnd/docs-drag-source-monitor.html");try{return l=!0,this.internalMonitor.isDraggingSource(this.sourceId)}finally{l=!1}}},{key:"getItemType",value:function(){return this.internalMonitor.getItemType()}},{key:"getItem",value:function(){return this.internalMonitor.getItem()}},{key:"getDropResult",value:function(){return this.internalMonitor.getDropResult()}},{key:"didDrop",value:function(){return this.internalMonitor.didDrop()}},{key:"getInitialClientOffset",value:function(){return this.internalMonitor.getInitialClientOffset()}},{key:"getInitialSourceClientOffset",value:function(){return this.internalMonitor.getInitialSourceClientOffset()}},{key:"getSourceClientOffset",value:function(){return this.internalMonitor.getSourceClientOffset()}},{key:"getClientOffset",value:function(){return this.internalMonitor.getClientOffset()}},{key:"getDifferenceFromInitialOffset",value:function(){return this.internalMonitor.getDifferenceFromInitialOffset()}}]),e}()},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){var t=void 0,n=void 0,i=void 0,a=void 0,l=void 0,s=void 0,c=void 0;function u(){a&&(a(),a=null),t&&n&&(a=e.connectDragSource(t,n,i))}function d(){c&&(c(),c=null),t&&l&&(c=e.connectDragPreview(t,l,s))}return{receiveHandlerId:function(e){e!==t&&(t=e,u(),d())},hooks:(0,r.default)({dragSource:function(e,t){e===n&&(0,o.default)(t,i)||(n=e,i=t,u())},dragPreview:function(e,t){e===l&&(0,o.default)(t,s)||(l=e,s=t,d())}})}};var r=i(n(211)),o=i(n(212));function i(e){return e&&e.__esModule?e:{default:e}}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e,t){var n=e.ref;return(0,o.default)("string"!=typeof n,"Cannot connect React DnD to an element with an existing string ref. Please convert it to use a callback ref instead, or wrap it into a <span> or <div>. Read more: https://facebook.github.io/react/docs/more-about-refs.html#the-ref-callback-attribute"),n?(0,i.cloneElement)(e,{ref:function(e){t(e),n&&n(e)}}):(0,i.cloneElement)(e,{ref:t})};var r,o=(r=n(14))&&r.__esModule?r:{default:r},i=n(0)},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e,t,n){var f=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{};i.default.apply(void 0,["DropTarget","type, spec, collect[, options]"].concat(Array.prototype.slice.call(arguments)));var h=e;"function"!=typeof e&&((0,r.default)((0,d.default)(e,!0),'Expected "type" provided as the first argument to DropTarget to be a string, an array of strings, or a function that returns either given the current props. Instead, received %s. Read more: http://react-dnd.github.io/react-dnd/docs-drop-target.html',e),h=function(){return e}),(0,r.default)((0,o.default)(t),'Expected "spec" provided as the second argument to DropTarget to be a plain object. Instead, received %s. Read more: http://react-dnd.github.io/react-dnd/docs-drop-target.html',t);var p=(0,s.default)(t);return(0,r.default)("function"==typeof n,'Expected "collect" provided as the third argument to DropTarget to be a function that returns a plain object of props to inject. Instead, received %s. Read more: http://react-dnd.github.io/react-dnd/docs-drop-target.html',n),(0,r.default)((0,o.default)(f),'Expected "options" provided as the fourth argument to DropTarget to be a plain object when specified. Instead, received %s. Read more: http://react-dnd.github.io/react-dnd/docs-drop-target.html',n),function(e){return(0,a.default)({connectBackend:function(e,t){return e.connectDropTarget(t)},containerDisplayName:"DropTarget",createHandler:p,registerHandler:l.default,createMonitor:c.default,createConnector:u.default,DecoratedComponent:e,getType:h,collect:n,options:f})}};var r=f(n(14)),o=f(n(19)),i=f(n(60)),a=f(n(210)),l=f(n(266)),s=f(n(267)),c=f(n(268)),u=f(n(269)),d=f(n(213));function f(e){return e&&e.__esModule?e:{default:e}}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e,t,n){var r=n.getRegistry(),o=r.addTarget(e,t);return{handlerId:o,unregister:function(){r.removeTarget(o)}}}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=function(){function e(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,n,r){return n&&e(t.prototype,n),r&&e(t,r),t}}();t.default=function(e){Object.keys(e).forEach((function(t){(0,o.default)(a.indexOf(t)>-1,'Expected the drop target specification to only have some of the following keys: %s. Instead received a specification with an unexpected "%s" key. Read more: http://react-dnd.github.io/react-dnd/docs-drop-target.html',a.join(", "),t),(0,o.default)("function"==typeof e[t],"Expected %s in the drop target specification to be a function. Instead received a specification with %s: %s. Read more: http://react-dnd.github.io/react-dnd/docs-drop-target.html",t,t,e[t])}));var t=function(){function t(e){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,t),this.monitor=e,this.props=null,this.component=null}return r(t,[{key:"receiveProps",value:function(e){this.props=e}},{key:"receiveMonitor",value:function(e){this.monitor=e}},{key:"receiveComponent",value:function(e){this.component=e}},{key:"canDrop",value:function(){return!e.canDrop||e.canDrop(this.props,this.monitor)}},{key:"hover",value:function(){e.hover&&e.hover(this.props,this.monitor,this.component)}},{key:"drop",value:function(){if(e.drop)return e.drop(this.props,this.monitor,this.component)}}]),t}();return function(e){return new t(e)}};var o=i(n(14));function i(e){return e&&e.__esModule?e:{default:e}}i(n(19));var a=["canDrop","hover","drop"]},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=function(){function e(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,n,r){return n&&e(t.prototype,n),r&&e(t,r),t}}();t.default=function(e){return new l(e)};var o,i=(o=n(14))&&o.__esModule?o:{default:o},a=!1,l=function(){function e(t){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.internalMonitor=t.getMonitor()}return r(e,[{key:"receiveHandlerId",value:function(e){this.targetId=e}},{key:"canDrop",value:function(){(0,i.default)(!a,"You may not call monitor.canDrop() inside your canDrop() implementation. Read more: http://react-dnd.github.io/react-dnd/docs-drop-target-monitor.html");try{return a=!0,this.internalMonitor.canDropOnTarget(this.targetId)}finally{a=!1}}},{key:"isOver",value:function(e){return this.internalMonitor.isOverTarget(this.targetId,e)}},{key:"getItemType",value:function(){return this.internalMonitor.getItemType()}},{key:"getItem",value:function(){return this.internalMonitor.getItem()}},{key:"getDropResult",value:function(){return this.internalMonitor.getDropResult()}},{key:"didDrop",value:function(){return this.internalMonitor.didDrop()}},{key:"getInitialClientOffset",value:function(){return this.internalMonitor.getInitialClientOffset()}},{key:"getInitialSourceClientOffset",value:function(){return this.internalMonitor.getInitialSourceClientOffset()}},{key:"getSourceClientOffset",value:function(){return this.internalMonitor.getSourceClientOffset()}},{key:"getClientOffset",value:function(){return this.internalMonitor.getClientOffset()}},{key:"getDifferenceFromInitialOffset",value:function(){return this.internalMonitor.getDifferenceFromInitialOffset()}}]),e}()},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){var t=void 0,n=void 0,i=void 0,a=void 0;function l(){a&&(a(),a=null),t&&n&&(a=e.connectDropTarget(t,n,i))}return{receiveHandlerId:function(e){e!==t&&(t=e,l())},hooks:(0,r.default)({dropTarget:function(e,t){e===n&&(0,o.default)(t,i)||(n=e,i=t,l())}})}};var r=i(n(211)),o=i(n(212));function i(e){return e&&e.__esModule?e:{default:e}}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.getEmptyImage=t.NativeTypes=void 0,t.default=function(e){return new r.default(e)};var r=a(n(271)),o=a(n(277)),i=function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n]);return t.default=e,t}(n(90));function a(e){return e&&e.__esModule?e:{default:e}}t.NativeTypes=i,t.getEmptyImage=o.default},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=function(){function e(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,n,r){return n&&e(t.prototype,n),r&&e(t,r),t}}(),o=d(n(97)),i=d(n(272)),a=d(n(273)),l=n(214),s=n(274),c=n(276),u=function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n]);return t.default=e,t}(n(90));function d(e){return e&&e.__esModule?e:{default:e}}var f=function(){function e(t){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.actions=t.getActions(),this.monitor=t.getMonitor(),this.registry=t.getRegistry(),this.context=t.getContext(),this.sourcePreviewNodes={},this.sourcePreviewNodeOptions={},this.sourceNodes={},this.sourceNodeOptions={},this.enterLeaveCounter=new a.default,this.dragStartSourceIds=[],this.dropTargetIds=[],this.dragEnterTargetIds=[],this.currentNativeSource=null,this.currentNativeHandle=null,this.currentDragSourceNode=null,this.currentDragSourceNodeOffset=null,this.currentDragSourceNodeOffsetChanged=!1,this.altKeyPressed=!1,this.getSourceClientOffset=this.getSourceClientOffset.bind(this),this.handleTopDragStart=this.handleTopDragStart.bind(this),this.handleTopDragStartCapture=this.handleTopDragStartCapture.bind(this),this.handleTopDragEndCapture=this.handleTopDragEndCapture.bind(this),this.handleTopDragEnter=this.handleTopDragEnter.bind(this),this.handleTopDragEnterCapture=this.handleTopDragEnterCapture.bind(this),this.handleTopDragLeaveCapture=this.handleTopDragLeaveCapture.bind(this),this.handleTopDragOver=this.handleTopDragOver.bind(this),this.handleTopDragOverCapture=this.handleTopDragOverCapture.bind(this),this.handleTopDrop=this.handleTopDrop.bind(this),this.handleTopDropCapture=this.handleTopDropCapture.bind(this),this.handleSelectStart=this.handleSelectStart.bind(this),this.endDragIfSourceWasRemovedFromDOM=this.endDragIfSourceWasRemovedFromDOM.bind(this),this.endDragNativeItem=this.endDragNativeItem.bind(this),this.asyncEndDragNativeItem=this.asyncEndDragNativeItem.bind(this),this.isNodeInDocument=this.isNodeInDocument.bind(this)}return r(e,[{key:"setup",value:function(){if(void 0!==this.window){if(this.window.__isReactDndBackendSetUp)throw new Error("Cannot have two HTML5 backends at the same time.");this.window.__isReactDndBackendSetUp=!0,this.addEventListeners(this.window)}}},{key:"teardown",value:function(){void 0!==this.window&&(this.window.__isReactDndBackendSetUp=!1,this.removeEventListeners(this.window),this.clearCurrentDragSourceNode(),this.asyncEndDragFrameId&&this.window.cancelAnimationFrame(this.asyncEndDragFrameId))}},{key:"addEventListeners",value:function(e){e.addEventListener&&(e.addEventListener("dragstart",this.handleTopDragStart),e.addEventListener("dragstart",this.handleTopDragStartCapture,!0),e.addEventListener("dragend",this.handleTopDragEndCapture,!0),e.addEventListener("dragenter",this.handleTopDragEnter),e.addEventListener("dragenter",this.handleTopDragEnterCapture,!0),e.addEventListener("dragleave",this.handleTopDragLeaveCapture,!0),e.addEventListener("dragover",this.handleTopDragOver),e.addEventListener("dragover",this.handleTopDragOverCapture,!0),e.addEventListener("drop",this.handleTopDrop),e.addEventListener("drop",this.handleTopDropCapture,!0))}},{key:"removeEventListeners",value:function(e){e.removeEventListener&&(e.removeEventListener("dragstart",this.handleTopDragStart),e.removeEventListener("dragstart",this.handleTopDragStartCapture,!0),e.removeEventListener("dragend",this.handleTopDragEndCapture,!0),e.removeEventListener("dragenter",this.handleTopDragEnter),e.removeEventListener("dragenter",this.handleTopDragEnterCapture,!0),e.removeEventListener("dragleave",this.handleTopDragLeaveCapture,!0),e.removeEventListener("dragover",this.handleTopDragOver),e.removeEventListener("dragover",this.handleTopDragOverCapture,!0),e.removeEventListener("drop",this.handleTopDrop),e.removeEventListener("drop",this.handleTopDropCapture,!0))}},{key:"connectDragPreview",value:function(e,t,n){var r=this;return this.sourcePreviewNodeOptions[e]=n,this.sourcePreviewNodes[e]=t,function(){delete r.sourcePreviewNodes[e],delete r.sourcePreviewNodeOptions[e]}}},{key:"connectDragSource",value:function(e,t,n){var r=this;this.sourceNodes[e]=t,this.sourceNodeOptions[e]=n;var o=function(t){return r.handleDragStart(t,e)},i=function(t){return r.handleSelectStart(t,e)};return t.setAttribute("draggable",!0),t.addEventListener("dragstart",o),t.addEventListener("selectstart",i),function(){delete r.sourceNodes[e],delete r.sourceNodeOptions[e],t.removeEventListener("dragstart",o),t.removeEventListener("selectstart",i),t.setAttribute("draggable",!1)}}},{key:"connectDropTarget",value:function(e,t){var n=this,r=function(t){return n.handleDragEnter(t,e)},o=function(t){return n.handleDragOver(t,e)},i=function(t){return n.handleDrop(t,e)};return t.addEventListener("dragenter",r),t.addEventListener("dragover",o),t.addEventListener("drop",i),function(){t.removeEventListener("dragenter",r),t.removeEventListener("dragover",o),t.removeEventListener("drop",i)}}},{key:"getCurrentSourceNodeOptions",value:function(){var e=this.monitor.getSourceId(),t=this.sourceNodeOptions[e];return(0,o.default)(t||{},{dropEffect:this.altKeyPressed?"copy":"move"})}},{key:"getCurrentDropEffect",value:function(){return this.isDraggingNativeItem()?"copy":this.getCurrentSourceNodeOptions().dropEffect}},{key:"getCurrentSourcePreviewNodeOptions",value:function(){var e=this.monitor.getSourceId(),t=this.sourcePreviewNodeOptions[e];return(0,o.default)(t||{},{anchorX:.5,anchorY:.5,captureDraggingState:!1})}},{key:"getSourceClientOffset",value:function(e){return(0,s.getNodeClientOffset)(this.sourceNodes[e])}},{key:"isDraggingNativeItem",value:function(){var e=this.monitor.getItemType();return Object.keys(u).some((function(t){return u[t]===e}))}},{key:"beginDragNativeItem",value:function(e){this.clearCurrentDragSourceNode();var t=(0,c.createNativeDragSource)(e);this.currentNativeSource=new t,this.currentNativeHandle=this.registry.addSource(e,this.currentNativeSource),this.actions.beginDrag([this.currentNativeHandle]),(0,l.isFirefox)()&&this.window.addEventListener("mouseover",this.asyncEndDragNativeItem,!0)}},{key:"asyncEndDragNativeItem",value:function(){this.asyncEndDragFrameId=this.window.requestAnimationFrame(this.endDragNativeItem),(0,l.isFirefox)()&&(this.window.removeEventListener("mouseover",this.asyncEndDragNativeItem,!0),this.enterLeaveCounter.reset())}},{key:"endDragNativeItem",value:function(){this.isDraggingNativeItem()&&(this.actions.endDrag(),this.registry.removeSource(this.currentNativeHandle),this.currentNativeHandle=null,this.currentNativeSource=null)}},{key:"isNodeInDocument",value:function(e){return!(!document.body.contains(e)&&!this.window)&&this.window.document.body.contains(e)}},{key:"endDragIfSourceWasRemovedFromDOM",value:function(){var e=this.currentDragSourceNode;this.isNodeInDocument(e)||this.clearCurrentDragSourceNode()&&this.actions.endDrag()}},{key:"setCurrentDragSourceNode",value:function(e){this.clearCurrentDragSourceNode(),this.currentDragSourceNode=e,this.currentDragSourceNodeOffset=(0,s.getNodeClientOffset)(e),this.currentDragSourceNodeOffsetChanged=!1,this.window.addEventListener("mousemove",this.endDragIfSourceWasRemovedFromDOM,!0)}},{key:"clearCurrentDragSourceNode",value:function(){return!!this.currentDragSourceNode&&(this.currentDragSourceNode=null,this.currentDragSourceNodeOffset=null,this.currentDragSourceNodeOffsetChanged=!1,this.window.removeEventListener("mousemove",this.endDragIfSourceWasRemovedFromDOM,!0),!0)}},{key:"checkIfCurrentDragSourceRectChanged",value:function(){var e=this.currentDragSourceNode;return!!e&&(!!this.currentDragSourceNodeOffsetChanged||(this.currentDragSourceNodeOffsetChanged=!(0,i.default)((0,s.getNodeClientOffset)(e),this.currentDragSourceNodeOffset),this.currentDragSourceNodeOffsetChanged))}},{key:"handleTopDragStartCapture",value:function(){this.clearCurrentDragSourceNode(),this.dragStartSourceIds=[]}},{key:"handleDragStart",value:function(e,t){this.dragStartSourceIds.unshift(t)}},{key:"handleTopDragStart",value:function(e){var t=this,n=this.dragStartSourceIds;this.dragStartSourceIds=null;var r=(0,s.getEventClientOffset)(e);this.monitor.isDragging()&&this.actions.endDrag(),this.actions.beginDrag(n,{publishSource:!1,getSourceClientOffset:this.getSourceClientOffset,clientOffset:r});var o=e.dataTransfer,i=(0,c.matchNativeItemType)(o);if(this.monitor.isDragging()){if("function"==typeof o.setDragImage){var a=this.monitor.getSourceId(),l=this.sourceNodes[a],u=this.sourcePreviewNodes[a]||l,d=this.getCurrentSourcePreviewNodeOptions(),f={anchorX:d.anchorX,anchorY:d.anchorY},h={offsetX:d.offsetX,offsetY:d.offsetY},p=(0,s.getDragPreviewOffset)(l,u,r,f,h);o.setDragImage(u,p.x,p.y)}try{o.setData("application/json",{})}catch(e){}this.setCurrentDragSourceNode(e.target),this.getCurrentSourcePreviewNodeOptions().captureDraggingState?this.actions.publishDragSource():setTimeout((function(){return t.actions.publishDragSource()}))}else if(i)this.beginDragNativeItem(i);else{if(!(o.types||e.target.hasAttribute&&e.target.hasAttribute("draggable")))return;e.preventDefault()}}},{key:"handleTopDragEndCapture",value:function(){this.clearCurrentDragSourceNode()&&this.actions.endDrag()}},{key:"handleTopDragEnterCapture",value:function(e){if(this.dragEnterTargetIds=[],this.enterLeaveCounter.enter(e.target)&&!this.monitor.isDragging()){var t=e.dataTransfer,n=(0,c.matchNativeItemType)(t);n&&this.beginDragNativeItem(n)}}},{key:"handleDragEnter",value:function(e,t){this.dragEnterTargetIds.unshift(t)}},{key:"handleTopDragEnter",value:function(e){var t=this,n=this.dragEnterTargetIds;this.dragEnterTargetIds=[],this.monitor.isDragging()&&(this.altKeyPressed=e.altKey,(0,l.isFirefox)()||this.actions.hover(n,{clientOffset:(0,s.getEventClientOffset)(e)}),n.some((function(e){return t.monitor.canDropOnTarget(e)}))&&(e.preventDefault(),e.dataTransfer.dropEffect=this.getCurrentDropEffect()))}},{key:"handleTopDragOverCapture",value:function(){this.dragOverTargetIds=[]}},{key:"handleDragOver",value:function(e,t){this.dragOverTargetIds.unshift(t)}},{key:"handleTopDragOver",value:function(e){var t=this,n=this.dragOverTargetIds;if(this.dragOverTargetIds=[],!this.monitor.isDragging())return e.preventDefault(),void(e.dataTransfer.dropEffect="none");this.altKeyPressed=e.altKey,this.actions.hover(n,{clientOffset:(0,s.getEventClientOffset)(e)}),n.some((function(e){return t.monitor.canDropOnTarget(e)}))?(e.preventDefault(),e.dataTransfer.dropEffect=this.getCurrentDropEffect()):this.isDraggingNativeItem()?(e.preventDefault(),e.dataTransfer.dropEffect="none"):this.checkIfCurrentDragSourceRectChanged()&&(e.preventDefault(),e.dataTransfer.dropEffect="move")}},{key:"handleTopDragLeaveCapture",value:function(e){this.isDraggingNativeItem()&&e.preventDefault(),this.enterLeaveCounter.leave(e.target)&&this.isDraggingNativeItem()&&this.endDragNativeItem()}},{key:"handleTopDropCapture",value:function(e){this.dropTargetIds=[],e.preventDefault(),this.isDraggingNativeItem()&&this.currentNativeSource.mutateItemByReadingDataTransfer(e.dataTransfer),this.enterLeaveCounter.reset()}},{key:"handleDrop",value:function(e,t){this.dropTargetIds.unshift(t)}},{key:"handleTopDrop",value:function(e){var t=this.dropTargetIds;this.dropTargetIds=[],this.actions.hover(t,{clientOffset:(0,s.getEventClientOffset)(e)}),this.actions.drop({dropEffect:this.getCurrentDropEffect()}),this.isDraggingNativeItem()?this.endDragNativeItem():this.endDragIfSourceWasRemovedFromDOM()}},{key:"handleSelectStart",value:function(e){var t=e.target;"function"==typeof t.dragDrop&&("INPUT"===t.tagName||"SELECT"===t.tagName||"TEXTAREA"===t.tagName||t.isContentEditable||(e.preventDefault(),t.dragDrop()))}},{key:"window",get:function(){return this.context&&this.context.window?this.context.window:"undefined"!=typeof window?window:void 0}}]),e}();t.default=f},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e,t){if(e===t)return!0;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(var o=Object.prototype.hasOwnProperty,i=0;i<n.length;i+=1){if(!o.call(t,n[i])||e[n[i]]!==t[n[i]])return!1;if(e[n[i]]!==t[n[i]])return!1}return!0}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=function(){function e(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,n,r){return n&&e(t.prototype,n),r&&e(t,r),t}}(),o=a(n(98)),i=a(n(58));function a(e){return e&&e.__esModule?e:{default:e}}var l=function(){function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.entered=[]}return r(e,[{key:"enter",value:function(e){var t=this.entered.length;return this.entered=(0,o.default)(this.entered.filter((function(t){return document.documentElement.contains(t)&&(!t.contains||t.contains(e))})),[e]),0===t&&this.entered.length>0}},{key:"leave",value:function(e){var t=this.entered.length;return this.entered=(0,i.default)(this.entered.filter((function(e){return document.documentElement.contains(e)})),e),t>0&&0===this.entered.length}},{key:"reset",value:function(){this.entered=[]}}]),e}();t.default=l},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.getNodeClientOffset=a,t.getEventClientOffset=function(e){return{x:e.clientX,y:e.clientY}},t.getDragPreviewOffset=function(e,t,n,r,l){var s,c,u="IMG"===(s=t).nodeName&&((0,o.isFirefox)()||!document.documentElement.contains(s)),d=a(u?e:t),f={x:n.x-d.x,y:n.y-d.y},h=e.offsetWidth,p=e.offsetHeight,g=r.anchorX,v=r.anchorY,m=function(e,t,n,r){var i=e?t.width:n,a=e?t.height:r;return(0,o.isSafari)()&&e&&(a/=window.devicePixelRatio,i/=window.devicePixelRatio),{dragPreviewWidth:i,dragPreviewHeight:a}}(u,t,h,p),y=m.dragPreviewWidth,_=m.dragPreviewHeight,b=l.offsetX,w=l.offsetY,S=0===w||w;return{x:0===b||b?b:new i.default([0,.5,1],[f.x,f.x/h*y,f.x+y-h]).interpolate(g),y:S?w:(c=new i.default([0,.5,1],[f.y,f.y/p*_,f.y+_-p]).interpolate(v),(0,o.isSafari)()&&u&&(c+=(window.devicePixelRatio-1)*_),c)}};var r,o=n(214),i=(r=n(275))&&r.__esModule?r:{default:r};function a(e){var t=1===e.nodeType?e:e.parentElement;if(!t)return null;var n=t.getBoundingClientRect(),r=n.top;return{x:n.left,y:r}}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=function(){function e(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,n,r){return n&&e(t.prototype,n),r&&e(t,r),t}}(),o=function(){function e(t,n){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e);for(var r=t.length,o=[],i=0;i<r;i++)o.push(i);o.sort((function(e,n){return t[e]<t[n]?-1:1}));for(var a=[],l=[],s=[],c=void 0,u=void 0,d=0;d<r-1;d++)c=t[d+1]-t[d],u=n[d+1]-n[d],l.push(c),a.push(u),s.push(u/c);for(var f=[s[0]],h=0;h<l.length-1;h++){var p=s[h],g=s[h+1];if(p*g<=0)f.push(0);else{c=l[h];var v=l[h+1],m=c+v;f.push(3*m/((m+v)/p+(m+c)/g))}}f.push(s[s.length-1]);for(var y=[],_=[],b=void 0,w=0;w<f.length-1;w++){b=s[w];var S=f[w],x=1/l[w],C=S+f[w+1]-b-b;y.push((b-S-C)*x),_.push(C*x*x)}this.xs=t,this.ys=n,this.c1s=f,this.c2s=y,this.c3s=_}return r(e,[{key:"interpolate",value:function(e){var t=this.xs,n=this.ys,r=this.c1s,o=this.c2s,i=this.c3s,a=t.length-1;if(e===t[a])return n[a];for(var l=0,s=i.length-1,c=void 0;l<=s;){var u=t[c=Math.floor(.5*(l+s))];if(u<e)l=c+1;else{if(!(u>e))return n[c];s=c-1}}var d=e-t[a=Math.max(0,s)],f=d*d;return n[a]+r[a]*d+o[a]*f+i[a]*d*f}}]),e}();t.default=o},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r,o=function(){function e(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,n,r){return n&&e(t.prototype,n),r&&e(t,r),t}}();t.createNativeDragSource=function(e){var t=s[e],n=t.exposeProperty,r=t.matchesTypes,i=t.getData;return function(){function e(){var t,r;!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.item=(t={},(r={})[n]=r[n]||{},r[n].get=function(){return console.warn("Browser doesn't allow reading \""+n+'" until the drop event.'),null},function(e,t){for(var n in t){var r=t[n];r.configurable=r.enumerable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,n,r)}}(t,r),t)}return o(e,[{key:"mutateItemByReadingDataTransfer",value:function(e){delete this.item[n],this.item[n]=i(e,r)}},{key:"canDrag",value:function(){return!0}},{key:"beginDrag",value:function(){return this.item}},{key:"isDragging",value:function(e,t){return t===e.getSourceId()}},{key:"endDrag",value:function(){}}]),e}()},t.matchNativeItemType=function(e){var t=Array.prototype.slice.call(e.types||[]);return Object.keys(s).filter((function(e){return s[e].matchesTypes.some((function(e){return t.indexOf(e)>-1}))}))[0]||null};var i=function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n]);return t.default=e,t}(n(90));function a(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function l(e,t,n){var r=t.reduce((function(t,n){return t||e.getData(n)}),null);return null!=r?r:n}var s=(a(r={},i.FILE,{exposeProperty:"files",matchesTypes:["Files"],getData:function(e){return Array.prototype.slice.call(e.files)}}),a(r,i.URL,{exposeProperty:"urls",matchesTypes:["Url","text/uri-list"],getData:function(e,t){return l(e,t,"").split("\n")}}),a(r,i.TEXT,{exposeProperty:"text",matchesTypes:["Text","text/plain"],getData:function(e,t){return l(e,t,"")}}),r)},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(){return r||((r=new Image).src="data:image/gif;base64,R0lGODlhAQABAAAAACH5BAEKAAEALAAAAAABAAEAAAICTAEAOw=="),r};var r=void 0},,,,,,,,,,,,,,function(e,t,n){"use strict";n.r(t),n.d(t,"ArrowKeyStepper",(function(){return te})),n.d(t,"AutoSizer",(function(){return ie})),n.d(t,"CellMeasurer",(function(){return ce})),n.d(t,"CellMeasurerCache",(function(){return ue})),n.d(t,"Collection",(function(){return _e})),n.d(t,"ColumnSizer",(function(){return be})),n.d(t,"accessibilityOverscanIndicesGetter",(function(){return X})),n.d(t,"defaultCellRangeRenderer",(function(){return P})),n.d(t,"defaultOverscanIndicesGetter",(function(){return k})),n.d(t,"Grid",(function(){return Z})),n.d(t,"InfiniteLoader",(function(){return xe})),n.d(t,"List",(function(){return Te})),n.d(t,"createMasonryCellPositioner",(function(){return Qe})),n.d(t,"Masonry",(function(){return $e})),n.d(t,"MultiGrid",(function(){return ot})),n.d(t,"ScrollSync",(function(){return it})),n.d(t,"createTableMultiSort",(function(){return at})),n.d(t,"defaultTableCellDataGetter",(function(){return lt})),n.d(t,"defaultTableCellRenderer",(function(){return st})),n.d(t,"defaultTableHeaderRenderer",(function(){return ft})),n.d(t,"defaultTableHeaderRowRenderer",(function(){return ct})),n.d(t,"defaultTableRowRenderer",(function(){return ht})),n.d(t,"Table",(function(){return mt})),n.d(t,"Column",(function(){return pt})),n.d(t,"SortDirection",(function(){return ut})),n.d(t,"SortIndicator",(function(){return dt})),n.d(t,"WindowScroller",(function(){return Nt}));var r=n(4),o=n.n(r),i=n(5),a=n.n(i),l=n(8),s=n.n(l),c=n(6),u=n.n(c),d=n(3),f=n.n(d),h=n(7),p=n.n(h),g=n(1),v=n.n(g),m=n(0),y=n(21),_=n(15),b=n.n(_);function w(e){var t,n,r="";if("string"==typeof e||"number"==typeof e)r+=e;else if("object"==typeof e)if(Array.isArray(e))for(t=0;t<e.length;t++)e[t]&&(n=w(e[t]))&&(r&&(r+=" "),r+=n);else for(t in e)e[t]&&(r&&(r+=" "),r+=t);return r}var S=function(){for(var e,t,n=0,r="";n<arguments.length;)(e=arguments[n++])&&(t=w(e))&&(r&&(r+=" "),r+=t);return r};function x(e){var t=e.cellCount,n=e.cellSize,r=e.computeMetadataCallback,o=e.computeMetadataCallbackProps,i=e.nextCellsCount,a=e.nextCellSize,l=e.nextScrollToIndex,s=e.scrollToIndex,c=e.updateScrollOffsetForScrollToIndex;t===i&&("number"!=typeof n&&"number"!=typeof a||n===a)||(r(o),s>=0&&s===l&&c())}var C=n(30),O=n.n(C),T=function(){function e(t){var n=t.cellCount,r=t.cellSizeGetter,i=t.estimatedCellSize;o()(this,e),v()(this,"_cellSizeAndPositionData",{}),v()(this,"_lastMeasuredIndex",-1),v()(this,"_lastBatchedIndex",-1),v()(this,"_cellCount",void 0),v()(this,"_cellSizeGetter",void 0),v()(this,"_estimatedCellSize",void 0),this._cellSizeGetter=r,this._cellCount=n,this._estimatedCellSize=i}return a()(e,[{key:"areOffsetsAdjusted",value:function(){return!1}},{key:"configure",value:function(e){var t=e.cellCount,n=e.estimatedCellSize,r=e.cellSizeGetter;this._cellCount=t,this._estimatedCellSize=n,this._cellSizeGetter=r}},{key:"getCellCount",value:function(){return this._cellCount}},{key:"getEstimatedCellSize",value:function(){return this._estimatedCellSize}},{key:"getLastMeasuredIndex",value:function(){return this._lastMeasuredIndex}},{key:"getOffsetAdjustment",value:function(){return 0}},{key:"getSizeAndPositionOfCell",value:function(e){if(e<0||e>=this._cellCount)throw Error("Requested index ".concat(e," is outside of range 0..").concat(this._cellCount));if(e>this._lastMeasuredIndex)for(var t=this.getSizeAndPositionOfLastMeasuredCell(),n=t.offset+t.size,r=this._lastMeasuredIndex+1;r<=e;r++){var o=this._cellSizeGetter({index:r});if(void 0===o||isNaN(o))throw Error("Invalid size returned for cell ".concat(r," of value ").concat(o));null===o?(this._cellSizeAndPositionData[r]={offset:n,size:0},this._lastBatchedIndex=e):(this._cellSizeAndPositionData[r]={offset:n,size:o},n+=o,this._lastMeasuredIndex=e)}return this._cellSizeAndPositionData[e]}},{key:"getSizeAndPositionOfLastMeasuredCell",value:function(){return this._lastMeasuredIndex>=0?this._cellSizeAndPositionData[this._lastMeasuredIndex]:{offset:0,size:0}}},{key:"getTotalSize",value:function(){var e=this.getSizeAndPositionOfLastMeasuredCell();return e.offset+e.size+(this._cellCount-this._lastMeasuredIndex-1)*this._estimatedCellSize}},{key:"getUpdatedOffsetForIndex",value:function(e){var t=e.align,n=void 0===t?"auto":t,r=e.containerSize,o=e.currentOffset,i=e.targetIndex;if(r<=0)return 0;var a,l=this.getSizeAndPositionOfCell(i),s=l.offset,c=s-r+l.size;switch(n){case"start":a=s;break;case"end":a=c;break;case"center":a=s-(r-l.size)/2;break;default:a=Math.max(c,Math.min(s,o))}var u=this.getTotalSize();return Math.max(0,Math.min(u-r,a))}},{key:"getVisibleCellRange",value:function(e){var t=e.containerSize,n=e.offset;if(0===this.getTotalSize())return{};var r=n+t,o=this._findNearestCell(n),i=this.getSizeAndPositionOfCell(o);n=i.offset+i.size;for(var a=o;n<r&&a<this._cellCount-1;)a++,n+=this.getSizeAndPositionOfCell(a).size;return{start:o,stop:a}}},{key:"resetCell",value:function(e){this._lastMeasuredIndex=Math.min(this._lastMeasuredIndex,e-1)}},{key:"_binarySearch",value:function(e,t,n){for(;t<=e;){var r=t+Math.floor((e-t)/2),o=this.getSizeAndPositionOfCell(r).offset;if(o===n)return r;o<n?t=r+1:o>n&&(e=r-1)}return t>0?t-1:0}},{key:"_exponentialSearch",value:function(e,t){for(var n=1;e<this._cellCount&&this.getSizeAndPositionOfCell(e).offset<t;)e+=n,n*=2;return this._binarySearch(Math.min(e,this._cellCount-1),Math.floor(e/2),t)}},{key:"_findNearestCell",value:function(e){if(isNaN(e))throw Error("Invalid offset ".concat(e," specified"));e=Math.max(0,e);var t=this.getSizeAndPositionOfLastMeasuredCell(),n=Math.max(0,this._lastMeasuredIndex);return t.offset>=e?this._binarySearch(n,0,e):this._exponentialSearch(n,e)}}]),e}(),I=function(){function e(t){var n=t.maxScrollSize,r=void 0===n?"undefined"!=typeof window&&window.chrome?16777100:15e5:n,i=O()(t,["maxScrollSize"]);o()(this,e),v()(this,"_cellSizeAndPositionManager",void 0),v()(this,"_maxScrollSize",void 0),this._cellSizeAndPositionManager=new T(i),this._maxScrollSize=r}return a()(e,[{key:"areOffsetsAdjusted",value:function(){return this._cellSizeAndPositionManager.getTotalSize()>this._maxScrollSize}},{key:"configure",value:function(e){this._cellSizeAndPositionManager.configure(e)}},{key:"getCellCount",value:function(){return this._cellSizeAndPositionManager.getCellCount()}},{key:"getEstimatedCellSize",value:function(){return this._cellSizeAndPositionManager.getEstimatedCellSize()}},{key:"getLastMeasuredIndex",value:function(){return this._cellSizeAndPositionManager.getLastMeasuredIndex()}},{key:"getOffsetAdjustment",value:function(e){var t=e.containerSize,n=e.offset,r=this._cellSizeAndPositionManager.getTotalSize(),o=this.getTotalSize(),i=this._getOffsetPercentage({containerSize:t,offset:n,totalSize:o});return Math.round(i*(o-r))}},{key:"getSizeAndPositionOfCell",value:function(e){return this._cellSizeAndPositionManager.getSizeAndPositionOfCell(e)}},{key:"getSizeAndPositionOfLastMeasuredCell",value:function(){return this._cellSizeAndPositionManager.getSizeAndPositionOfLastMeasuredCell()}},{key:"getTotalSize",value:function(){return Math.min(this._maxScrollSize,this._cellSizeAndPositionManager.getTotalSize())}},{key:"getUpdatedOffsetForIndex",value:function(e){var t=e.align,n=void 0===t?"auto":t,r=e.containerSize,o=e.currentOffset,i=e.targetIndex;o=this._safeOffsetToOffset({containerSize:r,offset:o});var a=this._cellSizeAndPositionManager.getUpdatedOffsetForIndex({align:n,containerSize:r,currentOffset:o,targetIndex:i});return this._offsetToSafeOffset({containerSize:r,offset:a})}},{key:"getVisibleCellRange",value:function(e){var t=e.containerSize,n=e.offset;return n=this._safeOffsetToOffset({containerSize:t,offset:n}),this._cellSizeAndPositionManager.getVisibleCellRange({containerSize:t,offset:n})}},{key:"resetCell",value:function(e){this._cellSizeAndPositionManager.resetCell(e)}},{key:"_getOffsetPercentage",value:function(e){var t=e.containerSize,n=e.offset,r=e.totalSize;return r<=t?0:n/(r-t)}},{key:"_offsetToSafeOffset",value:function(e){var t=e.containerSize,n=e.offset,r=this._cellSizeAndPositionManager.getTotalSize(),o=this.getTotalSize();if(r===o)return n;var i=this._getOffsetPercentage({containerSize:t,offset:n,totalSize:r});return Math.round(i*(o-t))}},{key:"_safeOffsetToOffset",value:function(e){var t=e.containerSize,n=e.offset,r=this._cellSizeAndPositionManager.getTotalSize(),o=this.getTotalSize();if(r===o)return n;var i=this._getOffsetPercentage({containerSize:t,offset:n,totalSize:o});return Math.round(i*(r-t))}}]),e}();function D(){var e=!(arguments.length>0&&void 0!==arguments[0])||arguments[0],t={};return function(n){var r=n.callback,o=n.indices,i=Object.keys(o),a=!e||i.every((function(e){var t=o[e];return Array.isArray(t)?t.length>0:t>=0})),l=i.length!==Object.keys(t).length||i.some((function(e){var n=t[e],r=o[e];return Array.isArray(r)?n.join(",")!==r.join(","):n!==r}));t=o,a&&l&&r(o)}}function k(e){var t=e.cellCount,n=e.overscanCellsCount,r=e.scrollDirection,o=e.startIndex,i=e.stopIndex;return 1===r?{overscanStartIndex:Math.max(0,o),overscanStopIndex:Math.min(t-1,i+n)}:{overscanStartIndex:Math.max(0,o-n),overscanStopIndex:Math.min(t-1,i)}}function R(e){var t=e.cellSize,n=e.cellSizeAndPositionManager,r=e.previousCellsCount,o=e.previousCellSize,i=e.previousScrollToAlignment,a=e.previousScrollToIndex,l=e.previousSize,s=e.scrollOffset,c=e.scrollToAlignment,u=e.scrollToIndex,d=e.size,f=e.sizeJustIncreasedFromZero,h=e.updateScrollIndexCallback,p=n.getCellCount(),g=u>=0&&u<p;g&&(d!==l||f||!o||"number"==typeof t&&t!==o||c!==i||u!==a)?h(u):!g&&p>0&&(d<l||p<r)&&s>n.getTotalSize()-d&&h(p-1)}function P(e){for(var t=e.cellCache,n=e.cellRenderer,r=e.columnSizeAndPositionManager,o=e.columnStartIndex,i=e.columnStopIndex,a=e.deferredMeasurementCache,l=e.horizontalOffsetAdjustment,s=e.isScrolling,c=e.isScrollingOptOut,u=e.parent,d=e.rowSizeAndPositionManager,f=e.rowStartIndex,h=e.rowStopIndex,p=e.styleCache,g=e.verticalOffsetAdjustment,v=e.visibleColumnIndices,m=e.visibleRowIndices,y=[],_=r.areOffsetsAdjusted()||d.areOffsetsAdjusted(),b=!s&&!_,w=f;w<=h;w++)for(var S=d.getSizeAndPositionOfCell(w),x=o;x<=i;x++){var C=r.getSizeAndPositionOfCell(x),O=x>=v.start&&x<=v.stop&&w>=m.start&&w<=m.stop,T="".concat(w,"-").concat(x),I=void 0;b&&p[T]?I=p[T]:a&&!a.has(w,x)?I={height:"auto",left:0,position:"absolute",top:0,width:"auto"}:(I={height:S.size,left:C.offset+l,position:"absolute",top:S.offset+g,width:C.size},p[T]=I);var D={columnIndex:x,isScrolling:s,isVisible:O,key:T,parent:u,rowIndex:w,style:I},k=void 0;!c&&!s||l||g?k=n(D):(t[T]||(t[T]=n(D)),k=t[T]),null!=k&&!1!==k&&y.push(k)}return y}var E,M,j=!("undefined"==typeof window||!window.document||!window.document.createElement);function z(e){if((!E&&0!==E||e)&&j){var t=document.createElement("div");t.style.position="absolute",t.style.top="-9999px",t.style.width="50px",t.style.height="50px",t.style.overflow="scroll",document.body.appendChild(t),E=t.offsetWidth-t.clientWidth,document.body.removeChild(t)}return E}var N,L,A=(M="undefined"!=typeof window?window:"undefined"!=typeof self?self:{}).requestAnimationFrame||M.webkitRequestAnimationFrame||M.mozRequestAnimationFrame||M.oRequestAnimationFrame||M.msRequestAnimationFrame||function(e){return M.setTimeout(e,1e3/60)},H=M.cancelAnimationFrame||M.webkitCancelAnimationFrame||M.mozCancelAnimationFrame||M.oCancelAnimationFrame||M.msCancelAnimationFrame||function(e){M.clearTimeout(e)},G=A,F=H,W=function(e){return F(e.id)},U=function(e,t){var n;Promise.resolve().then((function(){n=Date.now()}));var r={id:G((function o(){Date.now()-n>=t?e.call():r.id=G(o)}))};return r};function B(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function V(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?B(n,!0).forEach((function(t){v()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):B(n).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var K=(L=N=function(e){function t(e){var n;o()(this,t),n=s()(this,u()(t).call(this,e)),v()(f()(n),"_onGridRenderedMemoizer",D()),v()(f()(n),"_onScrollMemoizer",D(!1)),v()(f()(n),"_deferredInvalidateColumnIndex",null),v()(f()(n),"_deferredInvalidateRowIndex",null),v()(f()(n),"_recomputeScrollLeftFlag",!1),v()(f()(n),"_recomputeScrollTopFlag",!1),v()(f()(n),"_horizontalScrollBarSize",0),v()(f()(n),"_verticalScrollBarSize",0),v()(f()(n),"_scrollbarPresenceChanged",!1),v()(f()(n),"_scrollingContainer",void 0),v()(f()(n),"_childrenToDisplay",void 0),v()(f()(n),"_columnStartIndex",void 0),v()(f()(n),"_columnStopIndex",void 0),v()(f()(n),"_rowStartIndex",void 0),v()(f()(n),"_rowStopIndex",void 0),v()(f()(n),"_renderedColumnStartIndex",0),v()(f()(n),"_renderedColumnStopIndex",0),v()(f()(n),"_renderedRowStartIndex",0),v()(f()(n),"_renderedRowStopIndex",0),v()(f()(n),"_initialScrollTop",void 0),v()(f()(n),"_initialScrollLeft",void 0),v()(f()(n),"_disablePointerEventsTimeoutId",void 0),v()(f()(n),"_styleCache",{}),v()(f()(n),"_cellCache",{}),v()(f()(n),"_debounceScrollEndedCallback",(function(){n._disablePointerEventsTimeoutId=null,n.setState({isScrolling:!1,needToResetStyleCache:!1})})),v()(f()(n),"_invokeOnGridRenderedHelper",(function(){var e=n.props.onSectionRendered;n._onGridRenderedMemoizer({callback:e,indices:{columnOverscanStartIndex:n._columnStartIndex,columnOverscanStopIndex:n._columnStopIndex,columnStartIndex:n._renderedColumnStartIndex,columnStopIndex:n._renderedColumnStopIndex,rowOverscanStartIndex:n._rowStartIndex,rowOverscanStopIndex:n._rowStopIndex,rowStartIndex:n._renderedRowStartIndex,rowStopIndex:n._renderedRowStopIndex}})})),v()(f()(n),"_setScrollingContainerRef",(function(e){n._scrollingContainer=e})),v()(f()(n),"_onScroll",(function(e){e.target===n._scrollingContainer&&n.handleScrollEvent(e.target)}));var r=new I({cellCount:e.columnCount,cellSizeGetter:function(n){return t._wrapSizeGetter(e.columnWidth)(n)},estimatedCellSize:t._getEstimatedColumnSize(e)}),i=new I({cellCount:e.rowCount,cellSizeGetter:function(n){return t._wrapSizeGetter(e.rowHeight)(n)},estimatedCellSize:t._getEstimatedRowSize(e)});return n.state={instanceProps:{columnSizeAndPositionManager:r,rowSizeAndPositionManager:i,prevColumnWidth:e.columnWidth,prevRowHeight:e.rowHeight,prevColumnCount:e.columnCount,prevRowCount:e.rowCount,prevIsScrolling:!0===e.isScrolling,prevScrollToColumn:e.scrollToColumn,prevScrollToRow:e.scrollToRow,scrollbarSize:0,scrollbarSizeMeasured:!1},isScrolling:!1,scrollDirectionHorizontal:1,scrollDirectionVertical:1,scrollLeft:0,scrollTop:0,scrollPositionChangeReason:null,needToResetStyleCache:!1},e.scrollToRow>0&&(n._initialScrollTop=n._getCalculatedScrollTop(e,n.state)),e.scrollToColumn>0&&(n._initialScrollLeft=n._getCalculatedScrollLeft(e,n.state)),n}return p()(t,e),a()(t,[{key:"getOffsetForCell",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.alignment,n=void 0===t?this.props.scrollToAlignment:t,r=e.columnIndex,o=void 0===r?this.props.scrollToColumn:r,i=e.rowIndex,a=void 0===i?this.props.scrollToRow:i,l=V({},this.props,{scrollToAlignment:n,scrollToColumn:o,scrollToRow:a});return{scrollLeft:this._getCalculatedScrollLeft(l),scrollTop:this._getCalculatedScrollTop(l)}}},{key:"getTotalRowsHeight",value:function(){return this.state.instanceProps.rowSizeAndPositionManager.getTotalSize()}},{key:"getTotalColumnsWidth",value:function(){return this.state.instanceProps.columnSizeAndPositionManager.getTotalSize()}},{key:"handleScrollEvent",value:function(e){var t=e.scrollLeft,n=void 0===t?0:t,r=e.scrollTop,o=void 0===r?0:r;if(!(o<0)){this._debounceScrollEnded();var i=this.props,a=i.autoHeight,l=i.autoWidth,s=i.height,c=i.width,u=this.state.instanceProps,d=u.scrollbarSize,f=u.rowSizeAndPositionManager.getTotalSize(),h=u.columnSizeAndPositionManager.getTotalSize(),p=Math.min(Math.max(0,h-c+d),n),g=Math.min(Math.max(0,f-s+d),o);if(this.state.scrollLeft!==p||this.state.scrollTop!==g){var v={isScrolling:!0,scrollDirectionHorizontal:p!==this.state.scrollLeft?p>this.state.scrollLeft?1:-1:this.state.scrollDirectionHorizontal,scrollDirectionVertical:g!==this.state.scrollTop?g>this.state.scrollTop?1:-1:this.state.scrollDirectionVertical,scrollPositionChangeReason:"observed"};a||(v.scrollTop=g),l||(v.scrollLeft=p),v.needToResetStyleCache=!1,this.setState(v)}this._invokeOnScrollMemoizer({scrollLeft:p,scrollTop:g,totalColumnsWidth:h,totalRowsHeight:f})}}},{key:"invalidateCellSizeAfterRender",value:function(e){var t=e.columnIndex,n=e.rowIndex;this._deferredInvalidateColumnIndex="number"==typeof this._deferredInvalidateColumnIndex?Math.min(this._deferredInvalidateColumnIndex,t):t,this._deferredInvalidateRowIndex="number"==typeof this._deferredInvalidateRowIndex?Math.min(this._deferredInvalidateRowIndex,n):n}},{key:"measureAllCells",value:function(){var e=this.props,t=e.columnCount,n=e.rowCount,r=this.state.instanceProps;r.columnSizeAndPositionManager.getSizeAndPositionOfCell(t-1),r.rowSizeAndPositionManager.getSizeAndPositionOfCell(n-1)}},{key:"recomputeGridSize",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.columnIndex,n=void 0===t?0:t,r=e.rowIndex,o=void 0===r?0:r,i=this.props,a=i.scrollToColumn,l=i.scrollToRow,s=this.state.instanceProps;s.columnSizeAndPositionManager.resetCell(n),s.rowSizeAndPositionManager.resetCell(o),this._recomputeScrollLeftFlag=a>=0&&(1===this.state.scrollDirectionHorizontal?n<=a:n>=a),this._recomputeScrollTopFlag=l>=0&&(1===this.state.scrollDirectionVertical?o<=l:o>=l),this._styleCache={},this._cellCache={},this.forceUpdate()}},{key:"scrollToCell",value:function(e){var t=e.columnIndex,n=e.rowIndex,r=this.props.columnCount,o=this.props;r>1&&void 0!==t&&this._updateScrollLeftForScrollToColumn(V({},o,{scrollToColumn:t})),void 0!==n&&this._updateScrollTopForScrollToRow(V({},o,{scrollToRow:n}))}},{key:"componentDidMount",value:function(){var e=this.props,n=e.getScrollbarSize,r=e.height,o=e.scrollLeft,i=e.scrollToColumn,a=e.scrollTop,l=e.scrollToRow,s=e.width,c=this.state.instanceProps;if(this._initialScrollTop=0,this._initialScrollLeft=0,this._handleInvalidatedGridSize(),c.scrollbarSizeMeasured||this.setState((function(e){var t=V({},e,{needToResetStyleCache:!1});return t.instanceProps.scrollbarSize=n(),t.instanceProps.scrollbarSizeMeasured=!0,t})),"number"==typeof o&&o>=0||"number"==typeof a&&a>=0){var u=t._getScrollToPositionStateUpdate({prevState:this.state,scrollLeft:o,scrollTop:a});u&&(u.needToResetStyleCache=!1,this.setState(u))}this._scrollingContainer&&(this._scrollingContainer.scrollLeft!==this.state.scrollLeft&&(this._scrollingContainer.scrollLeft=this.state.scrollLeft),this._scrollingContainer.scrollTop!==this.state.scrollTop&&(this._scrollingContainer.scrollTop=this.state.scrollTop));var d=r>0&&s>0;i>=0&&d&&this._updateScrollLeftForScrollToColumn(),l>=0&&d&&this._updateScrollTopForScrollToRow(),this._invokeOnGridRenderedHelper(),this._invokeOnScrollMemoizer({scrollLeft:o||0,scrollTop:a||0,totalColumnsWidth:c.columnSizeAndPositionManager.getTotalSize(),totalRowsHeight:c.rowSizeAndPositionManager.getTotalSize()}),this._maybeCallOnScrollbarPresenceChange()}},{key:"componentDidUpdate",value:function(e,t){var n=this,r=this.props,o=r.autoHeight,i=r.autoWidth,a=r.columnCount,l=r.height,s=r.rowCount,c=r.scrollToAlignment,u=r.scrollToColumn,d=r.scrollToRow,f=r.width,h=this.state,p=h.scrollLeft,g=h.scrollPositionChangeReason,v=h.scrollTop,m=h.instanceProps;this._handleInvalidatedGridSize();var y=a>0&&0===e.columnCount||s>0&&0===e.rowCount;"requested"===g&&(!i&&p>=0&&(p!==this._scrollingContainer.scrollLeft||y)&&(this._scrollingContainer.scrollLeft=p),!o&&v>=0&&(v!==this._scrollingContainer.scrollTop||y)&&(this._scrollingContainer.scrollTop=v));var _=(0===e.width||0===e.height)&&l>0&&f>0;if(this._recomputeScrollLeftFlag?(this._recomputeScrollLeftFlag=!1,this._updateScrollLeftForScrollToColumn(this.props)):R({cellSizeAndPositionManager:m.columnSizeAndPositionManager,previousCellsCount:e.columnCount,previousCellSize:e.columnWidth,previousScrollToAlignment:e.scrollToAlignment,previousScrollToIndex:e.scrollToColumn,previousSize:e.width,scrollOffset:p,scrollToAlignment:c,scrollToIndex:u,size:f,sizeJustIncreasedFromZero:_,updateScrollIndexCallback:function(){return n._updateScrollLeftForScrollToColumn(n.props)}}),this._recomputeScrollTopFlag?(this._recomputeScrollTopFlag=!1,this._updateScrollTopForScrollToRow(this.props)):R({cellSizeAndPositionManager:m.rowSizeAndPositionManager,previousCellsCount:e.rowCount,previousCellSize:e.rowHeight,previousScrollToAlignment:e.scrollToAlignment,previousScrollToIndex:e.scrollToRow,previousSize:e.height,scrollOffset:v,scrollToAlignment:c,scrollToIndex:d,size:l,sizeJustIncreasedFromZero:_,updateScrollIndexCallback:function(){return n._updateScrollTopForScrollToRow(n.props)}}),this._invokeOnGridRenderedHelper(),p!==t.scrollLeft||v!==t.scrollTop){var b=m.rowSizeAndPositionManager.getTotalSize(),w=m.columnSizeAndPositionManager.getTotalSize();this._invokeOnScrollMemoizer({scrollLeft:p,scrollTop:v,totalColumnsWidth:w,totalRowsHeight:b})}this._maybeCallOnScrollbarPresenceChange()}},{key:"componentWillUnmount",value:function(){this._disablePointerEventsTimeoutId&&W(this._disablePointerEventsTimeoutId)}},{key:"render",value:function(){var e=this.props,t=e.autoContainerWidth,n=e.autoHeight,r=e.autoWidth,o=e.className,i=e.containerProps,a=e.containerRole,l=e.containerStyle,s=e.height,c=e.id,u=e.noContentRenderer,d=e.role,f=e.style,h=e.tabIndex,p=e.width,g=this.state,v=g.instanceProps,y=g.needToResetStyleCache,_=this._isScrolling(),w={boxSizing:"border-box",direction:"ltr",height:n?"auto":s,position:"relative",width:r?"auto":p,WebkitOverflowScrolling:"touch",willChange:"transform"};y&&(this._styleCache={}),this.state.isScrolling||this._resetStyleCache(),this._calculateChildrenToRender(this.props,this.state);var x=v.columnSizeAndPositionManager.getTotalSize(),C=v.rowSizeAndPositionManager.getTotalSize(),O=C>s?v.scrollbarSize:0,T=x>p?v.scrollbarSize:0;T===this._horizontalScrollBarSize&&O===this._verticalScrollBarSize||(this._horizontalScrollBarSize=T,this._verticalScrollBarSize=O,this._scrollbarPresenceChanged=!0),w.overflowX=x+O<=p?"hidden":"auto",w.overflowY=C+T<=s?"hidden":"auto";var I=this._childrenToDisplay,D=0===I.length&&s>0&&p>0;return m.createElement("div",b()({ref:this._setScrollingContainerRef},i,{"aria-label":this.props["aria-label"],"aria-readonly":this.props["aria-readonly"],className:S("ReactVirtualized__Grid",o),id:c,onScroll:this._onScroll,role:d,style:V({},w,{},f),tabIndex:h}),I.length>0&&m.createElement("div",{className:"ReactVirtualized__Grid__innerScrollContainer",role:a,style:V({width:t?"auto":x,height:C,maxWidth:x,maxHeight:C,overflow:"hidden",pointerEvents:_?"none":"",position:"relative"},l)},I),D&&u())}},{key:"_calculateChildrenToRender",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.props,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.state,n=e.cellRenderer,r=e.cellRangeRenderer,o=e.columnCount,i=e.deferredMeasurementCache,a=e.height,l=e.overscanColumnCount,s=e.overscanIndicesGetter,c=e.overscanRowCount,u=e.rowCount,d=e.width,f=e.isScrollingOptOut,h=t.scrollDirectionHorizontal,p=t.scrollDirectionVertical,g=t.instanceProps,v=this._initialScrollTop>0?this._initialScrollTop:t.scrollTop,m=this._initialScrollLeft>0?this._initialScrollLeft:t.scrollLeft,y=this._isScrolling(e,t);if(this._childrenToDisplay=[],a>0&&d>0){var _=g.columnSizeAndPositionManager.getVisibleCellRange({containerSize:d,offset:m}),b=g.rowSizeAndPositionManager.getVisibleCellRange({containerSize:a,offset:v}),w=g.columnSizeAndPositionManager.getOffsetAdjustment({containerSize:d,offset:m}),S=g.rowSizeAndPositionManager.getOffsetAdjustment({containerSize:a,offset:v});this._renderedColumnStartIndex=_.start,this._renderedColumnStopIndex=_.stop,this._renderedRowStartIndex=b.start,this._renderedRowStopIndex=b.stop;var x=s({direction:"horizontal",cellCount:o,overscanCellsCount:l,scrollDirection:h,startIndex:"number"==typeof _.start?_.start:0,stopIndex:"number"==typeof _.stop?_.stop:-1}),C=s({direction:"vertical",cellCount:u,overscanCellsCount:c,scrollDirection:p,startIndex:"number"==typeof b.start?b.start:0,stopIndex:"number"==typeof b.stop?b.stop:-1}),O=x.overscanStartIndex,T=x.overscanStopIndex,I=C.overscanStartIndex,D=C.overscanStopIndex;if(i){if(!i.hasFixedHeight())for(var k=I;k<=D;k++)if(!i.has(k,0)){O=0,T=o-1;break}if(!i.hasFixedWidth())for(var R=O;R<=T;R++)if(!i.has(0,R)){I=0,D=u-1;break}}this._childrenToDisplay=r({cellCache:this._cellCache,cellRenderer:n,columnSizeAndPositionManager:g.columnSizeAndPositionManager,columnStartIndex:O,columnStopIndex:T,deferredMeasurementCache:i,horizontalOffsetAdjustment:w,isScrolling:y,isScrollingOptOut:f,parent:this,rowSizeAndPositionManager:g.rowSizeAndPositionManager,rowStartIndex:I,rowStopIndex:D,scrollLeft:m,scrollTop:v,styleCache:this._styleCache,verticalOffsetAdjustment:S,visibleColumnIndices:_,visibleRowIndices:b}),this._columnStartIndex=O,this._columnStopIndex=T,this._rowStartIndex=I,this._rowStopIndex=D}}},{key:"_debounceScrollEnded",value:function(){var e=this.props.scrollingResetTimeInterval;this._disablePointerEventsTimeoutId&&W(this._disablePointerEventsTimeoutId),this._disablePointerEventsTimeoutId=U(this._debounceScrollEndedCallback,e)}},{key:"_handleInvalidatedGridSize",value:function(){if("number"==typeof this._deferredInvalidateColumnIndex&&"number"==typeof this._deferredInvalidateRowIndex){var e=this._deferredInvalidateColumnIndex,t=this._deferredInvalidateRowIndex;this._deferredInvalidateColumnIndex=null,this._deferredInvalidateRowIndex=null,this.recomputeGridSize({columnIndex:e,rowIndex:t})}}},{key:"_invokeOnScrollMemoizer",value:function(e){var t=this,n=e.scrollLeft,r=e.scrollTop,o=e.totalColumnsWidth,i=e.totalRowsHeight;this._onScrollMemoizer({callback:function(e){var n=e.scrollLeft,r=e.scrollTop,a=t.props,l=a.height;(0,a.onScroll)({clientHeight:l,clientWidth:a.width,scrollHeight:i,scrollLeft:n,scrollTop:r,scrollWidth:o})},indices:{scrollLeft:n,scrollTop:r}})}},{key:"_isScrolling",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.props,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.state;return Object.hasOwnProperty.call(e,"isScrolling")?Boolean(e.isScrolling):Boolean(t.isScrolling)}},{key:"_maybeCallOnScrollbarPresenceChange",value:function(){if(this._scrollbarPresenceChanged){var e=this.props.onScrollbarPresenceChange;this._scrollbarPresenceChanged=!1,e({horizontal:this._horizontalScrollBarSize>0,size:this.state.instanceProps.scrollbarSize,vertical:this._verticalScrollBarSize>0})}}},{key:"scrollToPosition",value:function(e){var n=e.scrollLeft,r=e.scrollTop,o=t._getScrollToPositionStateUpdate({prevState:this.state,scrollLeft:n,scrollTop:r});o&&(o.needToResetStyleCache=!1,this.setState(o))}},{key:"_getCalculatedScrollLeft",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.props,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.state;return t._getCalculatedScrollLeft(e,n)}},{key:"_updateScrollLeftForScrollToColumn",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.props,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.state,r=t._getScrollLeftForScrollToColumnStateUpdate(e,n);r&&(r.needToResetStyleCache=!1,this.setState(r))}},{key:"_getCalculatedScrollTop",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.props,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.state;return t._getCalculatedScrollTop(e,n)}},{key:"_resetStyleCache",value:function(){var e=this._styleCache,t=this._cellCache,n=this.props.isScrollingOptOut;this._cellCache={},this._styleCache={};for(var r=this._rowStartIndex;r<=this._rowStopIndex;r++)for(var o=this._columnStartIndex;o<=this._columnStopIndex;o++){var i="".concat(r,"-").concat(o);this._styleCache[i]=e[i],n&&(this._cellCache[i]=t[i])}}},{key:"_updateScrollTopForScrollToRow",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.props,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.state,r=t._getScrollTopForScrollToRowStateUpdate(e,n);r&&(r.needToResetStyleCache=!1,this.setState(r))}}],[{key:"getDerivedStateFromProps",value:function(e,n){var r={};0===e.columnCount&&0!==n.scrollLeft||0===e.rowCount&&0!==n.scrollTop?(r.scrollLeft=0,r.scrollTop=0):(e.scrollLeft!==n.scrollLeft&&e.scrollToColumn<0||e.scrollTop!==n.scrollTop&&e.scrollToRow<0)&&Object.assign(r,t._getScrollToPositionStateUpdate({prevState:n,scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}));var o,i,a=n.instanceProps;return r.needToResetStyleCache=!1,e.columnWidth===a.prevColumnWidth&&e.rowHeight===a.prevRowHeight||(r.needToResetStyleCache=!0),a.columnSizeAndPositionManager.configure({cellCount:e.columnCount,estimatedCellSize:t._getEstimatedColumnSize(e),cellSizeGetter:t._wrapSizeGetter(e.columnWidth)}),a.rowSizeAndPositionManager.configure({cellCount:e.rowCount,estimatedCellSize:t._getEstimatedRowSize(e),cellSizeGetter:t._wrapSizeGetter(e.rowHeight)}),0!==a.prevColumnCount&&0!==a.prevRowCount||(a.prevColumnCount=0,a.prevRowCount=0),e.autoHeight&&!1===e.isScrolling&&!0===a.prevIsScrolling&&Object.assign(r,{isScrolling:!1}),x({cellCount:a.prevColumnCount,cellSize:"number"==typeof a.prevColumnWidth?a.prevColumnWidth:null,computeMetadataCallback:function(){return a.columnSizeAndPositionManager.resetCell(0)},computeMetadataCallbackProps:e,nextCellsCount:e.columnCount,nextCellSize:"number"==typeof e.columnWidth?e.columnWidth:null,nextScrollToIndex:e.scrollToColumn,scrollToIndex:a.prevScrollToColumn,updateScrollOffsetForScrollToIndex:function(){o=t._getScrollLeftForScrollToColumnStateUpdate(e,n)}}),x({cellCount:a.prevRowCount,cellSize:"number"==typeof a.prevRowHeight?a.prevRowHeight:null,computeMetadataCallback:function(){return a.rowSizeAndPositionManager.resetCell(0)},computeMetadataCallbackProps:e,nextCellsCount:e.rowCount,nextCellSize:"number"==typeof e.rowHeight?e.rowHeight:null,nextScrollToIndex:e.scrollToRow,scrollToIndex:a.prevScrollToRow,updateScrollOffsetForScrollToIndex:function(){i=t._getScrollTopForScrollToRowStateUpdate(e,n)}}),a.prevColumnCount=e.columnCount,a.prevColumnWidth=e.columnWidth,a.prevIsScrolling=!0===e.isScrolling,a.prevRowCount=e.rowCount,a.prevRowHeight=e.rowHeight,a.prevScrollToColumn=e.scrollToColumn,a.prevScrollToRow=e.scrollToRow,a.scrollbarSize=e.getScrollbarSize(),void 0===a.scrollbarSize?(a.scrollbarSizeMeasured=!1,a.scrollbarSize=0):a.scrollbarSizeMeasured=!0,r.instanceProps=a,V({},r,{},o,{},i)}},{key:"_getEstimatedColumnSize",value:function(e){return"number"==typeof e.columnWidth?e.columnWidth:e.estimatedColumnSize}},{key:"_getEstimatedRowSize",value:function(e){return"number"==typeof e.rowHeight?e.rowHeight:e.estimatedRowSize}},{key:"_getScrollToPositionStateUpdate",value:function(e){var t=e.prevState,n=e.scrollLeft,r=e.scrollTop,o={scrollPositionChangeReason:"requested"};return"number"==typeof n&&n>=0&&(o.scrollDirectionHorizontal=n>t.scrollLeft?1:-1,o.scrollLeft=n),"number"==typeof r&&r>=0&&(o.scrollDirectionVertical=r>t.scrollTop?1:-1,o.scrollTop=r),"number"==typeof n&&n>=0&&n!==t.scrollLeft||"number"==typeof r&&r>=0&&r!==t.scrollTop?o:{}}},{key:"_wrapSizeGetter",value:function(e){return"function"==typeof e?e:function(){return e}}},{key:"_getCalculatedScrollLeft",value:function(e,t){var n=e.columnCount,r=e.height,o=e.scrollToAlignment,i=e.scrollToColumn,a=e.width,l=t.scrollLeft,s=t.instanceProps;if(n>0){var c=n-1,u=i<0?c:Math.min(c,i),d=s.rowSizeAndPositionManager.getTotalSize(),f=s.scrollbarSizeMeasured&&d>r?s.scrollbarSize:0;return s.columnSizeAndPositionManager.getUpdatedOffsetForIndex({align:o,containerSize:a-f,currentOffset:l,targetIndex:u})}return 0}},{key:"_getScrollLeftForScrollToColumnStateUpdate",value:function(e,n){var r=n.scrollLeft,o=t._getCalculatedScrollLeft(e,n);return"number"==typeof o&&o>=0&&r!==o?t._getScrollToPositionStateUpdate({prevState:n,scrollLeft:o,scrollTop:-1}):{}}},{key:"_getCalculatedScrollTop",value:function(e,t){var n=e.height,r=e.rowCount,o=e.scrollToAlignment,i=e.scrollToRow,a=e.width,l=t.scrollTop,s=t.instanceProps;if(r>0){var c=r-1,u=i<0?c:Math.min(c,i),d=s.columnSizeAndPositionManager.getTotalSize(),f=s.scrollbarSizeMeasured&&d>a?s.scrollbarSize:0;return s.rowSizeAndPositionManager.getUpdatedOffsetForIndex({align:o,containerSize:n-f,currentOffset:l,targetIndex:u})}return 0}},{key:"_getScrollTopForScrollToRowStateUpdate",value:function(e,n){var r=n.scrollTop,o=t._getCalculatedScrollTop(e,n);return"number"==typeof o&&o>=0&&r!==o?t._getScrollToPositionStateUpdate({prevState:n,scrollLeft:-1,scrollTop:o}):{}}}]),t}(m.PureComponent),v()(N,"propTypes",null),L);v()(K,"defaultProps",{"aria-label":"grid","aria-readonly":!0,autoContainerWidth:!1,autoHeight:!1,autoWidth:!1,cellRangeRenderer:P,containerRole:"rowgroup",containerStyle:{},estimatedColumnSize:100,estimatedRowSize:30,getScrollbarSize:z,noContentRenderer:function(){return null},onScroll:function(){},onScrollbarPresenceChange:function(){},onSectionRendered:function(){},overscanColumnCount:0,overscanIndicesGetter:k,overscanRowCount:10,role:"grid",scrollingResetTimeInterval:150,scrollToAlignment:"auto",scrollToColumn:-1,scrollToRow:-1,style:{},tabIndex:0,isScrollingOptOut:!1}),Object(y.polyfill)(K);var q,Y,Z=K;function X(e){var t=e.cellCount,n=e.overscanCellsCount,r=e.scrollDirection,o=e.startIndex,i=e.stopIndex;return n=Math.max(1,n),1===r?{overscanStartIndex:Math.max(0,o-1),overscanStopIndex:Math.min(t-1,i+n)}:{overscanStartIndex:Math.max(0,o-n),overscanStopIndex:Math.min(t-1,i+1)}}function J(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}var $=(Y=q=function(e){function t(){var e,n;o()(this,t);for(var r=arguments.length,i=new Array(r),a=0;a<r;a++)i[a]=arguments[a];return n=s()(this,(e=u()(t)).call.apply(e,[this].concat(i))),v()(f()(n),"state",{scrollToColumn:0,scrollToRow:0,instanceProps:{prevScrollToColumn:0,prevScrollToRow:0}}),v()(f()(n),"_columnStartIndex",0),v()(f()(n),"_columnStopIndex",0),v()(f()(n),"_rowStartIndex",0),v()(f()(n),"_rowStopIndex",0),v()(f()(n),"_onKeyDown",(function(e){var t=n.props,r=t.columnCount,o=t.disabled,i=t.mode,a=t.rowCount;if(!o){var l=n._getScrollState(),s=l.scrollToColumn,c=l.scrollToRow,u=n._getScrollState(),d=u.scrollToColumn,f=u.scrollToRow;switch(e.key){case"ArrowDown":f="cells"===i?Math.min(f+1,a-1):Math.min(n._rowStopIndex+1,a-1);break;case"ArrowLeft":d="cells"===i?Math.max(d-1,0):Math.max(n._columnStartIndex-1,0);break;case"ArrowRight":d="cells"===i?Math.min(d+1,r-1):Math.min(n._columnStopIndex+1,r-1);break;case"ArrowUp":f="cells"===i?Math.max(f-1,0):Math.max(n._rowStartIndex-1,0)}d===s&&f===c||(e.preventDefault(),n._updateScrollState({scrollToColumn:d,scrollToRow:f}))}})),v()(f()(n),"_onSectionRendered",(function(e){var t=e.columnStartIndex,r=e.columnStopIndex,o=e.rowStartIndex,i=e.rowStopIndex;n._columnStartIndex=t,n._columnStopIndex=r,n._rowStartIndex=o,n._rowStopIndex=i})),n}return p()(t,e),a()(t,[{key:"setScrollIndexes",value:function(e){var t=e.scrollToColumn,n=e.scrollToRow;this.setState({scrollToRow:n,scrollToColumn:t})}},{key:"render",value:function(){var e=this.props,t=e.className,n=e.children,r=this._getScrollState(),o=r.scrollToColumn,i=r.scrollToRow;return m.createElement("div",{className:t,onKeyDown:this._onKeyDown},n({onSectionRendered:this._onSectionRendered,scrollToColumn:o,scrollToRow:i}))}},{key:"_getScrollState",value:function(){return this.props.isControlled?this.props:this.state}},{key:"_updateScrollState",value:function(e){var t=e.scrollToColumn,n=e.scrollToRow,r=this.props,o=r.isControlled,i=r.onScrollToChange;"function"==typeof i&&i({scrollToColumn:t,scrollToRow:n}),o||this.setState({scrollToColumn:t,scrollToRow:n})}}],[{key:"getDerivedStateFromProps",value:function(e,t){return e.isControlled?{}:e.scrollToColumn!==t.instanceProps.prevScrollToColumn||e.scrollToRow!==t.instanceProps.prevScrollToRow?function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?J(n,!0).forEach((function(t){v()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):J(n).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}({},t,{scrollToColumn:e.scrollToColumn,scrollToRow:e.scrollToRow,instanceProps:{prevScrollToColumn:e.scrollToColumn,prevScrollToRow:e.scrollToRow}}):{}}}]),t}(m.PureComponent),v()(q,"propTypes",null),Y);v()($,"defaultProps",{disabled:!1,isControlled:!1,mode:"edges",scrollToColumn:0,scrollToRow:0}),Object(y.polyfill)($);var Q,ee,te=$,ne=n(61);function re(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function oe(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?re(n,!0).forEach((function(t){v()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):re(n).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var ie=(ee=Q=function(e){function t(){var e,n;o()(this,t);for(var r=arguments.length,i=new Array(r),a=0;a<r;a++)i[a]=arguments[a];return n=s()(this,(e=u()(t)).call.apply(e,[this].concat(i))),v()(f()(n),"state",{height:n.props.defaultHeight||0,width:n.props.defaultWidth||0}),v()(f()(n),"_parentNode",void 0),v()(f()(n),"_autoSizer",void 0),v()(f()(n),"_window",void 0),v()(f()(n),"_detectElementResize",void 0),v()(f()(n),"_onResize",(function(){var e=n.props,t=e.disableHeight,r=e.disableWidth,o=e.onResize;if(n._parentNode){var i=n._parentNode.offsetHeight||0,a=n._parentNode.offsetWidth||0,l=(n._window||window).getComputedStyle(n._parentNode)||{},s=parseInt(l.paddingLeft,10)||0,c=parseInt(l.paddingRight,10)||0,u=parseInt(l.paddingTop,10)||0,d=parseInt(l.paddingBottom,10)||0,f=i-u-d,h=a-s-c;(!t&&n.state.height!==f||!r&&n.state.width!==h)&&(n.setState({height:i-u-d,width:a-s-c}),o({height:i,width:a}))}})),v()(f()(n),"_setRef",(function(e){n._autoSizer=e})),n}return p()(t,e),a()(t,[{key:"componentDidMount",value:function(){var e=this.props.nonce;this._autoSizer&&this._autoSizer.parentNode&&this._autoSizer.parentNode.ownerDocument&&this._autoSizer.parentNode.ownerDocument.defaultView&&this._autoSizer.parentNode instanceof this._autoSizer.parentNode.ownerDocument.defaultView.HTMLElement&&(this._parentNode=this._autoSizer.parentNode,this._window=this._autoSizer.parentNode.ownerDocument.defaultView,this._detectElementResize=Object(ne.a)(e,this._window),this._detectElementResize.addResizeListener(this._parentNode,this._onResize),this._onResize())}},{key:"componentWillUnmount",value:function(){this._detectElementResize&&this._parentNode&&this._detectElementResize.removeResizeListener(this._parentNode,this._onResize)}},{key:"render",value:function(){var e=this.props,t=e.children,n=e.className,r=e.disableHeight,o=e.disableWidth,i=e.style,a=this.state,l=a.height,s=a.width,c={overflow:"visible"},u={};return r||(c.height=0,u.height=l),o||(c.width=0,u.width=s),m.createElement("div",{className:n,ref:this._setRef,style:oe({},c,{},i)},t(u))}}]),t}(m.Component),v()(Q,"propTypes",null),ee);v()(ie,"defaultProps",{onResize:function(){},disableHeight:!1,disableWidth:!1,style:{}});var ae,le,se=n(13),ce=(le=ae=function(e){function t(){var e,n;o()(this,t);for(var r=arguments.length,i=new Array(r),a=0;a<r;a++)i[a]=arguments[a];return n=s()(this,(e=u()(t)).call.apply(e,[this].concat(i))),v()(f()(n),"_child",void 0),v()(f()(n),"_measure",(function(){var e=n.props,t=e.cache,r=e.columnIndex,o=void 0===r?0:r,i=e.parent,a=e.rowIndex,l=void 0===a?n.props.index||0:a,s=n._getCellMeasurements(),c=s.height,u=s.width;c===t.getHeight(l,o)&&u===t.getWidth(l,o)||(t.set(l,o,u,c),i&&"function"==typeof i.recomputeGridSize&&i.recomputeGridSize({columnIndex:o,rowIndex:l}))})),v()(f()(n),"_registerChild",(function(e){!e||e instanceof Element||console.warn("CellMeasurer registerChild expects to be passed Element or null"),n._child=e,e&&n._maybeMeasureCell()})),n}return p()(t,e),a()(t,[{key:"componentDidMount",value:function(){this._maybeMeasureCell()}},{key:"componentDidUpdate",value:function(){this._maybeMeasureCell()}},{key:"render",value:function(){var e=this.props.children;return"function"==typeof e?e({measure:this._measure,registerChild:this._registerChild}):e}},{key:"_getCellMeasurements",value:function(){var e=this.props.cache,t=this._child||Object(se.findDOMNode)(this);if(t&&t.ownerDocument&&t.ownerDocument.defaultView&&t instanceof t.ownerDocument.defaultView.HTMLElement){var n=t.style.width,r=t.style.height;e.hasFixedWidth()||(t.style.width="auto"),e.hasFixedHeight()||(t.style.height="auto");var o=Math.ceil(t.offsetHeight),i=Math.ceil(t.offsetWidth);return n&&(t.style.width=n),r&&(t.style.height=r),{height:o,width:i}}return{height:0,width:0}}},{key:"_maybeMeasureCell",value:function(){var e=this.props,t=e.cache,n=e.columnIndex,r=void 0===n?0:n,o=e.parent,i=e.rowIndex,a=void 0===i?this.props.index||0:i;if(!t.has(a,r)){var l=this._getCellMeasurements(),s=l.height,c=l.width;t.set(a,r,c,s),o&&"function"==typeof o.invalidateCellSizeAfterRender&&o.invalidateCellSizeAfterRender({columnIndex:r,rowIndex:a})}}}]),t}(m.PureComponent),v()(ae,"propTypes",null),le);v()(ce,"__internalCellMeasurerFlag",!1);var ue=function(){function e(){var t=this,n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};o()(this,e),v()(this,"_cellHeightCache",{}),v()(this,"_cellWidthCache",{}),v()(this,"_columnWidthCache",{}),v()(this,"_rowHeightCache",{}),v()(this,"_defaultHeight",void 0),v()(this,"_defaultWidth",void 0),v()(this,"_minHeight",void 0),v()(this,"_minWidth",void 0),v()(this,"_keyMapper",void 0),v()(this,"_hasFixedHeight",void 0),v()(this,"_hasFixedWidth",void 0),v()(this,"_columnCount",0),v()(this,"_rowCount",0),v()(this,"columnWidth",(function(e){var n=e.index,r=t._keyMapper(0,n);return void 0!==t._columnWidthCache[r]?t._columnWidthCache[r]:t._defaultWidth})),v()(this,"rowHeight",(function(e){var n=e.index,r=t._keyMapper(n,0);return void 0!==t._rowHeightCache[r]?t._rowHeightCache[r]:t._defaultHeight}));var r=n.defaultHeight,i=n.defaultWidth,a=n.fixedHeight,l=n.fixedWidth,s=n.keyMapper,c=n.minHeight,u=n.minWidth;this._hasFixedHeight=!0===a,this._hasFixedWidth=!0===l,this._minHeight=c||0,this._minWidth=u||0,this._keyMapper=s||de,this._defaultHeight=Math.max(this._minHeight,"number"==typeof r?r:30),this._defaultWidth=Math.max(this._minWidth,"number"==typeof i?i:100)}return a()(e,[{key:"clear",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,n=this._keyMapper(e,t);delete this._cellHeightCache[n],delete this._cellWidthCache[n],this._updateCachedColumnAndRowSizes(e,t)}},{key:"clearAll",value:function(){this._cellHeightCache={},this._cellWidthCache={},this._columnWidthCache={},this._rowHeightCache={},this._rowCount=0,this._columnCount=0}},{key:"hasFixedHeight",value:function(){return this._hasFixedHeight}},{key:"hasFixedWidth",value:function(){return this._hasFixedWidth}},{key:"getHeight",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;if(this._hasFixedHeight)return this._defaultHeight;var n=this._keyMapper(e,t);return void 0!==this._cellHeightCache[n]?Math.max(this._minHeight,this._cellHeightCache[n]):this._defaultHeight}},{key:"getWidth",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;if(this._hasFixedWidth)return this._defaultWidth;var n=this._keyMapper(e,t);return void 0!==this._cellWidthCache[n]?Math.max(this._minWidth,this._cellWidthCache[n]):this._defaultWidth}},{key:"has",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,n=this._keyMapper(e,t);return void 0!==this._cellHeightCache[n]}},{key:"set",value:function(e,t,n,r){var o=this._keyMapper(e,t);t>=this._columnCount&&(this._columnCount=t+1),e>=this._rowCount&&(this._rowCount=e+1),this._cellHeightCache[o]=r,this._cellWidthCache[o]=n,this._updateCachedColumnAndRowSizes(e,t)}},{key:"_updateCachedColumnAndRowSizes",value:function(e,t){if(!this._hasFixedWidth){for(var n=0,r=0;r<this._rowCount;r++)n=Math.max(n,this.getWidth(r,t));var o=this._keyMapper(0,t);this._columnWidthCache[o]=n}if(!this._hasFixedHeight){for(var i=0,a=0;a<this._columnCount;a++)i=Math.max(i,this.getHeight(e,a));var l=this._keyMapper(e,0);this._rowHeightCache[l]=i}}},{key:"defaultHeight",get:function(){return this._defaultHeight}},{key:"defaultWidth",get:function(){return this._defaultWidth}}]),e}();function de(e,t){return"".concat(e,"-").concat(t)}function fe(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function he(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?fe(n,!0).forEach((function(t){v()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):fe(n).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var pe=function(e){function t(){var e,n;o()(this,t);for(var r=arguments.length,i=new Array(r),a=0;a<r;a++)i[a]=arguments[a];return n=s()(this,(e=u()(t)).call.apply(e,[this].concat(i))),v()(f()(n),"state",{isScrolling:!1,scrollLeft:0,scrollTop:0}),v()(f()(n),"_calculateSizeAndPositionDataOnNextUpdate",!1),v()(f()(n),"_onSectionRenderedMemoizer",D()),v()(f()(n),"_onScrollMemoizer",D(!1)),v()(f()(n),"_invokeOnSectionRenderedHelper",(function(){var e=n.props,t=e.cellLayoutManager,r=e.onSectionRendered;n._onSectionRenderedMemoizer({callback:r,indices:{indices:t.getLastRenderedIndices()}})})),v()(f()(n),"_setScrollingContainerRef",(function(e){n._scrollingContainer=e})),v()(f()(n),"_updateScrollPositionForScrollToCell",(function(){var e=n.props,t=e.cellLayoutManager,r=e.height,o=e.scrollToAlignment,i=e.scrollToCell,a=e.width,l=n.state,s=l.scrollLeft,c=l.scrollTop;if(i>=0){var u=t.getScrollPositionForCell({align:o,cellIndex:i,height:r,scrollLeft:s,scrollTop:c,width:a});u.scrollLeft===s&&u.scrollTop===c||n._setScrollPosition(u)}})),v()(f()(n),"_onScroll",(function(e){if(e.target===n._scrollingContainer){n._enablePointerEventsAfterDelay();var t=n.props,r=t.cellLayoutManager,o=t.height,i=t.isScrollingChange,a=t.width,l=n._scrollbarSize,s=r.getTotalSize(),c=s.height,u=s.width,d=Math.max(0,Math.min(u-a+l,e.target.scrollLeft)),f=Math.max(0,Math.min(c-o+l,e.target.scrollTop));if(n.state.scrollLeft!==d||n.state.scrollTop!==f){var h=e.cancelable?"observed":"requested";n.state.isScrolling||i(!0),n.setState({isScrolling:!0,scrollLeft:d,scrollPositionChangeReason:h,scrollTop:f})}n._invokeOnScrollMemoizer({scrollLeft:d,scrollTop:f,totalWidth:u,totalHeight:c})}})),n._scrollbarSize=z(),void 0===n._scrollbarSize?(n._scrollbarSizeMeasured=!1,n._scrollbarSize=0):n._scrollbarSizeMeasured=!0,n}return p()(t,e),a()(t,[{key:"recomputeCellSizesAndPositions",value:function(){this._calculateSizeAndPositionDataOnNextUpdate=!0,this.forceUpdate()}},{key:"componentDidMount",value:function(){var e=this.props,t=e.cellLayoutManager,n=e.scrollLeft,r=e.scrollToCell,o=e.scrollTop;this._scrollbarSizeMeasured||(this._scrollbarSize=z(),this._scrollbarSizeMeasured=!0,this.setState({})),r>=0?this._updateScrollPositionForScrollToCell():(n>=0||o>=0)&&this._setScrollPosition({scrollLeft:n,scrollTop:o}),this._invokeOnSectionRenderedHelper();var i=t.getTotalSize(),a=i.height,l=i.width;this._invokeOnScrollMemoizer({scrollLeft:n||0,scrollTop:o||0,totalHeight:a,totalWidth:l})}},{key:"componentDidUpdate",value:function(e,t){var n=this.props,r=n.height,o=n.scrollToAlignment,i=n.scrollToCell,a=n.width,l=this.state,s=l.scrollLeft,c=l.scrollPositionChangeReason,u=l.scrollTop;"requested"===c&&(s>=0&&s!==t.scrollLeft&&s!==this._scrollingContainer.scrollLeft&&(this._scrollingContainer.scrollLeft=s),u>=0&&u!==t.scrollTop&&u!==this._scrollingContainer.scrollTop&&(this._scrollingContainer.scrollTop=u)),r===e.height&&o===e.scrollToAlignment&&i===e.scrollToCell&&a===e.width||this._updateScrollPositionForScrollToCell(),this._invokeOnSectionRenderedHelper()}},{key:"componentWillUnmount",value:function(){this._disablePointerEventsTimeoutId&&clearTimeout(this._disablePointerEventsTimeoutId)}},{key:"render",value:function(){var e=this.props,t=e.autoHeight,n=e.cellCount,r=e.cellLayoutManager,o=e.className,i=e.height,a=e.horizontalOverscanSize,l=e.id,s=e.noContentRenderer,c=e.style,u=e.verticalOverscanSize,d=e.width,f=this.state,h=f.isScrolling,p=f.scrollLeft,g=f.scrollTop;(this._lastRenderedCellCount!==n||this._lastRenderedCellLayoutManager!==r||this._calculateSizeAndPositionDataOnNextUpdate)&&(this._lastRenderedCellCount=n,this._lastRenderedCellLayoutManager=r,this._calculateSizeAndPositionDataOnNextUpdate=!1,r.calculateSizeAndPositionData());var v=r.getTotalSize(),y=v.height,_=v.width,b=Math.max(0,p-a),w=Math.max(0,g-u),x=Math.min(_,p+d+a),C=Math.min(y,g+i+u),O=i>0&&d>0?r.cellRenderers({height:C-w,isScrolling:h,width:x-b,x:b,y:w}):[],T={boxSizing:"border-box",direction:"ltr",height:t?"auto":i,position:"relative",WebkitOverflowScrolling:"touch",width:d,willChange:"transform"},I=y>i?this._scrollbarSize:0,D=_>d?this._scrollbarSize:0;return T.overflowX=_+I<=d?"hidden":"auto",T.overflowY=y+D<=i?"hidden":"auto",m.createElement("div",{ref:this._setScrollingContainerRef,"aria-label":this.props["aria-label"],className:S("ReactVirtualized__Collection",o),id:l,onScroll:this._onScroll,role:"grid",style:he({},T,{},c),tabIndex:0},n>0&&m.createElement("div",{className:"ReactVirtualized__Collection__innerScrollContainer",style:{height:y,maxHeight:y,maxWidth:_,overflow:"hidden",pointerEvents:h?"none":"",width:_}},O),0===n&&s())}},{key:"_enablePointerEventsAfterDelay",value:function(){var e=this;this._disablePointerEventsTimeoutId&&clearTimeout(this._disablePointerEventsTimeoutId),this._disablePointerEventsTimeoutId=setTimeout((function(){(0,e.props.isScrollingChange)(!1),e._disablePointerEventsTimeoutId=null,e.setState({isScrolling:!1})}),150)}},{key:"_invokeOnScrollMemoizer",value:function(e){var t=this,n=e.scrollLeft,r=e.scrollTop,o=e.totalHeight,i=e.totalWidth;this._onScrollMemoizer({callback:function(e){var n=e.scrollLeft,r=e.scrollTop,a=t.props,l=a.height;(0,a.onScroll)({clientHeight:l,clientWidth:a.width,scrollHeight:o,scrollLeft:n,scrollTop:r,scrollWidth:i})},indices:{scrollLeft:n,scrollTop:r}})}},{key:"_setScrollPosition",value:function(e){var t=e.scrollLeft,n=e.scrollTop,r={scrollPositionChangeReason:"requested"};t>=0&&(r.scrollLeft=t),n>=0&&(r.scrollTop=n),(t>=0&&t!==this.state.scrollLeft||n>=0&&n!==this.state.scrollTop)&&this.setState(r)}}],[{key:"getDerivedStateFromProps",value:function(e,t){return 0!==e.cellCount||0===t.scrollLeft&&0===t.scrollTop?e.scrollLeft!==t.scrollLeft||e.scrollTop!==t.scrollTop?{scrollLeft:null!=e.scrollLeft?e.scrollLeft:t.scrollLeft,scrollTop:null!=e.scrollTop?e.scrollTop:t.scrollTop,scrollPositionChangeReason:"requested"}:null:{scrollLeft:0,scrollTop:0,scrollPositionChangeReason:"requested"}}}]),t}(m.PureComponent);v()(pe,"defaultProps",{"aria-label":"grid",horizontalOverscanSize:0,noContentRenderer:function(){return null},onScroll:function(){return null},onSectionRendered:function(){return null},scrollToAlignment:"auto",scrollToCell:-1,style:{},verticalOverscanSize:0}),pe.propTypes={},Object(y.polyfill)(pe);var ge=pe,ve=function(){function e(t){var n=t.height,r=t.width,i=t.x,a=t.y;o()(this,e),this.height=n,this.width=r,this.x=i,this.y=a,this._indexMap={},this._indices=[]}return a()(e,[{key:"addCellIndex",value:function(e){var t=e.index;this._indexMap[t]||(this._indexMap[t]=!0,this._indices.push(t))}},{key:"getCellIndices",value:function(){return this._indices}},{key:"toString",value:function(){return"".concat(this.x,",").concat(this.y," ").concat(this.width,"x").concat(this.height)}}]),e}(),me=function(){function e(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:100;o()(this,e),this._sectionSize=t,this._cellMetadata=[],this._sections={}}return a()(e,[{key:"getCellIndices",value:function(e){var t=e.height,n=e.width,r=e.x,o=e.y,i={};return this.getSections({height:t,width:n,x:r,y:o}).forEach((function(e){return e.getCellIndices().forEach((function(e){i[e]=e}))})),Object.keys(i).map((function(e){return i[e]}))}},{key:"getCellMetadata",value:function(e){var t=e.index;return this._cellMetadata[t]}},{key:"getSections",value:function(e){for(var t=e.height,n=e.width,r=e.x,o=e.y,i=Math.floor(r/this._sectionSize),a=Math.floor((r+n-1)/this._sectionSize),l=Math.floor(o/this._sectionSize),s=Math.floor((o+t-1)/this._sectionSize),c=[],u=i;u<=a;u++)for(var d=l;d<=s;d++){var f="".concat(u,".").concat(d);this._sections[f]||(this._sections[f]=new ve({height:this._sectionSize,width:this._sectionSize,x:u*this._sectionSize,y:d*this._sectionSize})),c.push(this._sections[f])}return c}},{key:"getTotalSectionCount",value:function(){return Object.keys(this._sections).length}},{key:"toString",value:function(){var e=this;return Object.keys(this._sections).map((function(t){return e._sections[t].toString()}))}},{key:"registerCell",value:function(e){var t=e.cellMetadatum,n=e.index;this._cellMetadata[n]=t,this.getSections(t).forEach((function(e){return e.addCellIndex({index:n})}))}}]),e}();function ye(e){var t=e.align,n=void 0===t?"auto":t,r=e.cellOffset,o=e.cellSize,i=e.containerSize,a=e.currentOffset,l=r,s=l-i+o;switch(n){case"start":return l;case"end":return s;case"center":return l-(i-o)/2;default:return Math.max(s,Math.min(l,a))}}var _e=function(e){function t(e,n){var r;return o()(this,t),(r=s()(this,u()(t).call(this,e,n)))._cellMetadata=[],r._lastRenderedCellIndices=[],r._cellCache=[],r._isScrollingChange=r._isScrollingChange.bind(f()(r)),r._setCollectionViewRef=r._setCollectionViewRef.bind(f()(r)),r}return p()(t,e),a()(t,[{key:"forceUpdate",value:function(){void 0!==this._collectionView&&this._collectionView.forceUpdate()}},{key:"recomputeCellSizesAndPositions",value:function(){this._cellCache=[],this._collectionView.recomputeCellSizesAndPositions()}},{key:"render",value:function(){var e=b()({},this.props);return m.createElement(ge,b()({cellLayoutManager:this,isScrollingChange:this._isScrollingChange,ref:this._setCollectionViewRef},e))}},{key:"calculateSizeAndPositionData",value:function(){var e=this.props,t=function(e){for(var t=e.cellCount,n=e.cellSizeAndPositionGetter,r=[],o=new me(e.sectionSize),i=0,a=0,l=0;l<t;l++){var s=n({index:l});if(null==s.height||isNaN(s.height)||null==s.width||isNaN(s.width)||null==s.x||isNaN(s.x)||null==s.y||isNaN(s.y))throw Error("Invalid metadata returned for cell ".concat(l,":\n        x:").concat(s.x,", y:").concat(s.y,", width:").concat(s.width,", height:").concat(s.height));i=Math.max(i,s.y+s.height),a=Math.max(a,s.x+s.width),r[l]=s,o.registerCell({cellMetadatum:s,index:l})}return{cellMetadata:r,height:i,sectionManager:o,width:a}}({cellCount:e.cellCount,cellSizeAndPositionGetter:e.cellSizeAndPositionGetter,sectionSize:e.sectionSize});this._cellMetadata=t.cellMetadata,this._sectionManager=t.sectionManager,this._height=t.height,this._width=t.width}},{key:"getLastRenderedIndices",value:function(){return this._lastRenderedCellIndices}},{key:"getScrollPositionForCell",value:function(e){var t=e.align,n=e.cellIndex,r=e.height,o=e.scrollLeft,i=e.scrollTop,a=e.width,l=this.props.cellCount;if(n>=0&&n<l){var s=this._cellMetadata[n];o=ye({align:t,cellOffset:s.x,cellSize:s.width,containerSize:a,currentOffset:o,targetIndex:n}),i=ye({align:t,cellOffset:s.y,cellSize:s.height,containerSize:r,currentOffset:i,targetIndex:n})}return{scrollLeft:o,scrollTop:i}}},{key:"getTotalSize",value:function(){return{height:this._height,width:this._width}}},{key:"cellRenderers",value:function(e){var t=this,n=e.height,r=e.isScrolling,o=e.width,i=e.x,a=e.y,l=this.props,s=l.cellGroupRenderer,c=l.cellRenderer;return this._lastRenderedCellIndices=this._sectionManager.getCellIndices({height:n,width:o,x:i,y:a}),s({cellCache:this._cellCache,cellRenderer:c,cellSizeAndPositionGetter:function(e){var n=e.index;return t._sectionManager.getCellMetadata({index:n})},indices:this._lastRenderedCellIndices,isScrolling:r})}},{key:"_isScrollingChange",value:function(e){e||(this._cellCache=[])}},{key:"_setCollectionViewRef",value:function(e){this._collectionView=e}}]),t}(m.PureComponent);v()(_e,"defaultProps",{"aria-label":"grid",cellGroupRenderer:function(e){var t=e.cellCache,n=e.cellRenderer,r=e.cellSizeAndPositionGetter,o=e.indices,i=e.isScrolling;return o.map((function(e){var o=r({index:e}),a={index:e,isScrolling:i,key:e,style:{height:o.height,left:o.x,position:"absolute",top:o.y,width:o.width}};return i?(e in t||(t[e]=n(a)),t[e]):n(a)})).filter((function(e){return!!e}))}}),_e.propTypes={};var be=function(e){function t(e,n){var r;return o()(this,t),(r=s()(this,u()(t).call(this,e,n)))._registerChild=r._registerChild.bind(f()(r)),r}return p()(t,e),a()(t,[{key:"componentDidUpdate",value:function(e){var t=this.props,n=t.columnMaxWidth,r=t.columnMinWidth,o=t.columnCount,i=t.width;n===e.columnMaxWidth&&r===e.columnMinWidth&&o===e.columnCount&&i===e.width||this._registeredChild&&this._registeredChild.recomputeGridSize()}},{key:"render",value:function(){var e=this.props,t=e.children,n=e.columnMaxWidth,r=e.columnMinWidth,o=e.columnCount,i=e.width,a=r||1,l=n?Math.min(n,i):i,s=i/o;return s=Math.max(a,s),s=Math.min(l,s),s=Math.floor(s),t({adjustedWidth:Math.min(i,s*o),columnWidth:s,getColumnWidth:function(){return s},registerChild:this._registerChild})}},{key:"_registerChild",value:function(e){if(e&&"function"!=typeof e.recomputeGridSize)throw Error("Unexpected child type registered; only Grid/MultiGrid children are supported.");this._registeredChild=e,this._registeredChild&&this._registeredChild.recomputeGridSize()}}]),t}(m.PureComponent);be.propTypes={};var we=n(18),Se=n.n(we),xe=function(e){function t(e,n){var r;return o()(this,t),(r=s()(this,u()(t).call(this,e,n)))._loadMoreRowsMemoizer=D(),r._onRowsRendered=r._onRowsRendered.bind(f()(r)),r._registerChild=r._registerChild.bind(f()(r)),r}return p()(t,e),a()(t,[{key:"resetLoadMoreRowsCache",value:function(e){this._loadMoreRowsMemoizer=D(),e&&this._doStuff(this._lastRenderedStartIndex,this._lastRenderedStopIndex)}},{key:"render",value:function(){return(0,this.props.children)({onRowsRendered:this._onRowsRendered,registerChild:this._registerChild})}},{key:"_loadUnloadedRanges",value:function(e){var t=this,n=this.props.loadMoreRows;e.forEach((function(e){var r=n(e);r&&r.then((function(){var n;(n={lastRenderedStartIndex:t._lastRenderedStartIndex,lastRenderedStopIndex:t._lastRenderedStopIndex,startIndex:e.startIndex,stopIndex:e.stopIndex}).startIndex>n.lastRenderedStopIndex||n.stopIndex<n.lastRenderedStartIndex||t._registeredChild&&function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,n="function"==typeof e.recomputeGridSize?e.recomputeGridSize:e.recomputeRowHeights;n?n.call(e,t):e.forceUpdate()}(t._registeredChild,t._lastRenderedStartIndex)}))}))}},{key:"_onRowsRendered",value:function(e){var t=e.startIndex,n=e.stopIndex;this._lastRenderedStartIndex=t,this._lastRenderedStopIndex=n,this._doStuff(t,n)}},{key:"_doStuff",value:function(e,t){var n,r=this,o=this.props,i=o.isRowLoaded,a=o.minimumBatchSize,l=o.rowCount,s=o.threshold,c=function(e){for(var t=e.isRowLoaded,n=e.minimumBatchSize,r=e.rowCount,o=e.stopIndex,i=[],a=null,l=null,s=e.startIndex;s<=o;s++)t({index:s})?null!==l&&(i.push({startIndex:a,stopIndex:l}),a=l=null):(l=s,null===a&&(a=s));if(null!==l){for(var c=Math.min(Math.max(l,a+n-1),r-1),u=l+1;u<=c&&!t({index:u});u++)l=u;i.push({startIndex:a,stopIndex:l})}if(i.length)for(var d=i[0];d.stopIndex-d.startIndex+1<n&&d.startIndex>0;){var f=d.startIndex-1;if(t({index:f}))break;d.startIndex=f}return i}({isRowLoaded:i,minimumBatchSize:a,rowCount:l,startIndex:Math.max(0,e-s),stopIndex:Math.min(l-1,t+s)}),u=(n=[]).concat.apply(n,Se()(c.map((function(e){return[e.startIndex,e.stopIndex]}))));this._loadMoreRowsMemoizer({callback:function(){r._loadUnloadedRanges(c)},indices:{squashedUnloadedRanges:u}})}},{key:"_registerChild",value:function(e){this._registeredChild=e}}]),t}(m.PureComponent);v()(xe,"defaultProps",{minimumBatchSize:10,rowCount:0,threshold:15}),xe.propTypes={};var Ce,Oe,Te=(Oe=Ce=function(e){function t(){var e,n;o()(this,t);for(var r=arguments.length,i=new Array(r),a=0;a<r;a++)i[a]=arguments[a];return n=s()(this,(e=u()(t)).call.apply(e,[this].concat(i))),v()(f()(n),"Grid",void 0),v()(f()(n),"_cellRenderer",(function(e){var t=e.parent,r=e.rowIndex,o=e.style,i=e.isScrolling,a=e.isVisible,l=e.key,s=n.props.rowRenderer,c=Object.getOwnPropertyDescriptor(o,"width");return c&&c.writable&&(o.width="100%"),s({index:r,style:o,isScrolling:i,isVisible:a,key:l,parent:t})})),v()(f()(n),"_setRef",(function(e){n.Grid=e})),v()(f()(n),"_onScroll",(function(e){var t=e.clientHeight,r=e.scrollHeight,o=e.scrollTop;(0,n.props.onScroll)({clientHeight:t,scrollHeight:r,scrollTop:o})})),v()(f()(n),"_onSectionRendered",(function(e){var t=e.rowOverscanStartIndex,r=e.rowOverscanStopIndex,o=e.rowStartIndex,i=e.rowStopIndex;(0,n.props.onRowsRendered)({overscanStartIndex:t,overscanStopIndex:r,startIndex:o,stopIndex:i})})),n}return p()(t,e),a()(t,[{key:"forceUpdateGrid",value:function(){this.Grid&&this.Grid.forceUpdate()}},{key:"getOffsetForRow",value:function(e){var t=e.alignment,n=e.index;return this.Grid?this.Grid.getOffsetForCell({alignment:t,rowIndex:n,columnIndex:0}).scrollTop:0}},{key:"invalidateCellSizeAfterRender",value:function(e){var t=e.columnIndex,n=e.rowIndex;this.Grid&&this.Grid.invalidateCellSizeAfterRender({rowIndex:n,columnIndex:t})}},{key:"measureAllRows",value:function(){this.Grid&&this.Grid.measureAllCells()}},{key:"recomputeGridSize",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.columnIndex,n=void 0===t?0:t,r=e.rowIndex,o=void 0===r?0:r;this.Grid&&this.Grid.recomputeGridSize({rowIndex:o,columnIndex:n})}},{key:"recomputeRowHeights",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0;this.Grid&&this.Grid.recomputeGridSize({rowIndex:e,columnIndex:0})}},{key:"scrollToPosition",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0;this.Grid&&this.Grid.scrollToPosition({scrollTop:e})}},{key:"scrollToRow",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0;this.Grid&&this.Grid.scrollToCell({columnIndex:0,rowIndex:e})}},{key:"render",value:function(){var e=this.props,t=e.className,n=e.noRowsRenderer,r=e.scrollToIndex,o=e.width,i=S("ReactVirtualized__List",t);return m.createElement(Z,b()({},this.props,{autoContainerWidth:!0,cellRenderer:this._cellRenderer,className:i,columnWidth:o,columnCount:1,noContentRenderer:n,onScroll:this._onScroll,onSectionRendered:this._onSectionRendered,ref:this._setRef,scrollToRow:r}))}}]),t}(m.PureComponent),v()(Ce,"propTypes",null),Oe);v()(Te,"defaultProps",{autoHeight:!1,estimatedRowSize:30,onScroll:function(){},noRowsRenderer:function(){return null},onRowsRendered:function(){},overscanIndicesGetter:X,overscanRowCount:10,scrollToAlignment:"auto",scrollToIndex:-1,style:{}});var Ie=n(24),De=n.n(Ie),ke=function(e,t,n,r,o){return"function"==typeof n?function(e,t,n,r,o){for(var i=n+1;t<=n;){var a=t+n>>>1;o(e[a],r)>=0?(i=a,n=a-1):t=a+1}return i}(e,void 0===r?0:0|r,void 0===o?e.length-1:0|o,t,n):function(e,t,n,r){for(var o=n+1;t<=n;){var i=t+n>>>1;e[i]>=r?(o=i,n=i-1):t=i+1}return o}(e,void 0===n?0:0|n,void 0===r?e.length-1:0|r,t)};function Re(e,t,n,r,o){this.mid=e,this.left=t,this.right=n,this.leftPoints=r,this.rightPoints=o,this.count=(t?t.count:0)+(n?n.count:0)+r.length}var Pe=Re.prototype;function Ee(e,t){e.mid=t.mid,e.left=t.left,e.right=t.right,e.leftPoints=t.leftPoints,e.rightPoints=t.rightPoints,e.count=t.count}function Me(e,t){var n=We(t);e.mid=n.mid,e.left=n.left,e.right=n.right,e.leftPoints=n.leftPoints,e.rightPoints=n.rightPoints,e.count=n.count}function je(e,t){var n=e.intervals([]);n.push(t),Me(e,n)}function ze(e,t){var n=e.intervals([]),r=n.indexOf(t);return r<0?0:(n.splice(r,1),Me(e,n),1)}function Ne(e,t,n){for(var r=0;r<e.length&&e[r][0]<=t;++r){var o=n(e[r]);if(o)return o}}function Le(e,t,n){for(var r=e.length-1;r>=0&&e[r][1]>=t;--r){var o=n(e[r]);if(o)return o}}function Ae(e,t){for(var n=0;n<e.length;++n){var r=t(e[n]);if(r)return r}}function He(e,t){return e-t}function Ge(e,t){return e[0]-t[0]||e[1]-t[1]}function Fe(e,t){return e[1]-t[1]||e[0]-t[0]}function We(e){if(0===e.length)return null;for(var t=[],n=0;n<e.length;++n)t.push(e[n][0],e[n][1]);t.sort(He);var r=t[t.length>>1],o=[],i=[],a=[];for(n=0;n<e.length;++n){var l=e[n];l[1]<r?o.push(l):r<l[0]?i.push(l):a.push(l)}var s=a,c=a.slice();return s.sort(Ge),c.sort(Fe),new Re(r,We(o),We(i),s,c)}function Ue(e){this.root=e}Pe.intervals=function(e){return e.push.apply(e,this.leftPoints),this.left&&this.left.intervals(e),this.right&&this.right.intervals(e),e},Pe.insert=function(e){var t=this.count-this.leftPoints.length;if(this.count+=1,e[1]<this.mid)this.left?4*(this.left.count+1)>3*(t+1)?je(this,e):this.left.insert(e):this.left=We([e]);else if(e[0]>this.mid)this.right?4*(this.right.count+1)>3*(t+1)?je(this,e):this.right.insert(e):this.right=We([e]);else{var n=ke(this.leftPoints,e,Ge),r=ke(this.rightPoints,e,Fe);this.leftPoints.splice(n,0,e),this.rightPoints.splice(r,0,e)}},Pe.remove=function(e){var t=this.count-this.leftPoints;if(e[1]<this.mid)return this.left?4*(this.right?this.right.count:0)>3*(t-1)?ze(this,e):2===(i=this.left.remove(e))?(this.left=null,this.count-=1,1):(1===i&&(this.count-=1),i):0;if(e[0]>this.mid)return this.right?4*(this.left?this.left.count:0)>3*(t-1)?ze(this,e):2===(i=this.right.remove(e))?(this.right=null,this.count-=1,1):(1===i&&(this.count-=1),i):0;if(1===this.count)return this.leftPoints[0]===e?2:0;if(1===this.leftPoints.length&&this.leftPoints[0]===e){if(this.left&&this.right){for(var n=this,r=this.left;r.right;)n=r,r=r.right;if(n===this)r.right=this.right;else{var o=this.left,i=this.right;n.count-=r.count,n.right=r.left,r.left=o,r.right=i}Ee(this,r),this.count=(this.left?this.left.count:0)+(this.right?this.right.count:0)+this.leftPoints.length}else this.left?Ee(this,this.left):Ee(this,this.right);return 1}for(o=ke(this.leftPoints,e,Ge);o<this.leftPoints.length&&this.leftPoints[o][0]===e[0];++o)if(this.leftPoints[o]===e)for(this.count-=1,this.leftPoints.splice(o,1),i=ke(this.rightPoints,e,Fe);i<this.rightPoints.length&&this.rightPoints[i][1]===e[1];++i)if(this.rightPoints[i]===e)return this.rightPoints.splice(i,1),1;return 0},Pe.queryPoint=function(e,t){return e<this.mid?this.left&&(n=this.left.queryPoint(e,t))?n:Ne(this.leftPoints,e,t):e>this.mid?this.right&&(n=this.right.queryPoint(e,t))?n:Le(this.rightPoints,e,t):Ae(this.leftPoints,t);var n},Pe.queryInterval=function(e,t,n){var r;return e<this.mid&&this.left&&(r=this.left.queryInterval(e,t,n))||t>this.mid&&this.right&&(r=this.right.queryInterval(e,t,n))?r:t<this.mid?Ne(this.leftPoints,t,n):e>this.mid?Le(this.rightPoints,e,n):Ae(this.leftPoints,n)};var Be=Ue.prototype;Be.insert=function(e){this.root?this.root.insert(e):this.root=new Re(e[0],null,null,[e],[e])},Be.remove=function(e){if(this.root){var t=this.root.remove(e);return 2===t&&(this.root=null),0!==t}return!1},Be.queryPoint=function(e,t){if(this.root)return this.root.queryPoint(e,t)},Be.queryInterval=function(e,t,n){if(e<=t&&this.root)return this.root.queryInterval(e,t,n)},Object.defineProperty(Be,"count",{get:function(){return this.root?this.root.count:0}}),Object.defineProperty(Be,"intervals",{get:function(){return this.root?this.root.intervals([]):[]}});var Ve,Ke,qe=function(){function e(){o()(this,e),v()(this,"_columnSizeMap",{}),v()(this,"_intervalTree",new Ue(null)),v()(this,"_leftMap",{})}return a()(e,[{key:"estimateTotalHeight",value:function(e,t,n){var r=e-this.count;return this.tallestColumnSize+Math.ceil(r/t)*n}},{key:"range",value:function(e,t,n){var r=this;this._intervalTree.queryInterval(e,e+t,(function(e){var t=De()(e,3),o=t[0],i=(t[1],t[2]);return n(i,r._leftMap[i],o)}))}},{key:"setPosition",value:function(e,t,n,r){this._intervalTree.insert([n,n+r,e]),this._leftMap[e]=t;var o=this._columnSizeMap,i=o[t];o[t]=void 0===i?n+r:Math.max(i,n+r)}},{key:"count",get:function(){return this._intervalTree.count}},{key:"shortestColumnSize",get:function(){var e=this._columnSizeMap,t=0;for(var n in e){var r=e[n];t=0===t?r:Math.min(t,r)}return t}},{key:"tallestColumnSize",get:function(){var e=this._columnSizeMap,t=0;for(var n in e){var r=e[n];t=Math.max(t,r)}return t}}]),e}();function Ye(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function Ze(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Ye(n,!0).forEach((function(t){v()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Ye(n).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var Xe=(Ke=Ve=function(e){function t(){var e,n;o()(this,t);for(var r=arguments.length,i=new Array(r),a=0;a<r;a++)i[a]=arguments[a];return n=s()(this,(e=u()(t)).call.apply(e,[this].concat(i))),v()(f()(n),"state",{isScrolling:!1,scrollTop:0}),v()(f()(n),"_debounceResetIsScrollingId",void 0),v()(f()(n),"_invalidateOnUpdateStartIndex",null),v()(f()(n),"_invalidateOnUpdateStopIndex",null),v()(f()(n),"_positionCache",new qe),v()(f()(n),"_startIndex",null),v()(f()(n),"_startIndexMemoized",null),v()(f()(n),"_stopIndex",null),v()(f()(n),"_stopIndexMemoized",null),v()(f()(n),"_debounceResetIsScrollingCallback",(function(){n.setState({isScrolling:!1})})),v()(f()(n),"_setScrollingContainerRef",(function(e){n._scrollingContainer=e})),v()(f()(n),"_onScroll",(function(e){var t=n.props.height,r=e.currentTarget.scrollTop,o=Math.min(Math.max(0,n._getEstimatedTotalHeight()-t),r);r===o&&(n._debounceResetIsScrolling(),n.state.scrollTop!==o&&n.setState({isScrolling:!0,scrollTop:o}))})),n}return p()(t,e),a()(t,[{key:"clearCellPositions",value:function(){this._positionCache=new qe,this.forceUpdate()}},{key:"invalidateCellSizeAfterRender",value:function(e){var t=e.rowIndex;null===this._invalidateOnUpdateStartIndex?(this._invalidateOnUpdateStartIndex=t,this._invalidateOnUpdateStopIndex=t):(this._invalidateOnUpdateStartIndex=Math.min(this._invalidateOnUpdateStartIndex,t),this._invalidateOnUpdateStopIndex=Math.max(this._invalidateOnUpdateStopIndex,t))}},{key:"recomputeCellPositions",value:function(){var e=this._positionCache.count-1;this._positionCache=new qe,this._populatePositionCache(0,e),this.forceUpdate()}},{key:"componentDidMount",value:function(){this._checkInvalidateOnUpdate(),this._invokeOnScrollCallback(),this._invokeOnCellsRenderedCallback()}},{key:"componentDidUpdate",value:function(e,t){this._checkInvalidateOnUpdate(),this._invokeOnScrollCallback(),this._invokeOnCellsRenderedCallback(),this.props.scrollTop!==e.scrollTop&&this._debounceResetIsScrolling()}},{key:"componentWillUnmount",value:function(){this._debounceResetIsScrollingId&&W(this._debounceResetIsScrollingId)}},{key:"render",value:function(){var e,t=this,n=this.props,r=n.autoHeight,o=n.cellCount,i=n.cellMeasurerCache,a=n.cellRenderer,l=n.className,s=n.height,c=n.id,u=n.keyMapper,d=n.overscanByPixels,f=n.role,h=n.style,p=n.tabIndex,g=n.width,y=n.rowDirection,_=this.state,b=_.isScrolling,w=_.scrollTop,x=[],C=this._getEstimatedTotalHeight(),O=this._positionCache.shortestColumnSize,T=this._positionCache.count,I=0;if(this._positionCache.range(Math.max(0,w-d),s+2*d,(function(n,r,o){var l;void 0===e?(I=n,e=n):(I=Math.min(I,n),e=Math.max(e,n)),x.push(a({index:n,isScrolling:b,key:u(n),parent:t,style:(l={height:i.getHeight(n)},v()(l,"ltr"===y?"left":"right",r),v()(l,"position","absolute"),v()(l,"top",o),v()(l,"width",i.getWidth(n)),l)}))})),O<w+s+d&&T<o)for(var D=Math.min(o-T,Math.ceil((w+s+d-O)/i.defaultHeight*g/i.defaultWidth)),k=T;k<T+D;k++)e=k,x.push(a({index:k,isScrolling:b,key:u(k),parent:this,style:{width:i.getWidth(k)}}));return this._startIndex=I,this._stopIndex=e,m.createElement("div",{ref:this._setScrollingContainerRef,"aria-label":this.props["aria-label"],className:S("ReactVirtualized__Masonry",l),id:c,onScroll:this._onScroll,role:f,style:Ze({boxSizing:"border-box",direction:"ltr",height:r?"auto":s,overflowX:"hidden",overflowY:C<s?"hidden":"auto",position:"relative",width:g,WebkitOverflowScrolling:"touch",willChange:"transform"},h),tabIndex:p},m.createElement("div",{className:"ReactVirtualized__Masonry__innerScrollContainer",style:{width:"100%",height:C,maxWidth:"100%",maxHeight:C,overflow:"hidden",pointerEvents:b?"none":"",position:"relative"}},x))}},{key:"_checkInvalidateOnUpdate",value:function(){if("number"==typeof this._invalidateOnUpdateStartIndex){var e=this._invalidateOnUpdateStartIndex,t=this._invalidateOnUpdateStopIndex;this._invalidateOnUpdateStartIndex=null,this._invalidateOnUpdateStopIndex=null,this._populatePositionCache(e,t),this.forceUpdate()}}},{key:"_debounceResetIsScrolling",value:function(){var e=this.props.scrollingResetTimeInterval;this._debounceResetIsScrollingId&&W(this._debounceResetIsScrollingId),this._debounceResetIsScrollingId=U(this._debounceResetIsScrollingCallback,e)}},{key:"_getEstimatedTotalHeight",value:function(){var e=this.props,t=e.cellCount,n=e.cellMeasurerCache,r=e.width,o=Math.max(1,Math.floor(r/n.defaultWidth));return this._positionCache.estimateTotalHeight(t,o,n.defaultHeight)}},{key:"_invokeOnScrollCallback",value:function(){var e=this.props,t=e.height,n=e.onScroll,r=this.state.scrollTop;this._onScrollMemoized!==r&&(n({clientHeight:t,scrollHeight:this._getEstimatedTotalHeight(),scrollTop:r}),this._onScrollMemoized=r)}},{key:"_invokeOnCellsRenderedCallback",value:function(){this._startIndexMemoized===this._startIndex&&this._stopIndexMemoized===this._stopIndex||((0,this.props.onCellsRendered)({startIndex:this._startIndex,stopIndex:this._stopIndex}),this._startIndexMemoized=this._startIndex,this._stopIndexMemoized=this._stopIndex)}},{key:"_populatePositionCache",value:function(e,t){for(var n=this.props,r=n.cellMeasurerCache,o=n.cellPositioner,i=e;i<=t;i++){var a=o(i),l=a.left,s=a.top;this._positionCache.setPosition(i,l,s,r.getHeight(i))}}}],[{key:"getDerivedStateFromProps",value:function(e,t){return void 0!==e.scrollTop&&t.scrollTop!==e.scrollTop?{isScrolling:!0,scrollTop:e.scrollTop}:null}}]),t}(m.PureComponent),v()(Ve,"propTypes",null),Ke);function Je(){}v()(Xe,"defaultProps",{autoHeight:!1,keyMapper:function(e){return e},onCellsRendered:Je,onScroll:Je,overscanByPixels:20,role:"grid",scrollingResetTimeInterval:150,style:{},tabIndex:0,rowDirection:"ltr"}),Object(y.polyfill)(Xe);var $e=Xe;function Qe(e){var t,n=e.cellMeasurerCache,r=e.columnCount,o=e.columnWidth,i=e.spacer,a=void 0===i?0:i;function l(e){for(var r=0,i=1;i<t.length;i++)t[i]<t[r]&&(r=i);var l=r*(o+a),s=t[r]||0;return t[r]=s+n.getHeight(e)+a,{left:l,top:s}}function s(){t=[];for(var e=0;e<r;e++)t[e]=0}return s(),l.reset=function(e){r=e.columnCount,o=e.columnWidth,a=e.spacer,s()},l}var et=function(){function e(){var t=this,n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};o()(this,e),v()(this,"_cellMeasurerCache",void 0),v()(this,"_columnIndexOffset",void 0),v()(this,"_rowIndexOffset",void 0),v()(this,"columnWidth",(function(e){var n=e.index;t._cellMeasurerCache.columnWidth({index:n+t._columnIndexOffset})})),v()(this,"rowHeight",(function(e){var n=e.index;t._cellMeasurerCache.rowHeight({index:n+t._rowIndexOffset})}));var r=n.cellMeasurerCache,i=n.columnIndexOffset,a=void 0===i?0:i,l=n.rowIndexOffset,s=void 0===l?0:l;this._cellMeasurerCache=r,this._columnIndexOffset=a,this._rowIndexOffset=s}return a()(e,[{key:"clear",value:function(e,t){this._cellMeasurerCache.clear(e+this._rowIndexOffset,t+this._columnIndexOffset)}},{key:"clearAll",value:function(){this._cellMeasurerCache.clearAll()}},{key:"hasFixedHeight",value:function(){return this._cellMeasurerCache.hasFixedHeight()}},{key:"hasFixedWidth",value:function(){return this._cellMeasurerCache.hasFixedWidth()}},{key:"getHeight",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return this._cellMeasurerCache.getHeight(e+this._rowIndexOffset,t+this._columnIndexOffset)}},{key:"getWidth",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return this._cellMeasurerCache.getWidth(e+this._rowIndexOffset,t+this._columnIndexOffset)}},{key:"has",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return this._cellMeasurerCache.has(e+this._rowIndexOffset,t+this._columnIndexOffset)}},{key:"set",value:function(e,t,n,r){this._cellMeasurerCache.set(e+this._rowIndexOffset,t+this._columnIndexOffset,n,r)}},{key:"defaultHeight",get:function(){return this._cellMeasurerCache.defaultHeight}},{key:"defaultWidth",get:function(){return this._cellMeasurerCache.defaultWidth}}]),e}();function tt(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function nt(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?tt(n,!0).forEach((function(t){v()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):tt(n).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var rt=function(e){function t(e,n){var r;o()(this,t),r=s()(this,u()(t).call(this,e,n)),v()(f()(r),"state",{scrollLeft:0,scrollTop:0,scrollbarSize:0,showHorizontalScrollbar:!1,showVerticalScrollbar:!1}),v()(f()(r),"_deferredInvalidateColumnIndex",null),v()(f()(r),"_deferredInvalidateRowIndex",null),v()(f()(r),"_bottomLeftGridRef",(function(e){r._bottomLeftGrid=e})),v()(f()(r),"_bottomRightGridRef",(function(e){r._bottomRightGrid=e})),v()(f()(r),"_cellRendererBottomLeftGrid",(function(e){var t=e.rowIndex,n=O()(e,["rowIndex"]),o=r.props,i=o.cellRenderer,a=o.fixedRowCount;return t===o.rowCount-a?m.createElement("div",{key:n.key,style:nt({},n.style,{height:20})}):i(nt({},n,{parent:f()(r),rowIndex:t+a}))})),v()(f()(r),"_cellRendererBottomRightGrid",(function(e){var t=e.columnIndex,n=e.rowIndex,o=O()(e,["columnIndex","rowIndex"]),i=r.props,a=i.cellRenderer,l=i.fixedColumnCount,s=i.fixedRowCount;return a(nt({},o,{columnIndex:t+l,parent:f()(r),rowIndex:n+s}))})),v()(f()(r),"_cellRendererTopRightGrid",(function(e){var t=e.columnIndex,n=O()(e,["columnIndex"]),o=r.props,i=o.cellRenderer,a=o.columnCount,l=o.fixedColumnCount;return t===a-l?m.createElement("div",{key:n.key,style:nt({},n.style,{width:20})}):i(nt({},n,{columnIndex:t+l,parent:f()(r)}))})),v()(f()(r),"_columnWidthRightGrid",(function(e){var t=e.index,n=r.props,o=n.columnCount,i=n.fixedColumnCount,a=n.columnWidth,l=r.state,s=l.scrollbarSize;return l.showHorizontalScrollbar&&t===o-i?s:"function"==typeof a?a({index:t+i}):a})),v()(f()(r),"_onScroll",(function(e){var t=e.scrollLeft,n=e.scrollTop;r.setState({scrollLeft:t,scrollTop:n});var o=r.props.onScroll;o&&o(e)})),v()(f()(r),"_onScrollbarPresenceChange",(function(e){var t=e.horizontal,n=e.size,o=e.vertical,i=r.state,a=i.showHorizontalScrollbar,l=i.showVerticalScrollbar;if(t!==a||o!==l){r.setState({scrollbarSize:n,showHorizontalScrollbar:t,showVerticalScrollbar:o});var s=r.props.onScrollbarPresenceChange;"function"==typeof s&&s({horizontal:t,size:n,vertical:o})}})),v()(f()(r),"_onScrollLeft",(function(e){var t=e.scrollLeft;r._onScroll({scrollLeft:t,scrollTop:r.state.scrollTop})})),v()(f()(r),"_onScrollTop",(function(e){var t=e.scrollTop;r._onScroll({scrollTop:t,scrollLeft:r.state.scrollLeft})})),v()(f()(r),"_rowHeightBottomGrid",(function(e){var t=e.index,n=r.props,o=n.fixedRowCount,i=n.rowCount,a=n.rowHeight,l=r.state,s=l.scrollbarSize;return l.showVerticalScrollbar&&t===i-o?s:"function"==typeof a?a({index:t+o}):a})),v()(f()(r),"_topLeftGridRef",(function(e){r._topLeftGrid=e})),v()(f()(r),"_topRightGridRef",(function(e){r._topRightGrid=e}));var i=e.deferredMeasurementCache,a=e.fixedColumnCount,l=e.fixedRowCount;return r._maybeCalculateCachedStyles(!0),i&&(r._deferredMeasurementCacheBottomLeftGrid=l>0?new et({cellMeasurerCache:i,columnIndexOffset:0,rowIndexOffset:l}):i,r._deferredMeasurementCacheBottomRightGrid=a>0||l>0?new et({cellMeasurerCache:i,columnIndexOffset:a,rowIndexOffset:l}):i,r._deferredMeasurementCacheTopRightGrid=a>0?new et({cellMeasurerCache:i,columnIndexOffset:a,rowIndexOffset:0}):i),r}return p()(t,e),a()(t,[{key:"forceUpdateGrids",value:function(){this._bottomLeftGrid&&this._bottomLeftGrid.forceUpdate(),this._bottomRightGrid&&this._bottomRightGrid.forceUpdate(),this._topLeftGrid&&this._topLeftGrid.forceUpdate(),this._topRightGrid&&this._topRightGrid.forceUpdate()}},{key:"invalidateCellSizeAfterRender",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.columnIndex,n=void 0===t?0:t,r=e.rowIndex,o=void 0===r?0:r;this._deferredInvalidateColumnIndex="number"==typeof this._deferredInvalidateColumnIndex?Math.min(this._deferredInvalidateColumnIndex,n):n,this._deferredInvalidateRowIndex="number"==typeof this._deferredInvalidateRowIndex?Math.min(this._deferredInvalidateRowIndex,o):o}},{key:"measureAllCells",value:function(){this._bottomLeftGrid&&this._bottomLeftGrid.measureAllCells(),this._bottomRightGrid&&this._bottomRightGrid.measureAllCells(),this._topLeftGrid&&this._topLeftGrid.measureAllCells(),this._topRightGrid&&this._topRightGrid.measureAllCells()}},{key:"recomputeGridSize",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.columnIndex,n=void 0===t?0:t,r=e.rowIndex,o=void 0===r?0:r,i=this.props,a=i.fixedColumnCount,l=i.fixedRowCount,s=Math.max(0,n-a),c=Math.max(0,o-l);this._bottomLeftGrid&&this._bottomLeftGrid.recomputeGridSize({columnIndex:n,rowIndex:c}),this._bottomRightGrid&&this._bottomRightGrid.recomputeGridSize({columnIndex:s,rowIndex:c}),this._topLeftGrid&&this._topLeftGrid.recomputeGridSize({columnIndex:n,rowIndex:o}),this._topRightGrid&&this._topRightGrid.recomputeGridSize({columnIndex:s,rowIndex:o}),this._leftGridWidth=null,this._topGridHeight=null,this._maybeCalculateCachedStyles(!0)}},{key:"componentDidMount",value:function(){var e=this.props,t=e.scrollLeft,n=e.scrollTop;if(t>0||n>0){var r={};t>0&&(r.scrollLeft=t),n>0&&(r.scrollTop=n),this.setState(r)}this._handleInvalidatedGridSize()}},{key:"componentDidUpdate",value:function(){this._handleInvalidatedGridSize()}},{key:"render",value:function(){var e=this.props,t=e.onScroll,n=e.onSectionRendered,r=(e.onScrollbarPresenceChange,e.scrollLeft,e.scrollToColumn),o=(e.scrollTop,e.scrollToRow),i=O()(e,["onScroll","onSectionRendered","onScrollbarPresenceChange","scrollLeft","scrollToColumn","scrollTop","scrollToRow"]);if(this._prepareForRender(),0===this.props.width||0===this.props.height)return null;var a=this.state,l=a.scrollLeft,s=a.scrollTop;return m.createElement("div",{style:this._containerOuterStyle},m.createElement("div",{style:this._containerTopStyle},this._renderTopLeftGrid(i),this._renderTopRightGrid(nt({},i,{onScroll:t,scrollLeft:l}))),m.createElement("div",{style:this._containerBottomStyle},this._renderBottomLeftGrid(nt({},i,{onScroll:t,scrollTop:s})),this._renderBottomRightGrid(nt({},i,{onScroll:t,onSectionRendered:n,scrollLeft:l,scrollToColumn:r,scrollToRow:o,scrollTop:s}))))}},{key:"_getBottomGridHeight",value:function(e){return e.height-this._getTopGridHeight(e)}},{key:"_getLeftGridWidth",value:function(e){var t=e.fixedColumnCount,n=e.columnWidth;if(null==this._leftGridWidth)if("function"==typeof n){for(var r=0,o=0;o<t;o++)r+=n({index:o});this._leftGridWidth=r}else this._leftGridWidth=n*t;return this._leftGridWidth}},{key:"_getRightGridWidth",value:function(e){return e.width-this._getLeftGridWidth(e)}},{key:"_getTopGridHeight",value:function(e){var t=e.fixedRowCount,n=e.rowHeight;if(null==this._topGridHeight)if("function"==typeof n){for(var r=0,o=0;o<t;o++)r+=n({index:o});this._topGridHeight=r}else this._topGridHeight=n*t;return this._topGridHeight}},{key:"_handleInvalidatedGridSize",value:function(){if("number"==typeof this._deferredInvalidateColumnIndex){var e=this._deferredInvalidateColumnIndex,t=this._deferredInvalidateRowIndex;this._deferredInvalidateColumnIndex=null,this._deferredInvalidateRowIndex=null,this.recomputeGridSize({columnIndex:e,rowIndex:t}),this.forceUpdate()}}},{key:"_maybeCalculateCachedStyles",value:function(e){var t=this.props,n=t.columnWidth,r=t.enableFixedColumnScroll,o=t.enableFixedRowScroll,i=t.height,a=t.fixedColumnCount,l=t.fixedRowCount,s=t.rowHeight,c=t.style,u=t.styleBottomLeftGrid,d=t.styleBottomRightGrid,f=t.styleTopLeftGrid,h=t.styleTopRightGrid,p=t.width,g=e||i!==this._lastRenderedHeight||p!==this._lastRenderedWidth,v=e||n!==this._lastRenderedColumnWidth||a!==this._lastRenderedFixedColumnCount,m=e||l!==this._lastRenderedFixedRowCount||s!==this._lastRenderedRowHeight;(e||g||c!==this._lastRenderedStyle)&&(this._containerOuterStyle=nt({height:i,overflow:"visible",width:p},c)),(e||g||m)&&(this._containerTopStyle={height:this._getTopGridHeight(this.props),position:"relative",width:p},this._containerBottomStyle={height:i-this._getTopGridHeight(this.props),overflow:"visible",position:"relative",width:p}),(e||u!==this._lastRenderedStyleBottomLeftGrid)&&(this._bottomLeftGridStyle=nt({left:0,overflowX:"hidden",overflowY:r?"auto":"hidden",position:"absolute"},u)),(e||v||d!==this._lastRenderedStyleBottomRightGrid)&&(this._bottomRightGridStyle=nt({left:this._getLeftGridWidth(this.props),position:"absolute"},d)),(e||f!==this._lastRenderedStyleTopLeftGrid)&&(this._topLeftGridStyle=nt({left:0,overflowX:"hidden",overflowY:"hidden",position:"absolute",top:0},f)),(e||v||h!==this._lastRenderedStyleTopRightGrid)&&(this._topRightGridStyle=nt({left:this._getLeftGridWidth(this.props),overflowX:o?"auto":"hidden",overflowY:"hidden",position:"absolute",top:0},h)),this._lastRenderedColumnWidth=n,this._lastRenderedFixedColumnCount=a,this._lastRenderedFixedRowCount=l,this._lastRenderedHeight=i,this._lastRenderedRowHeight=s,this._lastRenderedStyle=c,this._lastRenderedStyleBottomLeftGrid=u,this._lastRenderedStyleBottomRightGrid=d,this._lastRenderedStyleTopLeftGrid=f,this._lastRenderedStyleTopRightGrid=h,this._lastRenderedWidth=p}},{key:"_prepareForRender",value:function(){this._lastRenderedColumnWidth===this.props.columnWidth&&this._lastRenderedFixedColumnCount===this.props.fixedColumnCount||(this._leftGridWidth=null),this._lastRenderedFixedRowCount===this.props.fixedRowCount&&this._lastRenderedRowHeight===this.props.rowHeight||(this._topGridHeight=null),this._maybeCalculateCachedStyles(),this._lastRenderedColumnWidth=this.props.columnWidth,this._lastRenderedFixedColumnCount=this.props.fixedColumnCount,this._lastRenderedFixedRowCount=this.props.fixedRowCount,this._lastRenderedRowHeight=this.props.rowHeight}},{key:"_renderBottomLeftGrid",value:function(e){var t=e.enableFixedColumnScroll,n=e.fixedColumnCount,r=e.fixedRowCount,o=e.rowCount,i=e.hideBottomLeftGridScrollbar,a=this.state.showVerticalScrollbar;if(!n)return null;var l=a?1:0,s=this._getBottomGridHeight(e),c=this._getLeftGridWidth(e),u=this.state.showVerticalScrollbar?this.state.scrollbarSize:0,d=i?c+u:c,f=m.createElement(Z,b()({},e,{cellRenderer:this._cellRendererBottomLeftGrid,className:this.props.classNameBottomLeftGrid,columnCount:n,deferredMeasurementCache:this._deferredMeasurementCacheBottomLeftGrid,height:s,onScroll:t?this._onScrollTop:void 0,ref:this._bottomLeftGridRef,rowCount:Math.max(0,o-r)+l,rowHeight:this._rowHeightBottomGrid,style:this._bottomLeftGridStyle,tabIndex:null,width:d}));return i?m.createElement("div",{className:"BottomLeftGrid_ScrollWrapper",style:nt({},this._bottomLeftGridStyle,{height:s,width:c,overflowY:"hidden"})},f):f}},{key:"_renderBottomRightGrid",value:function(e){var t=e.columnCount,n=e.fixedColumnCount,r=e.fixedRowCount,o=e.rowCount,i=e.scrollToColumn,a=e.scrollToRow;return m.createElement(Z,b()({},e,{cellRenderer:this._cellRendererBottomRightGrid,className:this.props.classNameBottomRightGrid,columnCount:Math.max(0,t-n),columnWidth:this._columnWidthRightGrid,deferredMeasurementCache:this._deferredMeasurementCacheBottomRightGrid,height:this._getBottomGridHeight(e),onScroll:this._onScroll,onScrollbarPresenceChange:this._onScrollbarPresenceChange,ref:this._bottomRightGridRef,rowCount:Math.max(0,o-r),rowHeight:this._rowHeightBottomGrid,scrollToColumn:i-n,scrollToRow:a-r,style:this._bottomRightGridStyle,width:this._getRightGridWidth(e)}))}},{key:"_renderTopLeftGrid",value:function(e){var t=e.fixedColumnCount,n=e.fixedRowCount;return t&&n?m.createElement(Z,b()({},e,{className:this.props.classNameTopLeftGrid,columnCount:t,height:this._getTopGridHeight(e),ref:this._topLeftGridRef,rowCount:n,style:this._topLeftGridStyle,tabIndex:null,width:this._getLeftGridWidth(e)})):null}},{key:"_renderTopRightGrid",value:function(e){var t=e.columnCount,n=e.enableFixedRowScroll,r=e.fixedColumnCount,o=e.fixedRowCount,i=e.scrollLeft,a=e.hideTopRightGridScrollbar,l=this.state,s=l.showHorizontalScrollbar,c=l.scrollbarSize;if(!o)return null;var u=s?1:0,d=this._getTopGridHeight(e),f=this._getRightGridWidth(e),h=s?c:0,p=d,g=this._topRightGridStyle;a&&(p=d+h,g=nt({},this._topRightGridStyle,{left:0}));var v=m.createElement(Z,b()({},e,{cellRenderer:this._cellRendererTopRightGrid,className:this.props.classNameTopRightGrid,columnCount:Math.max(0,t-r)+u,columnWidth:this._columnWidthRightGrid,deferredMeasurementCache:this._deferredMeasurementCacheTopRightGrid,height:p,onScroll:n?this._onScrollLeft:void 0,ref:this._topRightGridRef,rowCount:o,scrollLeft:i,style:g,tabIndex:null,width:f}));return a?m.createElement("div",{className:"TopRightGrid_ScrollWrapper",style:nt({},this._topRightGridStyle,{height:d,width:f,overflowX:"hidden"})},v):v}}],[{key:"getDerivedStateFromProps",value:function(e,t){return e.scrollLeft!==t.scrollLeft||e.scrollTop!==t.scrollTop?{scrollLeft:null!=e.scrollLeft&&e.scrollLeft>=0?e.scrollLeft:t.scrollLeft,scrollTop:null!=e.scrollTop&&e.scrollTop>=0?e.scrollTop:t.scrollTop}:null}}]),t}(m.PureComponent);v()(rt,"defaultProps",{classNameBottomLeftGrid:"",classNameBottomRightGrid:"",classNameTopLeftGrid:"",classNameTopRightGrid:"",enableFixedColumnScroll:!1,enableFixedRowScroll:!1,fixedColumnCount:0,fixedRowCount:0,scrollToColumn:-1,scrollToRow:-1,style:{},styleBottomLeftGrid:{},styleBottomRightGrid:{},styleTopLeftGrid:{},styleTopRightGrid:{},hideTopRightGridScrollbar:!1,hideBottomLeftGridScrollbar:!1}),rt.propTypes={},Object(y.polyfill)(rt);var ot=rt,it=function(e){function t(e,n){var r;return o()(this,t),(r=s()(this,u()(t).call(this,e,n))).state={clientHeight:0,clientWidth:0,scrollHeight:0,scrollLeft:0,scrollTop:0,scrollWidth:0},r._onScroll=r._onScroll.bind(f()(r)),r}return p()(t,e),a()(t,[{key:"render",value:function(){var e=this.props.children,t=this.state,n=t.clientHeight,r=t.clientWidth,o=t.scrollHeight,i=t.scrollLeft,a=t.scrollTop,l=t.scrollWidth;return e({clientHeight:n,clientWidth:r,onScroll:this._onScroll,scrollHeight:o,scrollLeft:i,scrollTop:a,scrollWidth:l})}},{key:"_onScroll",value:function(e){var t=e.clientHeight,n=e.clientWidth,r=e.scrollHeight,o=e.scrollLeft,i=e.scrollTop,a=e.scrollWidth;this.setState({clientHeight:t,clientWidth:n,scrollHeight:r,scrollLeft:o,scrollTop:i,scrollWidth:a})}}]),t}(m.PureComponent);function at(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=t.defaultSortBy,r=t.defaultSortDirection,o=void 0===r?{}:r;if(!e)throw Error('Required parameter "sortCallback" not specified');var i=n||[],a={};function l(t){var n=t.defaultSortDirection,r=t.event,o=t.sortBy;if(r.shiftKey)void 0!==a[o]?a[o]="ASC"===a[o]?"DESC":"ASC":(a[o]=n,i.push(o));else if(r.ctrlKey||r.metaKey){var l=i.indexOf(o);l>=0&&(i.splice(l,1),delete a[o])}else i.length=0,i.push(o),Object.keys(a).forEach((function(e){e!==o&&delete a[e]})),void 0!==a[o]?a[o]="ASC"===a[o]?"DESC":"ASC":a[o]=n;e({sortBy:i,sortDirection:a})}return i.forEach((function(e){a[e]=void 0!==o[e]?o[e]:"ASC"})),{sort:l,sortBy:i,sortDirection:a}}function lt(e){var t=e.dataKey,n=e.rowData;return"function"==typeof n.get?n.get(t):n[t]}function st(e){var t=e.cellData;return null==t?"":String(t)}function ct(e){var t=e.className,n=e.columns,r=e.style;return m.createElement("div",{className:t,role:"row",style:r},n)}it.propTypes={},ct.propTypes=null;var ut={ASC:"ASC",DESC:"DESC"};function dt(e){var t=e.sortDirection,n=S("ReactVirtualized__Table__sortableHeaderIcon",{"ReactVirtualized__Table__sortableHeaderIcon--ASC":t===ut.ASC,"ReactVirtualized__Table__sortableHeaderIcon--DESC":t===ut.DESC});return m.createElement("svg",{className:n,width:18,height:18,viewBox:"0 0 24 24"},t===ut.ASC?m.createElement("path",{d:"M7 14l5-5 5 5z"}):m.createElement("path",{d:"M7 10l5 5 5-5z"}),m.createElement("path",{d:"M0 0h24v24H0z",fill:"none"}))}function ft(e){var t=e.dataKey,n=e.label,r=e.sortBy,o=e.sortDirection,i=r===t,a=[m.createElement("span",{className:"ReactVirtualized__Table__headerTruncatedText",key:"label",title:"string"==typeof n?n:null},n)];return i&&a.push(m.createElement(dt,{key:"SortIndicator",sortDirection:o})),a}function ht(e){var t=e.className,n=e.columns,r=e.index,o=e.key,i=e.onRowClick,a=e.onRowDoubleClick,l=e.onRowMouseOut,s=e.onRowMouseOver,c=e.onRowRightClick,u=e.rowData,d=e.style,f={"aria-rowindex":r+1};return(i||a||l||s||c)&&(f["aria-label"]="row",f.tabIndex=0,i&&(f.onClick=function(e){return i({event:e,index:r,rowData:u})}),a&&(f.onDoubleClick=function(e){return a({event:e,index:r,rowData:u})}),l&&(f.onMouseOut=function(e){return l({event:e,index:r,rowData:u})}),s&&(f.onMouseOver=function(e){return s({event:e,index:r,rowData:u})}),c&&(f.onContextMenu=function(e){return c({event:e,index:r,rowData:u})})),m.createElement("div",b()({},f,{className:t,key:o,role:"row",style:d}),n)}dt.propTypes={},ft.propTypes=null,ht.propTypes=null;var pt=function(e){function t(){return o()(this,t),s()(this,u()(t).apply(this,arguments))}return p()(t,e),t}(m.Component);function gt(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function vt(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?gt(n,!0).forEach((function(t){v()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):gt(n).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}v()(pt,"defaultProps",{cellDataGetter:lt,cellRenderer:st,defaultSortDirection:ut.ASC,flexGrow:0,flexShrink:1,headerRenderer:ft,style:{}}),pt.propTypes={};var mt=function(e){function t(e){var n;return o()(this,t),(n=s()(this,u()(t).call(this,e))).state={scrollbarWidth:0},n._createColumn=n._createColumn.bind(f()(n)),n._createRow=n._createRow.bind(f()(n)),n._onScroll=n._onScroll.bind(f()(n)),n._onSectionRendered=n._onSectionRendered.bind(f()(n)),n._setRef=n._setRef.bind(f()(n)),n}return p()(t,e),a()(t,[{key:"forceUpdateGrid",value:function(){this.Grid&&this.Grid.forceUpdate()}},{key:"getOffsetForRow",value:function(e){var t=e.alignment,n=e.index;return this.Grid?this.Grid.getOffsetForCell({alignment:t,rowIndex:n}).scrollTop:0}},{key:"invalidateCellSizeAfterRender",value:function(e){var t=e.columnIndex,n=e.rowIndex;this.Grid&&this.Grid.invalidateCellSizeAfterRender({rowIndex:n,columnIndex:t})}},{key:"measureAllRows",value:function(){this.Grid&&this.Grid.measureAllCells()}},{key:"recomputeGridSize",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.columnIndex,n=void 0===t?0:t,r=e.rowIndex,o=void 0===r?0:r;this.Grid&&this.Grid.recomputeGridSize({rowIndex:o,columnIndex:n})}},{key:"recomputeRowHeights",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0;this.Grid&&this.Grid.recomputeGridSize({rowIndex:e})}},{key:"scrollToPosition",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0;this.Grid&&this.Grid.scrollToPosition({scrollTop:e})}},{key:"scrollToRow",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0;this.Grid&&this.Grid.scrollToCell({columnIndex:0,rowIndex:e})}},{key:"getScrollbarWidth",value:function(){if(this.Grid){var e=Object(se.findDOMNode)(this.Grid),t=e.clientWidth||0;return(e.offsetWidth||0)-t}return 0}},{key:"componentDidMount",value:function(){this._setScrollbarWidth()}},{key:"componentDidUpdate",value:function(){this._setScrollbarWidth()}},{key:"render",value:function(){var e=this,t=this.props,n=t.children,r=t.className,o=t.disableHeader,i=t.gridClassName,a=t.gridStyle,l=t.headerHeight,s=t.headerRowRenderer,c=t.height,u=t.id,d=t.noRowsRenderer,f=t.rowClassName,h=t.rowStyle,p=t.scrollToIndex,g=t.style,v=t.width,y=this.state.scrollbarWidth,_=o?c:c-l,w="function"==typeof f?f({index:-1}):f,x="function"==typeof h?h({index:-1}):h;return this._cachedColumnStyles=[],m.Children.toArray(n).forEach((function(t,n){var r=e._getFlexStyleForColumn(t,t.props.style);e._cachedColumnStyles[n]=vt({overflow:"hidden"},r)})),m.createElement("div",{"aria-label":this.props["aria-label"],"aria-labelledby":this.props["aria-labelledby"],"aria-colcount":m.Children.toArray(n).length,"aria-rowcount":this.props.rowCount,className:S("ReactVirtualized__Table",r),id:u,role:"grid",style:g},!o&&s({className:S("ReactVirtualized__Table__headerRow",w),columns:this._getHeaderColumns(),style:vt({height:l,overflow:"hidden",paddingRight:y,width:v},x)}),m.createElement(Z,b()({},this.props,{"aria-readonly":null,autoContainerWidth:!0,className:S("ReactVirtualized__Table__Grid",i),cellRenderer:this._createRow,columnWidth:v,columnCount:1,height:_,id:void 0,noContentRenderer:d,onScroll:this._onScroll,onSectionRendered:this._onSectionRendered,ref:this._setRef,role:"rowgroup",scrollbarWidth:y,scrollToRow:p,style:vt({},a,{overflowX:"hidden"})})))}},{key:"_createColumn",value:function(e){var t=e.column,n=e.columnIndex,r=e.isScrolling,o=e.parent,i=e.rowData,a=e.rowIndex,l=this.props.onColumnClick,s=t.props,c=s.cellDataGetter,u=s.cellRenderer,d=s.className,f=s.columnData,h=s.dataKey,p=s.id,g=u({cellData:c({columnData:f,dataKey:h,rowData:i}),columnData:f,columnIndex:n,dataKey:h,isScrolling:r,parent:o,rowData:i,rowIndex:a}),v=this._cachedColumnStyles[n],y="string"==typeof g?g:null;return m.createElement("div",{"aria-colindex":n+1,"aria-describedby":p,className:S("ReactVirtualized__Table__rowColumn",d),key:"Row"+a+"-Col"+n,onClick:function(e){l&&l({columnData:f,dataKey:h,event:e})},role:"gridcell",style:v,title:y},g)}},{key:"_createHeader",value:function(e){var t,n,r,o,i,a=e.column,l=e.index,s=this.props,c=s.headerClassName,u=s.headerStyle,d=s.onHeaderClick,f=s.sort,h=s.sortBy,p=s.sortDirection,g=a.props,v=g.columnData,y=g.dataKey,_=g.defaultSortDirection,b=g.disableSort,w=g.headerRenderer,x=g.id,C=g.label,O=!b&&f,T=S("ReactVirtualized__Table__headerColumn",c,a.props.headerClassName,{ReactVirtualized__Table__sortableHeaderColumn:O}),I=this._getFlexStyleForColumn(a,vt({},u,{},a.props.headerStyle)),D=w({columnData:v,dataKey:y,disableSort:b,label:C,sortBy:h,sortDirection:p});if(O||d){var k=h!==y?_:p===ut.DESC?ut.ASC:ut.DESC,R=function(e){O&&f({defaultSortDirection:_,event:e,sortBy:y,sortDirection:k}),d&&d({columnData:v,dataKey:y,event:e})};i=a.props["aria-label"]||C||y,o="none",r=0,t=R,n=function(e){"Enter"!==e.key&&" "!==e.key||R(e)}}return h===y&&(o=p===ut.ASC?"ascending":"descending"),m.createElement("div",{"aria-label":i,"aria-sort":o,className:T,id:x,key:"Header-Col"+l,onClick:t,onKeyDown:n,role:"columnheader",style:I,tabIndex:r},D)}},{key:"_createRow",value:function(e){var t=this,n=e.rowIndex,r=e.isScrolling,o=e.key,i=e.parent,a=e.style,l=this.props,s=l.children,c=l.onRowClick,u=l.onRowDoubleClick,d=l.onRowRightClick,f=l.onRowMouseOver,h=l.onRowMouseOut,p=l.rowClassName,g=l.rowGetter,v=l.rowRenderer,y=l.rowStyle,_=this.state.scrollbarWidth,b="function"==typeof p?p({index:n}):p,w="function"==typeof y?y({index:n}):y,x=g({index:n}),C=m.Children.toArray(s).map((function(e,o){return t._createColumn({column:e,columnIndex:o,isScrolling:r,parent:i,rowData:x,rowIndex:n,scrollbarWidth:_})})),O=S("ReactVirtualized__Table__row",b),T=vt({},a,{height:this._getRowHeight(n),overflow:"hidden",paddingRight:_},w);return v({className:O,columns:C,index:n,isScrolling:r,key:o,onRowClick:c,onRowDoubleClick:u,onRowRightClick:d,onRowMouseOver:f,onRowMouseOut:h,rowData:x,style:T})}},{key:"_getFlexStyleForColumn",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n="".concat(e.props.flexGrow," ").concat(e.props.flexShrink," ").concat(e.props.width,"px"),r=vt({},t,{flex:n,msFlex:n,WebkitFlex:n});return e.props.maxWidth&&(r.maxWidth=e.props.maxWidth),e.props.minWidth&&(r.minWidth=e.props.minWidth),r}},{key:"_getHeaderColumns",value:function(){var e=this,t=this.props,n=t.children;return(t.disableHeader?[]:m.Children.toArray(n)).map((function(t,n){return e._createHeader({column:t,index:n})}))}},{key:"_getRowHeight",value:function(e){var t=this.props.rowHeight;return"function"==typeof t?t({index:e}):t}},{key:"_onScroll",value:function(e){var t=e.clientHeight,n=e.scrollHeight,r=e.scrollTop;(0,this.props.onScroll)({clientHeight:t,scrollHeight:n,scrollTop:r})}},{key:"_onSectionRendered",value:function(e){var t=e.rowOverscanStartIndex,n=e.rowOverscanStopIndex,r=e.rowStartIndex,o=e.rowStopIndex;(0,this.props.onRowsRendered)({overscanStartIndex:t,overscanStopIndex:n,startIndex:r,stopIndex:o})}},{key:"_setRef",value:function(e){this.Grid=e}},{key:"_setScrollbarWidth",value:function(){var e=this.getScrollbarWidth();this.setState({scrollbarWidth:e})}}]),t}(m.PureComponent);v()(mt,"defaultProps",{disableHeader:!1,estimatedRowSize:30,headerHeight:0,headerStyle:{},noRowsRenderer:function(){return null},onRowsRendered:function(){return null},onScroll:function(){return null},overscanIndicesGetter:X,overscanRowCount:10,rowRenderer:ht,headerRowRenderer:ct,rowStyle:{},scrollToAlignment:"auto",scrollToIndex:-1,style:{}}),mt.propTypes={};var yt=[],_t=null,bt=null;function wt(){bt&&(bt=null,document.body&&null!=_t&&(document.body.style.pointerEvents=_t),_t=null)}function St(){wt(),yt.forEach((function(e){return e.__resetIsScrolling()}))}function xt(e){e.currentTarget===window&&null==_t&&document.body&&(_t=document.body.style.pointerEvents,document.body.style.pointerEvents="none"),function(){bt&&W(bt);var e=0;yt.forEach((function(t){e=Math.max(e,t.props.scrollingResetTimeInterval)})),bt=U(St,e)}(),yt.forEach((function(t){t.props.scrollElement===e.currentTarget&&t.__handleWindowScrollEvent()}))}function Ct(e,t){yt.some((function(e){return e.props.scrollElement===t}))||t.addEventListener("scroll",xt),yt.push(e)}function Ot(e,t){(yt=yt.filter((function(t){return t!==e}))).length||(t.removeEventListener("scroll",xt),bt&&(W(bt),wt()))}var Tt,It,Dt=function(e){return e===window},kt=function(e){return e.getBoundingClientRect()};function Rt(e,t){if(e){if(Dt(e)){var n=window,r=n.innerHeight,o=n.innerWidth;return{height:"number"==typeof r?r:0,width:"number"==typeof o?o:0}}return kt(e)}return{height:t.serverHeight,width:t.serverWidth}}function Pt(e,t){if(Dt(t)&&document.documentElement){var n=document.documentElement,r=kt(e),o=kt(n);return{top:r.top-o.top,left:r.left-o.left}}var i=Et(t),a=kt(e),l=kt(t);return{top:a.top+i.top-l.top,left:a.left+i.left-l.left}}function Et(e){return Dt(e)&&document.documentElement?{top:"scrollY"in window?window.scrollY:document.documentElement.scrollTop,left:"scrollX"in window?window.scrollX:document.documentElement.scrollLeft}:{top:e.scrollTop,left:e.scrollLeft}}function Mt(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function jt(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Mt(n,!0).forEach((function(t){v()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Mt(n).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var zt=function(){return"undefined"!=typeof window?window:void 0},Nt=(It=Tt=function(e){function t(){var e,n;o()(this,t);for(var r=arguments.length,i=new Array(r),a=0;a<r;a++)i[a]=arguments[a];return n=s()(this,(e=u()(t)).call.apply(e,[this].concat(i))),v()(f()(n),"_window",zt()),v()(f()(n),"_isMounted",!1),v()(f()(n),"_positionFromTop",0),v()(f()(n),"_positionFromLeft",0),v()(f()(n),"_detectElementResize",void 0),v()(f()(n),"_child",void 0),v()(f()(n),"state",jt({},Rt(n.props.scrollElement,n.props),{isScrolling:!1,scrollLeft:0,scrollTop:0})),v()(f()(n),"_registerChild",(function(e){!e||e instanceof Element||console.warn("WindowScroller registerChild expects to be passed Element or null"),n._child=e,n.updatePosition()})),v()(f()(n),"_onChildScroll",(function(e){var t=e.scrollTop;if(n.state.scrollTop!==t){var r=n.props.scrollElement;r&&("function"==typeof r.scrollTo?r.scrollTo(0,t+n._positionFromTop):r.scrollTop=t+n._positionFromTop)}})),v()(f()(n),"_registerResizeListener",(function(e){e===window?window.addEventListener("resize",n._onResize,!1):n._detectElementResize.addResizeListener(e,n._onResize)})),v()(f()(n),"_unregisterResizeListener",(function(e){e===window?window.removeEventListener("resize",n._onResize,!1):e&&n._detectElementResize.removeResizeListener(e,n._onResize)})),v()(f()(n),"_onResize",(function(){n.updatePosition()})),v()(f()(n),"__handleWindowScrollEvent",(function(){if(n._isMounted){var e=n.props.onScroll,t=n.props.scrollElement;if(t){var r=Et(t),o=Math.max(0,r.left-n._positionFromLeft),i=Math.max(0,r.top-n._positionFromTop);n.setState({isScrolling:!0,scrollLeft:o,scrollTop:i}),e({scrollLeft:o,scrollTop:i})}}})),v()(f()(n),"__resetIsScrolling",(function(){n.setState({isScrolling:!1})})),n}return p()(t,e),a()(t,[{key:"updatePosition",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.props.scrollElement,t=this.props.onResize,n=this.state,r=n.height,o=n.width,i=this._child||se.findDOMNode(this);if(i instanceof Element&&e){var a=Pt(i,e);this._positionFromTop=a.top,this._positionFromLeft=a.left}var l=Rt(e,this.props);r===l.height&&o===l.width||(this.setState({height:l.height,width:l.width}),t({height:l.height,width:l.width}))}},{key:"componentDidMount",value:function(){var e=this.props.scrollElement;this._detectElementResize=Object(ne.a)(),this.updatePosition(e),e&&(Ct(this,e),this._registerResizeListener(e)),this._isMounted=!0}},{key:"componentDidUpdate",value:function(e,t){var n=this.props.scrollElement,r=e.scrollElement;r!==n&&null!=r&&null!=n&&(this.updatePosition(n),Ot(this,r),Ct(this,n),this._unregisterResizeListener(r),this._registerResizeListener(n))}},{key:"componentWillUnmount",value:function(){var e=this.props.scrollElement;e&&(Ot(this,e),this._unregisterResizeListener(e)),this._isMounted=!1}},{key:"render",value:function(){var e=this.props.children,t=this.state,n=t.isScrolling,r=t.scrollTop,o=t.scrollLeft,i=t.height,a=t.width;return e({onChildScroll:this._onChildScroll,registerChild:this._registerChild,height:i,isScrolling:n,scrollLeft:o,scrollTop:r,width:a})}}]),t}(m.PureComponent),v()(Tt,"propTypes",null),It);v()(Nt,"defaultProps",{onResize:function(){},onScroll:function(){},scrollingResetTimeInterval:150,scrollElement:zt(),serverHeight:0,serverWidth:0})},,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,function(e,t,n){"use strict";n.r(t);var r=n(1),o=n.n(r),i=n(4),a=n.n(i),l=n(5),s=n.n(l),c=n(8),u=n.n(c),d=n(6),f=n.n(d),h=n(7),p=n.n(h),g=n(0),v=n.n(g),m=n(13),y=n.n(m);function _(){return React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none"},React.createElement("path",{d:"M17.3274 7.53073L10.5767 16.6097L6.66221 13.6991",stroke:"white","stroke-width":"1.5"}))}function b(){return React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none"},React.createElement("path",{d:"M5 3.75H19C19.6904 3.75 20.25 4.30964 20.25 5V19C20.25 19.6904 19.6904 20.25 19 20.25H5C4.30964 20.25 3.75 19.6904 3.75 19V5C3.75 4.30964 4.30964 3.75 5 3.75Z",stroke:"white","stroke-width":"1.5"}),React.createElement("path",{d:"M9 4L5 8.5L5 4L9 4Z",fill:"white",stroke:"white"}),React.createElement("path",{d:"M14 15.2857L17 12L14 9",stroke:"white","stroke-width":"1.5"}),React.createElement("path",{d:"M10 15.2857L7 12L10 9",stroke:"white","stroke-width":"1.5"}))}function w(){return React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none"},React.createElement("path",{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M11.9287 18C15.2424 18 17.9287 15.3137 17.9287 12C17.9287 8.68629 15.2424 6 11.9287 6C8.615 6 5.92871 8.68629 5.92871 12C5.92871 15.3137 8.615 18 11.9287 18ZM11.9287 15C13.5856 15 14.9287 13.6569 14.9287 12C14.9287 10.3431 13.5856 9 11.9287 9C10.2719 9 8.92871 10.3431 8.92871 12C8.92871 13.6569 10.2719 15 11.9287 15Z",fill:"white"}),React.createElement("path",{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M11.2758 4C10.787 4 10.3698 4.35341 10.2894 4.8356L9.92871 7H13.9287L13.568 4.8356C13.4876 4.35341 13.0704 4 12.5816 4H11.2758ZM12.5816 20C13.0704 20 13.4876 19.6466 13.568 19.1644L13.9287 17H9.92871L10.2894 19.1644C10.3698 19.6466 10.787 20 11.2758 20H12.5816Z",fill:"white"}),React.createElement("path",{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M18.53 7.43471C18.2856 7.01137 17.7709 6.82677 17.3132 6.99827L15.2584 7.76807L17.2584 11.2322L18.9524 9.83756C19.3298 9.52687 19.4273 8.98887 19.1829 8.56552L18.53 7.43471ZM5.32647 16.5655C5.57089 16.9889 6.08555 17.1735 6.54332 17.002L8.59811 16.2322L6.59811 12.7681L4.90406 14.1627C4.52665 14.4734 4.42918 15.0114 4.6736 15.4347L5.32647 16.5655Z",fill:"white"}),React.createElement("path",{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M4.67454 8.56553C4.43012 8.98888 4.52759 9.52688 4.90499 9.83757L6.59905 11.2322L8.59905 7.76808L6.54426 6.99828C6.08649 6.82678 5.57183 7.01138 5.32741 7.43472L4.67454 8.56553ZM19.1838 15.4347C19.4282 15.0114 19.3308 14.4734 18.9534 14.1627L17.2593 12.7681L15.2593 16.2322L17.3141 17.002C17.7719 17.1735 18.2865 16.9889 18.5309 16.5655L19.1838 15.4347Z",fill:"white"}))}var S=n(12),x=n(11),C={getTreeData:function(e){return e.treeData},getRowInfo:function(e){return e.rowInfo},getStates:function(e){return e},getVisualData:function(e){return e.visualData},getFields:function(e){return e.fields},getTest:function(e){return console.log("TEST",e),[55,66]}},O=n(18),T=n.n(O),I=n(33),D=n(9);function k(e,t){var n="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!n){if(Array.isArray(e)||(n=function(e,t){if(e){if("string"==typeof e)return R(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?R(e,t):void 0}}(e))||t&&e&&"number"==typeof e.length){n&&(e=n);var _n=0,r=function(){};return{s:r,n:function(){return _n>=e.length?{done:!0}:{done:!1,value:e[_n++]}},e:function(e){throw e},f:r}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,i=!0,a=!1;return{s:function(){n=n.call(e)},n:function(){var e=n.next();return i=e.done,e},e:function(e){a=!0,o=e},f:function(){try{i||null==n.return||n.return()}finally{if(a)throw o}}}}function R(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=Array(t);n<t;n++)r[n]=e[n];return r}function P(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function E(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?P(Object(n),!0).forEach((function(t){o()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):P(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var M=wp.hooks.applyFilters,j=M("cf7svisual.state",{treeData:[],rowInfo:null,showEditModal:!1,notice:{status:"success",content:"",isDismissible:!0},showTreeData:!1,showRequired:!0,visualData:[]}),__=E({},window.wp.i18n).__;function z(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}(0,function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?z(Object(n),!0).forEach((function(t){o()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):z(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}({},window.wp.data).registerStore)("cf7svisual",{reducer:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:j,t=arguments.length>1?arguments[1]:void 0,n=function(t){if(t.path)return Object(I.getNodeAtPath)({treeData:e.treeData.slice(),path:t.path.slice(0,1),getNodeKey:function(e){return e.treeIndex},ignoreCollapsed:!0}).node},r=function(e,t,n){return Object(I.changeNodeAtPath)({treeData:e,getNodeKey:function(e){return e.treeIndex},path:t.path,newNode:n,ignoreCollapsed:!0})},o=function(n){var o=e.treeData.slice(),i=E({},e.rowInfo),a=t.event.target,l=a.name,s=void 0===l?"name is missing":l,c=a.type,u=void 0===c?"type is missing":c,d=a.value,f=void 0===d?"value is missing":d;"cf7Name"===s?i.node[s]=f.replace(/[^0-9a-zA-Z:._-]/g,"").replace(/^[^a-zA-Z]+/,""):"cf7sLabel"===s?i.node[s]=f.replace(/<(?:.|\n)*?>/gm,""):"cf7IdAttribute"===s?i.node[s]=f.replace(/[^-0-9a-zA-Z_]/g,""):"cf7FileTypes"===s?i.node[s]=f.replace(/[^0-9a-zA-Z.,|\s]/g,""):"cf7Limit"===s?i.node[s]=f.replace(/[^0-9kKmMbB]/g,""):"cf7ClassAttribute"===s?i.node[s]=f.replace(/[^-0-9a-zA-Z_ ]/g,""):"cf7Values"===s||"cf7sContent"===s?i.node[s]=f.trim():"number"===u?i.node[s]=f.replace(/[^0-9.-]/g,""):"date"===u&&(f.match(/^\d{4}-\d{2}-\d{2}$/)||(i.node[s]=""));var h=r(o,i,i.node);return E(E({},e),{},{treeData:h,rowInfo:i})};switch(t.type){case"UPDATE_TREEDATA":return E(E({},e),{},{treeData:t.treeData});case"UPDATE_VISUAL_STATE":return E(E({},e),t.state);case"ON_CHANGE_TREEDATA":var i=t.treeData.slice(),a=function(e){e.lowerSiblingCounts;var t=e.node,n=e.parentNode,r=e.path,o=(e.treeIndex,Object(S.cf7sSurroundingRules)(t,n));return o!==t?(i=Object(I.changeNodeAtPath)({treeData:i,getNodeKey:function(e){return e.treeIndex},path:r,newNode:o,ignoreCollapsed:!0}),!1):i};return Object(I.walk)({treeData:i,getNodeKey:function(e){return e.treeIndex},callback:a}),E(E({},e),{},{treeData:i});case"ADD_NODE":var l=e.treeData.slice(),s=E({},t.node),c=Object(S.cf7sDropRules)({node:s,treeData:l});if(!c)return E(E({},e),{},{notice:{status:"error",content:__("Unable to add the item.","contact-form-7-skins"),isDismissible:!0}});var u=Object(S.cf7sSurroundingRules)(s,null);return Object(S.randomizeName)(u),l.push(E({},u)),E(E({},e),{},{treeData:l});case"EDIT_NODE":var d=!1,f=t.rowInfo;if(e.rowInfo&&e.rowInfo.node.cf7Name===f.node.cf7Name)return E(E({},e),{},{showEditModal:!1,rowInfo:null});if(["list-ol","list-li"].indexOf(f.node.cf7sType)>-1){var h=M("cf7svisual.edit",[]),p=function(){return E(E({},e),{},{showEditModal:!1,showTreeData:d,notice:E(E({},e.notice),{},{status:"error",content:__("There is no edit field available for this item.","contact-form-7-skins")})})};h.length||p();var g,v=!1,m=k(h);try{for(m.s();!(g=m.n()).done;){var y=g.value;y.types&&y.types.indexOf(f.node.cf7sType)>-1&&(v=!0)}}catch(e){m.e(e)}finally{m.f()}v||p()}return f.ancestorNode=n(f,e.treeData),E(E({},e),{},{showEditModal:!0,rowInfo:f,showTreeData:d});case"DUPLICATE_NODE":var _=E({},t.rowInfo);if("undefined"===_.treeIndex)return;var b=Object(D.cloneDeep)(_.node);Object(S.randomizeName)(b);var w=Object(S.cf7sDuplicateRules)(_);if("boolean"==typeof w){var x=Object(I.insertNode)({treeData:e.treeData,newNode:b,depth:_.path.length-1,minimumTreeIndex:_.treeIndex+1,expandParent:!0,getNodeKey:function(e){return e.treeIndex}}),C=x.treeData;return E(E({},e),{},{treeData:C})}if("surrounding"===w){var O=Object(D.cloneDeep)(_.parentNode);(O=Object(S.randomizeName)(O)).children.forEach((function(e,t){Object(S.randomizeName)(O.children[t])}));var R=Object(I.insertNode)({treeData:e.treeData,newNode:O,depth:_.path.length-2,minimumTreeIndex:_.treeIndex+1,expandParent:!0,getNodeKey:function(e){return e.treeIndex}}),P=R.treeData;return E(E({},e),{},{treeData:P})}return E(E({},e),{},{notice:E(E({},e.notice),{},{status:"error",content:"<strong>"+__("Unable to duplicate!","contact-form-7-skins")+"</strong><br /> "+w})});case"DELETE_NODE":var z=Object(I.removeNodeAtPath)({treeData:e.treeData.slice(),path:t.rowInfo.path,getNodeKey:function(e){return e.treeIndex},ignoreCollapsed:!0}),N=e.showEditModal;return e.rowInfo&&(Object(D.isEqual)(t.rowInfo.path,e.rowInfo.path)&&(N=!1),e.rowInfo&&t.rowInfo.node.children&&Object(I.isDescendant)(t.rowInfo.node,e.rowInfo.node)&&(N=!1)),E(E({},e),{},{treeData:z,showEditModal:N});case"CHANGE_NODE_TYPE":var L=e.treeData.slice(),A=E({},t.rowInfo),H=Object(D.cloneDeep)(A.node),G=t.element.target.value,F=E(E({},A.node),{cf7sType:G}),W=Object(S.cf7sDropRules)({node:F,treeData:L}),U={};if(W){var B=T()(S.cf7sItems);B.forEach((function(e,t){e.cf7sType===G&&(A.node=E({},e))})),F=Object(S.cf7sSurroundingRules)(A.node,A.parentNode),Object(S.randomizeName)(A.node)}else F=H,U.content=__("Unable to change the item.","contact-form-7-skins"),U.status="error";var V=r(L,A,F);return E(E({},e),{},{treeData:V,rowInfo:A,showEditModal:!0,notice:E(E({},e.notice),U)});case"EDIT_ON_CHANGE":var K,q=e.treeData.slice(),Y=E({},t),Z=Y.event,X=Y.optionsArrayType,J=Y.index;if(t.rowInfo)K=E({},t.rowInfo);else{var $;if(K=E({},e.rowInfo),"function"==typeof Z.stopPropagation){Z.stopPropagation();var Q=Z.target,ee=void 0===Q?{error:"the event target is missing"}:Q;$=ee}else $=Z;var te=$,ne=te.name,re=void 0===ne?"name is missing":ne,oe=te.value,ie=void 0===oe?"value is missing":oe,ae=te.checked,le=void 0===ae?"checked is missing":ae,se=te.type,ce=void 0===se?"type is missing":se;if("checkbox"===ce)if(null===X&&null===J)K.node[re]=le;else if(null==X&&null!==J){K.node[re]||(K.node[re]=[],K.node[re].push());var ue=K.node[re].indexOf(J);ue<0?K.node[re].push(J):K.node[re].splice(ue,1)}else null!==X&&null!==J&&(K.node[X][J].isChecked=le);else"radio"===ce&&null!==J?(K.node[X][J].isChecked=le,K.node[X].map((function(e,t){return t!==J&&(K.node[X][t].isChecked=!1),K.node[X][t]}))):"range"===ce||"number"===ce?K.node[re]=parseInt(ie,10):"text"===ce&&null!==J?K.node.cf7Options[J][re]=ie:K.node[re]=ie}var de=r(q,K,K.node);return E(E({},e),{},{treeData:de,rowInfo:K});case"EDIT_ON_KEY_PRESS":return"Enter"===t.event.key&&"textarea"!==t.event.target.type?(e.rowInfo.path&&o(t.event),E(E({},e),{},{showTreeData:!1,showEditModal:!1,rowInfo:null})):E({},e);case"EDIT_ADD_OPTION":var fe=E({},e.rowInfo);return fe.node.cf7Options.push({value:"Option "+(fe.node.cf7Options.length+1),isChecked:!1}),E(E({},e),{},{rowInfo:fe});case"EDIT_REMOVE_OPTION":var he=E({},e.rowInfo),pe=t.index;return"radio"===he.node.cf7sType&&!0===he.node.cf7Options[pe].isChecked&&he.node.cf7Options.length>1&&(0!==pe?he.node.cf7Options[0].isChecked=!0:he.node.cf7Options[pe+1].isChecked=!0),he.node.cf7Options.splice(pe,1),E(E({},e),{},{rowInfo:he});case"VALIDATE_INPUT":return o(t.event);case"VALIDATE_VISUAL_DATA":var ge=e.treeData.slice(),ve=[];S.cf7sItems.forEach((function(e,t){ve.push(e.cf7sType)}));var me=function(n){return t.event.target.value=JSON.stringify(ge),E(E({},e),{},{notice:E(E({},e.notice),{},{status:"error",content:n})})};try{JSON.parse(t.event.target.value)}catch(e){return me(__("Visual Object Data is not valid ( JSON parse error )","contact-form-7-skins"))}var ye=JSON.parse(t.event.target.value);if(Object(D.isPlainObject)(ye)&&Object(D.isEmpty)(ye))return me(__("Visual data is empty.","contact-form-7-skins"));var _e=function e(t){var n,r=k(t);try{for(r.s();!(n=r.n()).done;){var o=n.value;(Object(D.isEmpty)(o)||void 0===o.cf7sType||ve.indexOf(o.cf7sType)<0)&&me(__("Visual data is not valid.","contact-form-7-skins")),o.children&&e(o.children)}}catch(e){r.e(e)}finally{r.f()}};return _e(ye),E(E({},e),{},{treeData:ye});case"SET_NOTICE":return E(E({},e),{},{notice:{status:t.status,content:t.content,isDismissible:t.isDismissible}});case"REMOVE_NOTICE":return E(E({},e),{},{notice:{status:"",content:"",isDismissible:!0}});case"CLICK_MENU":var be=E({},t.rowInfo);if(!t.menu)return be.ancestorNode=n(be,e.treeData),E(E({},e),{},{rowInfo:be});var we=E({},t.menu),Se=we.name,xe=we.index,Ce=we.multiple;if(!1===Ce)be.node[Se]=xe;else{be.node[Se]||(be.node[Se]=[],be.node[Se].push());var Oe=be.node[Se].indexOf(xe);Oe<0?be.node[Se].push(xe):be.node[Se].splice(Oe,1)}return be.ancestorNode=n(be,e.treeData),E(E({},e),{},{rowInfo:be});case"CLICK_VISUAL_OBJECT":if(e.showEditModal&&"visual-data"===e.rowInfo.node.cf7sType)return E(E({},e),{},{showEditModal:!1,rowInfo:null});var Te={node:{}};return Te.node.cf7sType="visual-data",Te.node.cf7sSelectLabel="Visual Data",E(E({},e),{},{showEditModal:!0,rowInfo:Te});case"CLICK_FORM_OPTIONS":var Ie={node:{}};return Ie.node.cf7sType="form-options",Ie.node.cf7sSelectLabel="Form Options",E(E({},e),{},{showEditModal:!1,rowInfo:Ie});case"CLOSE_FORM_OPTIONS":return E(E({},e),{},{rowInfo:null});case"CLOSE_EDIT":return E(E({},e),{},{showEditModal:!1,showTreeData:!1,rowInfo:null});case"SET_VISUAL_DATA":return E(E({},e),{},{visualData:t.data});default:return E({},e)}},actions:{updateTreeData:function(e){return{type:"UPDATE_TREEDATA",treeData:e}},updateVisualState:function(e){return{type:"UPDATE_VISUAL_STATE",state:e}},onChangeTreeData:function(e){return{type:"ON_CHANGE_TREEDATA",treeData:e}},addNode:function(e){return{type:"ADD_NODE",node:e}},editNode:function(e){return{type:"EDIT_NODE",rowInfo:e}},duplicateNode:function(e){return{type:"DUPLICATE_NODE",rowInfo:e}},deleteNode:function(e){return{type:"DELETE_NODE",rowInfo:e}},changeNodeType:function(e,t){return{type:"CHANGE_NODE_TYPE",element:e,rowInfo:t}},editOnChange:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null,r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:null;return{type:"EDIT_ON_CHANGE",event:e,optionsArrayType:t,index:n,rowInfo:r}},editOnKeyPress:function(e){return{type:"EDIT_ON_KEY_PRESS",event:e}},editAddOption:function(e){return{type:"EDIT_ADD_OPTION",event:e}},editRemoveOption:function(e,t){return{type:"EDIT_REMOVE_OPTION",event:e,index:t}},validateInput:function(e){return{type:"VALIDATE_INPUT",event:e}},validateVisualData:function(e){return{type:"VALIDATE_VISUAL_DATA",event:e}},setNotice:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"success",n=!(arguments.length>2&&void 0!==arguments[2])||arguments[2];return{type:"SET_NOTICE",content:e,status:t,isDismissible:n}},removeNotice:function(){return{type:"REMOVE_NOTICE"}},clickMenu:function(e,t){return{type:"CLICK_MENU",rowInfo:e,menu:t}},clickVisualObject:function(){return{type:"CLICK_VISUAL_OBJECT"}},clickFormOptions:function(){return{type:"CLICK_FORM_OPTIONS"}},closeFormOptions:function(){return{type:"CLOSE_FORM_OPTIONS"}},closeEdit:function(){return{type:"CLOSE_EDIT"}},setVisualData:function(e){return{type:"SET_VISUAL_DATA",data:e}}},selectors:C,controls:{},resolvers:{}});var N=function(){return React.createElement("div",null,"Items")},L=wp.data.useDispatch,A=function(){var e=L("cf7svisual").updateVisualState;return React.createElement("div",null,React.createElement("div",null,"Topbar"),React.createElement("button",{onClick:function(){e({treeData:{a:111,b:222}})}},"Click"))},H=((0,wp.data.withSelect)((function(e){return e("cf7svisual").getStates()}))((function(e){return console.log("TEST"),React.createElement("div",null,React.createElement(A,null),React.createElement(N,null))})),n(10));function G(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}var F=wp.i18n.__,W=wp.components,U=W.Button,B=W.Modal,V=wp.element,K=(V.useState,V.useEffect,V.useRef,V.useCallback,wp.compose),q=(K.ifCondition,K.compose,wp.data),Y=q.withSelect,Z=(q.withDispatch,q.select),X=q.dispatch,J=wp.hooks,$=J.applyFilters,Q=J.doAction,ee=$("cf7s.tabs",[]),te=(g.Component,function(e){function t(e){var n,r,i,l;return a()(this,t),r=this,i=t,l=[e],i=f()(i),n=u()(r,function(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(e){}return!!e}()?Reflect.construct(i,l||[],f()(r).constructor):i.apply(r,l)),o()(n,"handleActiveTab",(function(e){n.setState({active:e})})),o()(n,"buildVisualData",(function(){var e=Z("cf7svisual").getVisualData();return $("cf7svisual.data",{form:e},Z)})),o()(n,"openVisualModal",(function(){var e=n.buildVisualData();n.setState({allData:e,modalDataOpened:!0})})),o()(n,"applyVisualData",(function(){var e;if(null!==(e=n.ref.current)&&void 0!==e&&e.value)try{var t,r=JSON.parse(null===(t=n.ref.current)||void 0===t?void 0:t.value),o=!!r.hasOwnProperty("visualData")&&r.visualData;o&&X("cf7svisual").updateVisualState({treeData:o,visualData:o,showEditModal:!1}),Q("cf7svisual.apply.data",r,X),n.setState({modalDataOpened:!1})}catch(e){return void console.log(e)}else console.warn("Visual Data only accepts object and not empty.")})),o()(n,"applyOptionsData",(function(){n.setState({modalOptionsOpened:!1})})),o()(n,"closeVisualModal",(function(){n.setState({modalDataOpened:!1})})),o()(n,"closeOptionsModal",(function(){X("cf7svisual").closeFormOptions(),n.setState({modalOptionsOpened:!1})})),o()(n,"openFormOptions",(function(){X("cf7svisual").clickFormOptions(),n.setState({modalOptionsOpened:!0})})),o()(n,"setMessage",(function(e){n.setState({message:e})})),o()(n,"renderSaveButton",(function(){var e=ee.map((function(e){return e.id}));return v.a.createElement(U,{variant:"primary",isBusy:n.state.isRequesting,icon:_,onClick:function(){return Object(x.f)(e,n.setState.bind(n))}},F("Save Form","contact-form-7-skins"))})),o()(n,"renderFormDataButton",(function(){var e=Object(S.getVisualVar)("options");if(e&&e.showCopyPaste)return v.a.createElement(U,{variant:"primary",icon:b,iconPosition:"left",showTooltip:!0,tooltipPosition:"top center",label:F("Copy & paste visual data","contact-form-7-skins"),onClick:n.openVisualModal},F("Visual Data","contact-form-7-skins"))})),o()(n,"renderFormOptionsButton",(function(){var e=!1,t=[],r=$("cf7svisual.form.options",[]);if(r.length)for(var o=0;o<r.length;++o){e=!0;var i=r[o];t.push(v.a.createElement(i,{key:o,styles:H.a}))}if(e)return v.a.createElement(v.a.Fragment,null,v.a.createElement(U,{variant:"primary",icon:w,iconPosition:"left",showTooltip:!0,tooltipPosition:"top center",label:F("Configure this forms options","contact-form-7-skins"),onClick:n.openFormOptions},F("Form Options","contact-form-7-skins")),n.state.modalOptionsOpened&&v.a.createElement(B,{title:F("Form Options","contact-form-7-skins"),onRequestClose:n.closeOptionsModal},t.map((function(e){return e})),v.a.createElement("div",{className:"buttonWrapper___FLNPs"},v.a.createElement(U,{variant:"primary",className:"button-primary",onClick:function(){return n.applyOptionsData()}},F("Done","contact-form-7-skins")))))})),n.ref=v.a.createRef(),n.state={active:"visual",modalDataOpened:!1,modalOptionsOpened:!1,allData:{},visualData:[],message:null,isRequesting:!1},n}return p()(t,e),s()(t,[{key:"componentDidMount",value:function(){}},{key:"componentWillUnmount",value:function(){}},{key:"componentDidUpdate",value:function(){}},{key:"render",value:function(){var e=this;return Z("cf7svisual").getStates().treeData,v.a.createElement(v.a.Fragment,null,v.a.createElement("div",{className:"cf7s-actions"},this.renderSaveButton(),this.renderFormDataButton(),this.renderFormOptionsButton()),this.state.modalDataOpened&&v.a.createElement(B,{title:F("Visual Data","contact-form-7-skins"),onRequestClose:this.closeVisualModal},v.a.createElement("textarea",{id:"cf7sFormData",className:"textareaData___aBaoO",ref:this.ref,onFocus:function(e){e.target.select()},cols:"100",rows:"10",defaultValue:JSON.stringify(this.state.allData)}),v.a.createElement("div",{className:"buttonWrapper___FLNPs"},v.a.createElement(U,{className:"is-primary",onClick:function(){return e.applyVisualData()}},F("Done","contact-form-7-skins")))))}}])}(g.Component));ee.map((function(e,t){var n=e.component,r=e.container,i=Y((function(e){return function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?G(Object(n),!0).forEach((function(t){o()(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):G(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}({},(0,e("cf7svisual").getStates)())}))(n);y.a.render(v.a.createElement(i,null),document.getElementById(r))})),y.a.render(v.a.createElement(te,null),document.getElementById("cf7s-actions"))}]);