msgid ""
msgstr ""
"Project-Id-Version: \n"
"Report-Msgid-Bugs-To: \n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"POT-Creation-Date: 2024-07-10T17:32:50+02:00\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"X-Generator: WP-CLI 2.10.0\n"
"X-Domain: cf7skins-logic\n"

#: logic.js:24729
msgid "Logic Item"
msgstr ""

#: logic.js:24737
msgid "Drag to move"
msgstr ""

#: logic.js:24746
#: logic.js:24893
msgid "Field is missing, please set to other available field, or it will be deleted after save."
msgstr ""

#: logic.js:24747
#: logic.js:24894
msgid "Missing %s"
msgstr ""

#: logic.js:24748
#: logic.js:24897
msgid "Field"
msgstr ""

#: logic.js:24776
msgid "Hide"
msgstr ""

#: logic.js:24777
msgid "Show"
msgstr ""

#: logic.js:24785
msgid "if"
msgstr ""

#: logic.js:24789
msgid "Any"
msgstr ""

#: logic.js:24792
msgid "All"
msgstr ""

#: logic.js:24800
msgid "of the following statements are met"
msgstr ""

#: logic.js:24809
msgid "Select an action"
msgstr ""

#: logic.js:24811
msgid "Add Logic Statement"
msgstr ""

#: logic.js:24817
msgid "Duplicate Logic Item"
msgstr ""

#: logic.js:24823
msgid "Delete Logic Item"
msgstr ""

#: logic.js:24832
msgid "add statement"
msgstr ""

#: logic.js:24841
msgid "duplicate logic"
msgstr ""

#: logic.js:24851
msgid "delete logic"
msgstr ""

#: logic.js:24859
msgid "expand logic"
msgstr ""

#: logic.js:24882
msgid "Logic Statement"
msgstr ""

#: logic.js:24896
msgid "Select criteria"
msgstr ""

#: logic.js:24908
msgid "User"
msgstr ""

#: logic.js:24914
msgid "Post"
msgstr ""

#: logic.js:24940
msgid "duplicate statement"
msgstr ""

#: logic.js:24948
msgid "delete statement"
msgstr ""

#: logic.js:25116
#: logic.js:25204
msgid "Select..."
msgstr ""

#: logic.js:25416
msgid "Fields"
msgstr ""

#: logic.js:25418
msgid "Drag and drop or click this field to add to the Logic editor."
msgstr ""

#: logic.js:25811
msgid "Field %s is no longer existed in treeData, please create a new one or select another field."
msgstr ""

#: logic.js:26012
#: logic.js:26023
msgid "contains"
msgstr ""

#: logic.js:26039
msgid "changed"
msgstr ""

#: logic.js:26040
msgid "contains text"
msgstr ""

#: logic.js:26055
msgid "user logged in"
msgstr ""

#: logic.js:26058
msgid "user role"
msgstr ""

#: logic.js:26064
msgid "post ID"
msgstr ""

#: logic.js:26067
msgid "have meta key"
msgstr ""

#: logic.js:26149
msgid "unchecked"
msgstr ""

#: logic.js:26152
msgid "checked"
msgstr ""

#: logic.js:26160
msgid "is empty"
msgstr ""

#: logic.js:26163
msgid "is not empty"
msgstr ""
