"use strict";

/**
 * CF7Tab class for handling tabs on front.
 *
 * @class CF7Tab
 * @since 1.0.7
 */
class CF7Tab {
	constructor(form) {
		this.form = form;
		this.tabWrapper = form.querySelector(".tab-container");

		this.tabsWrapper = form.querySelector(".epanels");
		this.tabs = form.querySelectorAll(".panel-container");
		this.navsLi = form.querySelectorAll(".etabs > li");
		this.navs = form.querySelectorAll("a"); // with or without wpcf7_autop_or_not
		this.navUl = form.querySelector(".etabs");
		this.formId = form.querySelector(".tab-container").getAttribute("data-form-id");
		this.cf7skinsmulti = CF7Tab.cf7skinsmultiVars(this.formId);

		// events
		this.navs.forEach((nav) => {
			nav.addEventListener("click", this.openTab.bind(this));
		});

		document.body.addEventListener("click", this.doNavigation.bind(this));

		// addons
		this.addNavigation.bind(this)();
		this.addPagination.bind(this)();
		this.addProgressBar.bind(this)();

		// init
		this.init();
	}

	/**
	* Instance init.
	*
	* @since 1.0.7
	*/
	init() {
		let hash = window.location.hash;
		
		if ( hash ) {			
			// If hash is for formID
			if ( parseInt(this.formId) === parseInt( hash.split("-")[1]) ) {				
				let tab = hash.split("-")[2]; // get the last string
				tab = tab === '0' || tab === '' ? 1 : tab; // set to tab 1 1f 0 or empty i.e #part-1826-
				tab = Number.isInteger( +tab ) ? +tab : 1; // set to tab 1 if has not a number
				
				tab = tab <= this.tabs.length ? tab : this.tabs.length; // set to last tab if exceed tab length

				this.move( tab, true );
			} else {
				this.move( 1, false );
			}
		} else {
			this.move( 1, false ); 
		}
	}

	/**
	* Move by hash.
	*/
	moveByHash() {
		let hash = window.location.hash;
		if ( hash ) {
			if (parseInt(this.formId) === parseInt(hash.split("-")[1])) {
				let index = hash.split("-")[2];
				index = Number.isInteger( index ) ? +index + 1 : 1; // convert any string to 1 if the hash is not found
				this.move(index, false);
			}
		}
	}

	/**
	* Show tab by clicking on nav tab.
	*
	* @param {MouseEvent} e
	*/
	openTab( e ) {
		e.preventDefault();
		let index = e.target.getAttribute("data-index");
		this.move( index, true );
	}

	/**
	* Run navigation by clicking on next/prev buttons.
	*
	* @param {MouseEvent} e
	*/
	doNavigation( e ) {
		// Click function to scroll window to top of closest <form /> @since 1.0.7
		if ( ( e.target.classList.contains( "next" ) ||
			e.target.classList.contains( "previous" ) ) &&
			this.formId === e.target.closest( ".tab-container" ).getAttribute( "data-form-id" )
		) {
			e.preventDefault();
			let index = e.target.getAttribute( "data-href" );
			this.move( index, true );
		}
	}

	/**
	* Show tab by index.
	*
	* @param {Number} index
	*/
	createHash(index) {
		return "#part-" + this.formId + "-" + index;
	}
	
	/**
	* Show tab by index.
	*
	* @param {Number} index
	* @param {Boolean} hash
	*/
	move( index, hash = true) {		
		let activeTab = index;

		index = +index - 1;

		this.tabs.forEach(( tab ) => {
			tab.classList.remove( "active" );
		});

		const tabToShow = this.tabs[index];

		tabToShow.classList.add( "active" );

		this.navsLi.forEach( ( li, i ) => {
			li.classList.remove("active"); // remove active class from <li />
			this.navs[i].classList.remove("active"); // remove active class from <a />
		});

		const activeLi = this.navsLi[ index ];
		activeLi.classList.add( "active" );
		this.navs[index].classList.add( "active" );

		if ( hash ) {
			
			// Update hash
			let url = window.location.href;
			url = window.location.href.replace( window.location.hash, "" );
			url += this.createHash( activeTab );
			history.pushState( null, null, url );
			
			// Scrolling effect if activated
			if ( "undefined" !== typeof this.cf7skinsmulti.scroll && this.cf7skinsmulti.scroll ) {
				let formOffset = this.form.offsetTop;
				window.scrollTo({top: formOffset, behavior: "smooth"});
			}				
		}		

		this.runProgressBar();
	}

	/**
	* Get all visible tabs.
	*
	* @return {Array}			array of visible tabs
	*
	* @since 2.0.2
	*/
	visibleTabs() {
		const visible_tabs = [];
		this.navsLi.forEach((item, i) => {
			item.style.display !== "none" && visible_tabs.push(i);
		});

		return visible_tabs;
	}

	/**
	* Parse Multi localize script based on form id.
	*
	* Find all cf7skinsmulti_ global variables on window global.
	* Return cf7skinsmulti_form-id variable if available - contains Multi options.
	*
	* @see http://stackoverflow.com/questions/15597102/get-all-js-variables-that-begin-with-a-certain-string
	*
	* @param {Integer}				form_id		the CF7 form ID
	*
	* @return {Object||Boolean}	cf7skinsmulti_form-id || false - contains Multi options
	*
	* @since 1.0.7
	*/
	static cf7skinsmultiVars(formId) {
		let multi = {};
		let pattern = /^cf7skinsmulti_[0-9]+/;

		// Loop through Multi localize variables
		for ( let varName in window ) {
			if ( pattern.test( varName ) ) {
				let key = varName.split(/[_]+/).pop();
				multi[key] = window[varName];
			}
		}

		// Return cf7skinsmulti_form-id variable if available - contains Multi options.
		if ( typeof multi[formId] !== "undefined") return multi[formId];

		return false;
	}

	/**
	* Add next/previous navigation.
	*
	* @since 1.0.7
	*/
	addNavigation() {
		const visible_tabs = this.visibleTabs(); // @since 2.0.2

		this.tabs.forEach((item, i) => {
			const tabLength = this.tabs.length; //total tabs
			// Default Previous & Next buttons text - helps with backward compatibility

			let use_prev_tab = this.cf7skinsmulti.previous;
			let use_next_tab = this.cf7skinsmulti.next;

			// Get Multi options set for this form
			const show_navlinks =
			"undefined" !== typeof this.cf7skinsmulti.show_navlinks &&
			this.cf7skinsmulti.show_navlinks;
			const use_start_tab =
			"undefined" !== typeof this.cf7skinsmulti.use_start_tab &&
			this.cf7skinsmulti.use_start_tab;
			const use_end_tab =
			"undefined" !== typeof this.cf7skinsmulti.use_end_tab &&
			this.cf7skinsmulti.use_end_tab;

			// for old forms _this.cf7skinsmulti.use_prev_tab is undefined
			// for new forms where use_prev_tab value is not added the value is '' - so use defaults
			if (
				"undefined" !== typeof this.cf7skinsmulti.use_prev_tab &&
				"" !== this.cf7skinsmulti.use_prev_tab
			) {
				use_prev_tab = "\u00AB " + this.cf7skinsmulti.use_prev_tab;
			}

			if (
				"undefined" !== typeof this.cf7skinsmulti.use_next_tab &&
				"" !== this.cf7skinsmulti.use_next_tab
			) {
				use_next_tab = this.cf7skinsmulti.use_next_tab + " \u00BB";
			}

			const thanks =
			"undefined" !== typeof this.cf7skinsmulti.thanks &&
			this.cf7skinsmulti.use_end_tab;

			// Append a container for the links
			if (visible_tabs.length > 1) {
				if (item.querySelector(".navigation")) {
					item.querySelector(".navigation").remove();
				} // remove previous added navigation

				let navigation = document.createElement("div");
				navigation.className = "navigation";
				item.append(navigation);
			}

			// Get current, prev and next index position in visible_tabs
			let pos = visible_tabs.indexOf(i);
			let prev = visible_tabs[pos - 1];
			let next = visible_tabs[pos + 1];
			
			// Set prev to first and next to last if undefined
			prev = ! prev ? 0 : prev;
			next = ! next ? pos : next;

			// Update tab number starts by 1, JavaScripts starts by 0
			pos = pos + 1;
			prev = prev + 1;
			next = next + 1;

			// Append the next/previous links
			if (show_navlinks && pos > 0) {
				// Add previous button, but not at the first tab
				if ( pos !== 1 ) {
					let linkElement = document.createElement("a");
					linkElement.setAttribute("href", this.createHash( prev ) );
					linkElement.setAttribute("data-href", `${prev}`);
					linkElement.className = "previous";
					linkElement.textContent = use_prev_tab;
					item.querySelector(".navigation").append(linkElement);
				}

				// Add next button, but not at the last tab
				if ( pos !== tabLength ) {
					let linkElement = document.createElement("a");
					linkElement.setAttribute("href", this.createHash( next ) );
					linkElement.setAttribute("data-href", `${next}`);
					linkElement.className = "next";
					linkElement.textContent = use_next_tab;
					item.querySelector(".navigation").append(linkElement);
				}
			}

			// Append the start link
			if ( use_start_tab && i === 0 && pos > 0 ) {
				let linkElement = document.createElement("a");
				linkElement.setAttribute("href", this.createHash( next ) );
				linkElement.setAttribute("data-href", `${next}`);
				linkElement.className = "start";
				linkElement.textContent = use_start_tab;

				item.querySelector(".navigation").prepend(linkElement);
				linkElement.addEventListener("click", (e) => {
					let index = e.target.getAttribute("data-href");
					this.move(index, true);
				});
			}

			// Append the end link
			if (use_end_tab && i + 1 === tabLength) {
				let endElement = document.createElement("input");
				endElement.value = use_end_tab;
				endElement.type = "submit";
				endElement.className = "end";

				item.querySelector(".navigation").append(endElement);
			}
		});
	}

	/**
	* Add pagination to the form.
	*
	* @since 1.0.7
	*/
	addPagination() {
		const visible_tabs = this.visibleTabs(); // @since 2.0.2

		let tab = 1; // @since 2.0.2

		this.tabs.forEach((item, i) => {
			if (
				"undefined" === this.cf7skinsmulti.show_pagination ||
				!this.cf7skinsmulti.show_pagination
			) {
				return;
			}

			// Append a container for the pagination
			if ( this.cf7skinsmulti.show_pagination ) {
				if (item.querySelector(".pagination")) {
					item.querySelector(".pagination").remove(); // remove previous added pagination
				}
				let pagination = document.createElement("div");
				pagination.className = "pagination";
				item.append(pagination);
			}

			let pos = visible_tabs.indexOf(i); // current index position in visible_tabs

			// Append the pagination
			if ( this.cf7skinsmulti.show_pagination && pos > -1 ) {
				let paginationElement = document.createElement("span");
				paginationElement.innerHTML = `<strong>${tab}</strong>/${visible_tabs.length}`;

				item.querySelector(".pagination").append(paginationElement);

				tab++; // increase tab counter
			}
		});
	}

	/**
	* Add Progress bar to the form.
	*
	* @since 1.0.7
	*/
	addProgressBar() {
		const show_progressbar = "undefined" !== typeof this.cf7skinsmulti.show_progressbar &&
			this.cf7skinsmulti.show_progressbar;

		if (show_progressbar) {
			if (this.form.querySelector(".cf7m-progress")) {
				this.form.querySelector(".cf7m-progress").remove();
			}

			let progressElement = document.createElement("div");
			progressElement.className = "cf7m-progress";
			progressElement.innerHTML = "<div></div>";
			this.form.prepend(progressElement);
		}
	}

	/**
	* Run Progress bar to the form.
	*
	* @since 1.0.7
	*/
	runProgressBar() {
		const visible_tabs = this.visibleTabs();
		const show_progressbar =
			"undefined" !== typeof this.cf7skinsmulti.show_progressbar &&
			this.cf7skinsmulti.show_progressbar;

		// Bail if progress bar disabled
		if ( !show_progressbar ) return;

		const parts_length = visible_tabs.length;
		const part_index = Array.from(this.tabs).findIndex((tab) => {
			return (
				window.getComputedStyle(tab).getPropertyValue("visibility") !== "hidden"
			);
		}); // current active tab index

		const pos = visible_tabs.indexOf(part_index); // current index position in visible_tabs

		const progress_outer = this.form.querySelector(".cf7m-progress");
		const progress_inner = progress_outer.querySelector("div");
		let progress;

		// Set width now
		progress = (100 / parts_length) * (pos + 1); // visible_tabs starts by 0

		progress_inner.style.width = `${progress}%`;
	}
}

/**
 * All tabs on the page.
 */
const cf7multiTabs = [];

/**
 * Add tabs to cf7multiTabs by looking for .tab-container selector.
 */
document.querySelectorAll(".cf7multi").forEach((form) => {
	const formId = form.querySelector(".tab-container").getAttribute("data-form-id");
	cf7multiTabs.push({id: formId, form: new CF7Tab(form)});
});

/**
 * Custom callback action for CF7 Skins Logic.
 *
 * @param {Boolean}	bool		statement is fit (true)
 * @param {Array}	statement	current logic statement: action, comparator, criteria, field, form_id, if, item, num, type, value1
 *
 * @since 1.0.7
 */
function cf7skinsmulti_logic_action(bool, statement) {
	if ("multi-tabs" != statement.item) return;

	let _text;
	let _tabs = [];

	const form = document
	.querySelector(`[data-form-id="${statement.form_id}"]`)
	.closest("form");

	// Get all tabs and panels
	const tabNav = form.querySelector("ul.etabs");
	const mtabs = tabNav.querySelectorAll("li");
	const mpanels = form.querySelectorAll(".panel-container");
	const visibleTabs = () => {
		const visible_tabs = [];
		
		mtabs.forEach((item, i) => {
			item.style.display !== "none" && visible_tabs.push(i);
		});

		return visible_tabs;
	};

	// Detect any change in form and redraw navigation, pagination and progress bar
	// @since 2.0.2
	let visible_tabs = visibleTabs().length;
	
	// Get tabs list from localize script @since 2.6.1
	var cf7skinsmulti = CF7Tab.cf7skinsmultiVars( statement.form_id );	
	let tabs = cf7skinsmulti.hasOwnProperty( 'tabs' ) ? cf7skinsmulti.tabs : {};	

	// Loop for each tabs
	mtabs.forEach((tab, i) => {
		_tabs.push(i);
		_text = tab.querySelector("a").textContent;

		// Statement field is the tab text for jQuery version or field cf7Name for Visual version
		if ( statement.field == _text || statement.field == tabs[ i ].cf7Name ) {
			if (bool) {
				if ("show" == statement.action) {
					tab.removeAttribute("style");
				} else {
					tab.classList.remove("active");
					tab.style.display = "none";
				}
			} else {
				if ("show" == statement.action) {
					tab.classList.remove("active");
					tab.style.display = "none";
				} else {
					tab.removeAttribute("style");
				}
			}

			let current_visible_tabs = visibleTabs().length;

			if (visible_tabs !== current_visible_tabs) {
				// only update when total tabs changed
				const foundForm = cf7multiTabs.find(
					(tab) => tab.id === statement.form_id
				).form;

				foundForm.addNavigation();
				foundForm.addPagination();
				foundForm.addProgressBar();
				foundForm.runProgressBar();

				visible_tabs = current_visible_tabs; // update visible tabs for further check
			}
		}
	});
}

/**
 * Handles showing the thank you tab.
 *
 * Called in cf7skins-multi\includes\front.php
 *
 * NRM ?? - As this hooks to CF7 on_sent_ok this will no longer work
 *
 * @since 1.0.7
 */
function cf7skinsmulti_on_sent_ok_cb(form_id) {
	const foundForm = cf7multiTabs.find((tab) => tab.id === form_id).form;
	const cf7skinsmulti = foundForm.cf7skinsmultiVars(form_id);

	if ( "undefined" === cf7skinsmulti.thanks || !cf7skinsmulti.thanks ) {
		return;
	}

	// Hide tabs
	foundForm.navUl.style.display = "none";

	// Hide panels
	foundForm.tabs.style.display = "none";

	// Say thanks
	const thanksHtml = document.createElement("div");
	thanksHtml.className = "panel-container active thanks";
	thanksHtml.innerHTML = cf7skinsmulti.thanks;
	foundForm.tabsWrapper.classList.add("thanks");
	foundForm.append( thanksHtml );
}

/**
 * Handles showing the thank you tab.
 *
 * wpcf7mailsent event on send form.
 *
 * @since 1.0.7
 */
document.addEventListener(
	"wpcf7mailsent",
	function (event) {
		cf7multiTabs.forEach( ( tab ) => {

			// Current form exists
			if ( event.detail.contactFormId !== parseInt( tab.id ) ) {
				return;
			}

			// Get Multi settings
			const cf7skinsmulti = tab.form.cf7skinsmulti;

			// Only if Thank You Tab Text added
			if ( "undefined" === cf7skinsmulti.thanks || !cf7skinsmulti.thanks ) {
				return;
			}

			// Hide tabs
			tab.form.navUl.style.display = "none";

			// Hide panels
			tab.form.tabs.forEach((t) => {
				t.style.display = "none";
			});

			// Say thanks
			const thanksHtml = document.createElement("div");
			thanksHtml.className = "panel-container active thanks";
			thanksHtml.innerHTML = cf7skinsmulti.thanks;
			tab.form.tabsWrapper.classList.add("thanks");
			tab.form.tabsWrapper.append(thanksHtml);
		});
	},
	false
);
