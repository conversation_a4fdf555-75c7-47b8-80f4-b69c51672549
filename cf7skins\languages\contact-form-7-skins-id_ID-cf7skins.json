{"domain": "messages", "locale_data": {"messages": {"": {"domain": "messages", "plural_forms": "nplurals=2; plural=n != 1;", "lang": "id"}, "Acceptance (confirm)": ["<PERSON><PERSON><PERSON><PERSON> (konfirmasi)"], "Make this checkbox checked by default": ["Buat kotak centangan ini di centang secara bawaan"], "Make this work inversely": ["Buat ini bekerja secara terbalik"], "Make this checkbox optional": ["Buat kotak centangan ini dicentang secara bawaan"], "Checkbox (option)": ["Kotak centang (pilihan)"], "Put a label first, a checkbox last": ["Buat label pertama, kotak centangan terakhir"], "Wrap each item with label element": ["Bungkus setiap item dengan elemen label"], "Make checkboxes exclusive": ["Buat kotak centang ekslusif"], "Date": ["Tanggal"], "Default value": ["<PERSON><PERSON> b<PERSON>an"], "Use this text as the placeholder of the field": ["Gunakan teks ini sebagai wadah isian ruas"], "Range - min": ["Ren<PERSON><PERSON> - min"], "Range - max": ["Rentang - max"], "Email": ["<PERSON><PERSON>"], "Akismet - this field requires author's email address": ["Akismet - ruas ini membutuhkan alamat surel pengarang"], "File (upload)": ["<PERSON><PERSON><PERSON> (upload)"], "File size limit (bytes)": ["Batas ukuran berkas"], "Acceptable file types": ["<PERSON><PERSON><PERSON> berka<PERSON>"], "Number": ["Nomor"], "Quiz": ["<PERSON><PERSON>"], "Question 1": ["Pertanyaan 1"], "Answer 1": ["Jawaban 1"], "Question 2": ["Pertanyaan 2"], "Answer 2": ["Jawaban 2"], "Radio Button (option)": ["Tombol Radio (pilih)"], "Option 1": ["Pilihan 1"], "Option 2": ["Pilihan 2"], "Select (dropdown)": ["<PERSON><PERSON><PERSON> (<PERSON>rik turun)"], "Allow multiple selections": ["Ijinkan banyak pilihan"], "Insert a blank item as the first option": ["Masukkan item kosong sebagai pilihan pertama"], "Submit": ["Aju<PERSON>"], "Telephone": ["Telepon"], "Text (short text)": ["<PERSON><PERSON> ( singkat )"], "Textarea (long text)": ["Area teks (panjang)"], "URL (website link)": ["URL (tautan situs)"], "Fieldset (with legend)": ["<PERSON><PERSON> (dengan legend)"], "Legend ..": ["Legend .."], "List - ol": ["List - ol"], "List Item - li": ["List Item - li"], "Paragraph - p": ["Paragrap - p"], "Only one reCAPTCHA per form allowed.": ["<PERSON><PERSON> satu reCAPTCHA per formulir diijinkan."], "Only one submit for each form.": ["<PERSON>ya satu tombol ajukan yang diijinkan setiap formulir."], "Root node can not be dragged to other node.": [""], "Only one %s allowed in a form.": ["Hanya satu %s yang diijinkan di sebuah formulir."], "Node has recaptcha children. Only one recaptcha allowed in a form.": ["<PERSON><PERSON><PERSON> memiliki reCAPTCHA sebagai anakan. <PERSON><PERSON> satu reCAPTCHA diijinkan untuk sebuah formulir."], "Node has submit children. Only one submit allowed in a form.": ["<PERSON><PERSON><PERSON> memiliki tombol ajukan sebagai anakan. <PERSON><PERSON> satu tombol yang diijinkan untuk sebuah formulir."], "Legend": ["Lagenda"], "Your Name (required)": ["<PERSON><PERSON> (dibutuhkan)"], "Email Address (required)": ["<PERSON><PERSON><PERSON> (dibutuhkan)"], "Your Message": ["<PERSON><PERSON>"], "* Required": ["* Dibutuhkan"], "Send": ["<PERSON><PERSON>"], "Can not save! window.cf7svisual or post ID does not exist.": ["Tidak bias menyimpan! window.cf7svisual atau nomor pos tidak ada."], "Dismiss this notice.": ["Tutup pemberitahuan ini."], "Visual saved!": ["Visual disimpan!"], "Namespace %s is undefined!": ["Namespace %s belum ditentukan!"], "Function %1$s.%2$s is undefined!": ["Fungsi %1$s.%2$s belum ditentukan!"], "Function %s is undefined!": ["Function %s belum ditentukan!"], "Save Visual": ["Simpan Visual"], "Field Type": ["Tipe Field"], "Spinbox": ["Kotak Geser"], "Slider": ["<PERSON><PERSON><PERSON><PERSON>"], "Required": ["<PERSON><PERSON><PERSON><PERSON>"], "Name": ["<PERSON><PERSON>"], "Condition": ["<PERSON><PERSON><PERSON>"], "Label": ["Label"], "Default Value": ["<PERSON><PERSON>"], "This field requires author's name": ["Ruas ini membutuhkan nama pengarang"], "This field requires author's email address": ["Ruas ini membutuhkan alamat surel pengarang"], "This field requires author's URL": ["Ruas ini membutuhkan URL pengarang"], "Range": ["Rentang"], "Min": ["Min"], "Max": ["Max"], "Content": ["Konten"], "Allowed tags:": ["Tanda-tanda yang diijinkan:"], "Options": ["<PERSON><PERSON><PERSON>"], "Default": ["<PERSON><PERSON><PERSON>"], "Answer": ["J<PERSON>ban"], "Add Other as last option": ["Tambahkan Lai<PERSON>a sebagai pilihan terakhir"], "Other": ["<PERSON><PERSON><PERSON>"], "Size": ["Ukuran"], "Normal": ["Normal"], "Compact": ["Pa<PERSON>t"], "Theme": ["<PERSON>a"], "Light": ["Terang"], "Dark": ["<PERSON><PERSON><PERSON>"], "Id Attribute": ["Atribut Id"], "Class Attribute": ["Atribut Kelas"], "Save": ["Simpan"], "Done": ["Se<PERSON><PERSON>"], "Drag/Move": ["Geser/Pindah"], "Collapse": ["<PERSON><PERSON><PERSON><PERSON>"], "Expand": ["<PERSON><PERSON>"], "edit": ["ubah"], "duplicate": ["gandakan"], "delete": ["hapus"], "Add-on options": ["<PERSON><PERSON><PERSON> add-on"], "Dismiss this notice": ["<PERSON><PERSON><PERSON><PERSON> per<PERSON>tan ini"], "Forms are easier to follow along with when questions are on separate pages.": [""], "CF7 Skins Multi makes separate pages possible.": [""], "Your form is less likely to be completed if it’s too long. Separate it using": [""], "CF7 Skins Multi.": [""], "Progress bars make long forms feel less daunting. Add this in using ": [""], "Emphasize privacy policy acceptance by putting them on a second page.": [""], "CF7 Skins Multi": [""], " makes this easy to do.": [""], "Your form is more likely to be completed if it isn’t cluttered. Separate information onto different pages using": [""], "Put name and email address fields beside each other easily using CF7 Skins Ready.": [""], "Read more about CF7 Skins Ready.": [""], "Don’t forget that your form fields need to be mobile optimized, especially if fields are beside each other.": [""], "CF7 Skins Ready handles this for you.": [""], "Make your form easier to read by grouping together similar fields.": [""], "CF7 Skins Ready makes this easy to do.": [""], "Visually grouping similar fields together improves the flow of your form. A good way to do this is by drawing a box around the group using": [""], "CF7 Skins Ready.": [""], "Separate cluttered form content into 2 columns or more using": [""], "Looking to arrange your form fields into 2 or more columns?": [""], "CF7 Skins Ready ": [""], "makes this easy to do.": [""], "Increase the chances of your form being completed by aligning the fields. Use the grid structure in": [""], "CF7 Skins Ready": [""], "to get a head start.": [""], "More complex form layouts should follow a grid system.": [""], "You can align fields, put them side-by-side, and more with our easy-to-use": [""], "CF7 Skins Ready Add-on.": [""], "Have a yes/no radio button field? Display them horizontally on the same line using": [""], "Very good, good, neutral? For a survey form, put checkboxes or radio buttons on the same horizontal line.": [""], "It is more professional looking to have a form style that matches your site.": [""], "Find a better fit in our CF7 Skins Pro Styles.": [""], "Having more templates means you save time creating forms from scratch.": [""], "Get more templates to choose from.": [""], "For custom style options, you can use CSS to adjust the form’s style.": [""], "To get help, our premium email support team is available.": [""], "Ensure error-free forms by using templates and the visual editor.": [""], "Get more templates with the CF7 Skins Pro Add-on.": [""], "Show specific fields to customers based on their responses with": [""], "CF7 Skins Logic.": [""], "You may not need to show all of the possible fields to all of your customers.": [""], "Keep forms short & relevant with CF7 Skins Logic.": [""], "You can dynamically show and hide form fields using": [""], "Dynamic form fields are possibly when you add logic to the fields.": [""], "CF7 Skins Logic makes dynamic forms possible.": [""], "Feeling frustrated? Get 1-1 personalized, real-human responses to your tough questions with any of our": [""], "CF7 Skins Add-ons.": [""], "Make sure your form is set up correctly the first time by talking to our trained support staff, available with our": [""], "CF7 Skins Pro Add-on.": [""], "The WordPress support community is available to answer questions. Or, save time searching & ask us directly with our ": [""], "Premium Email support.": [""], "Double check your mail settings to avoid problems sending & receiving forms. Set it up correctly from the start with our": [""], "Tip": [""], "FIELDS (CF7 TAGS)": ["RUAS (TAG CF7)"], "Drag and drop or click a Contact Form 7 Tag to add it to the visual form editor.": ["Seret dan lepas atau klik Tag Contact Form 7 untuk menambahkannya ke editor formulir visual."], "To see how Fields are used, select a template it's a great way to get started quickly.": ["Untuk melihat bagaimana Fields digunakan, pilih templat itu cara yang bagus untuk memulai dengan cepat."], "Any field content can be changed by clicking Edit on the field.": ["Konten field apa pun dapat diubah dengan mengeklik Edit pada field tersebut."], "FIELDS (CF7 SKINS ITEMS)": ["RUAS (ITEM CF7 SKINS)"], "Use CF7 Skins Fields for the layout of your form.": ["Gunakan CF7 Skins Fields untuk tata letak formulir <PERSON>."], "Fieldsets, legends, and lists are used to group related fields, and allow for more detailed styling.": ["<PERSON><PERSON>, legend dan list digunakan untuk mengelompokkan ruas yang be<PERSON>, dan <PERSON><PERSON>n penggaya yang terinci."], "Form Options": ["<PERSON><PERSON>"], "Configure this forms options": ["Konfigurasikan opsi formulir ini"], "Visual Data": ["Data Visual"], "Copy & paste visual form data": ["Salin & tempel data formulir visual"], "Error, the result is not an object or null.": ["<PERSON><PERSON>, has<PERSON><PERSON> bukan merupakan sebuah object atau null."], "Error: treeData, form or callbacks property is not available.": ["Salah: property treeData, form ataupun callbacks tidak tersedia."], "Error, templateTree is not an array!": ["<PERSON><PERSON>, template<PERSON>ree bukanlah sebuah array!"], "Select a template that closely matches your needs then switch to the CF7 Skins Form  tab to add, duplicate or remove fields to match your requirements. Any field content can be changed by clicking Edit on the field.": [""], "Templates are pre-created forms that automatically create the form’s structure and content for you. Each template works as an easy to follow guide.": [""], "Save Form": ["<PERSON><PERSON><PERSON>"], "Copy & paste visual data": ["Salin & tempel data visual"], "Unable to add the item.": ["Tidak bias menambah item itu."], "There is no edit field available for this item.": ["Tidak ada pengubahan yang tersedia untuk item ini."], "Unable to duplicate!": ["Tidak bisa menggandakan!"], "Unable to change the item.": ["Tidak dapat mengubah item ini."], "Visual Object Data is not valid ( JSON parse error )": ["Data Obyek Visual tidak sah ( kesalahan penguraian JSON )"], "Visual data is empty.": ["Data Visual tidak ada."], "Visual data is not valid.": ["Data Visual tidak sah."]}}}