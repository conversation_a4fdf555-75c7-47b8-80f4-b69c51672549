const { Button } = wp.components;
const { __ } = wp.i18n;
const { useSelect } = wp.data;
const { applyFilters, } = wp.hooks;

const { chunk, } = lodash;
import { Droppable, Draggable } from 'react-beautiful-dnd';
import classNames from 'classnames';

import styles from './index.css';

const Sidebar = ( props ) => {

	const { fieldOptions, customField, fields, handleOnClick  } = props;

	const { logicData } = useSelect( ( select ) => {
		const visualState = select( 'cf7svisual' ).getStates();

		return {
			logicData: visualState.logic,
		}
	});	

	// Return index position from fieldOptions 
	const findDraggableIndex = (name) => {
		return fieldOptions.findIndex( field => field.name === name);
	}

	const renderCustomFields = ( fields ) => {
		return fields.map( ( field, i ) =>
			<Draggable
				key={ field.name }
				draggableId={ field.name }
				index={ i + fieldOptions.length }
				isDragDisabled={ typeof logicData[ field.name ] !== 'undefined' }
			>
				{ ( provided, snapshot ) =>
					<div
						{ ...provided.draggableProps }
						{ ...provided.dragHandleProps }
						ref={ provided.innerRef }
						className={ classNames(
							styles.logicListField,
							styles.logicListFieldFlex,
							{
								[ styles.disabledLi ]: typeof logicData[ field.name ] !== 'undefined',
								[ styles.borderDashed ]: snapshot.isDragging,
							}
						) }
						onClick={ () => typeof logicData[ field.name ] === 'undefined' && handleOnClick( field.name ) }
					>
						<span className={ 'dashicons-before dashicons-category' }></span>

						<span>
							<span className={ styles.fieldLabel } title={ field.label }>{ field.label }</span>
							<span className={ styles.fieldName } title={ field.name }>{ field.name }</span>
						</span>
					</div>
				}
			</Draggable>
		)
	}

	const sidebarFields = applyFilters( 'cf7slogic.sidebar', [] );
	
	const renderCustomSidebar = () => {
		return sidebarFields.map( ( sidebar, i ) =>
			<>
				<li className={ classNames(styles.logicHeaderField) }>
					{ sidebar.header }

					<span 
						className={ styles.logicFieldHelp }
						data-balloon={ sidebar.help }
						data-balloon-pos="left" 
						data-balloon-length="large"
					>
						?
					</span>
				</li>

				<div className={ styles.logicFieldScrollable }>
					<div className={ styles.logicFieldWrapper }>
						{ renderCustomFields( sidebar.fields ) }
					</div>
				</div>
			</>
		)
	}

	return (
		<div className={ styles.Sidebar }>
			<Droppable droppableId="sidebar" isDropDisabled={ true }>
				{ ( provided, snapshot ) => {
					return (
						<ul
							{ ...provided.droppableProps }
							ref={ provided.innerRef }
							style={ { margin: 0 } }
						>
							<li className={ styles.logicHeaderField }>
								{ __( 'Fields', 'cf7skins-logic' ) }

								<span 
									className={ styles.logicFieldHelp }
									data-balloon={ __( 'Drag and drop or click this field to add to the Logic editor.', 'cf7skins-logic' ) } 
									data-balloon-pos="left" 
									data-balloon-length="large"
								>
									?
								</span>
							</li>
							<div className={ styles.logicFieldScrollable }>
								{ chunk( fieldOptions, 2 ).map(
									( fieldChunk ) => (
										<div className={ styles.logicFieldChunk }>
											{ fieldChunk.map( ( field, i ) => (												
												<Draggable
													key={ field.name }
													draggableId={ field.name }
													index={ findDraggableIndex( field.name ) }
													isDragDisabled={ typeof logicData[ field.name ] !== 'undefined' }
												>
													{ ( provided, snapshot ) => {
														return (
															<li
																{ ...provided.draggableProps }
																{ ...provided.dragHandleProps }
																ref={ provided.innerRef }
																className={ classNames(
																	'logic-field-item',
																	styles.logicListField,
																	{
																		[ styles.disabledLi ]: typeof logicData[ field.name ] !== 'undefined',
																		[ styles.borderDashed ]: snapshot.isDragging,
																	}
																) }
																onClick={ () => typeof logicData[ field.name ] === 'undefined' && handleOnClick( field.name ) }
															>
																<div className="logic-field-item__top">
																	<span
																		className={
																			field.icon.includes('typcn') ? field.icon : `dashicons-before dashicons-${field.icon}`
																		}
																	>

																	</span>

																	<div>
																		<span className="logic-field-label">
																			{ field.label !== "" ? field.label : field.selectLabel }
																		</span>
																		<span>
																			{ field.name }
																		</span>
																	</div>
																</div>

																{ applyFilters( 'cf7slogic.sidebar.field.footer', [], field ) }
															</li>
														);
													} }
												</Draggable>
											) ) }
										</div>
									)
								) }
							</div>

							{ renderCustomSidebar() }

							{ provided.placeholder }
						</ul>
					);
				} }
			</Droppable>
		</div>
	);
};

export default Sidebar;
