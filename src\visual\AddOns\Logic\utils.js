import {
	isPlainObject ,
	isEqual , 
} from 'lodash';

const { __ } = wp.i18n;

export const maybeConvertLogic = ( data ) => {
	const updatedLogicData = Object.keys( data ).reduce( ( obj, field ) => {
		obj[ field ] = data[ field ];
		if ( isPlainObject( obj[ field ].statements ) ) {
			obj[ field ].statements = Object.values(
				obj[ field ].statements
			);
		}

		return obj;
	}, {} );

	return updatedLogicData;
}; 

export const searchTree = (fn) => (obj) =>
	Array.isArray(obj)
		? obj.length == 0
			? null
			: searchTree(fn)(obj[0]) || searchTree(fn)(obj.slice(1))
		: fn(
			obj,
			() => searchTree(fn)(obj.children || []),
			() => obj
		);

// Setup new logic data with its one empty statement
export const defaultLogicData = ( field ) => {
	return {
		[ field.name ]: {
			action: "show",
			if: "any",

			// Use to convert jQuery to Visual
			label: field.hasOwnProperty( 'label' ) ? field.label : "",

			item: field.hasOwnProperty( 'item' ) ? field.item : "fields",
			statements: [ {
				criteria: "",
				comparator: "",
				value1: "",
				type: "",
			}, ],
		},
	};
};

//comparators
export const getComparators = ( fieldName, type, fields ) => {
	let fieldType;

	if ( type === "field" ) {
		fieldType = fieldName.match(/[^-]*/)[0];
	} else if ( type === "user" || type === "post" ) {
		fieldType = fieldName;
	}

	switch ( fieldType ) {
		case "user_role":
		case "post_id":
		case "select":
			const findCf7Object = ( target, obj ) =>
				searchTree( ( node, next, found ) => {
					if ( ! node ) {
						return;
					}
					
					return node.cf7Name == target ? found() : next();
				})( fields );

			const foundField = findCf7Object( fieldName, fields );

			if ( foundField && foundField.hasOwnProperty( 'cf7Multiple' ) && foundField.cf7Multiple ) {
				return { "=": "=", "!=": "!=", contains:__( 'contains', 'cf7skins-logic' ) };
			}

			return { "=": "=", "!=": "!=" };

		case "checkbox":
			return { "=": "=", "!=": "!=", contains: __( 'contains', 'cf7skins-logic' ) };
			// case "file":
			//   return {"is empty": 0, "is not empty": 1};
			// case "acceptance":
			//   return {unchecked: 0, checked: 1};

		case "text":
		default:
			return {
				"=": "=",
				"!=": "!=",
				">": ">",
				">=": ">=",
				"<=": "<=",
				"<": "<",
				changed: __( 'changed', 'cf7skins-logic' ),
				contains: __( 'contains text', 'cf7skins-logic' ),
			};
	}
};

export const getCriteriaTypeField = ( fieldName ) => {
	if ( [ "user_logged_in", "user_role" ].includes( fieldName ) ) {
		return "user";
	}

	if ( [ "post_id", "have_meta_key" ].includes( fieldName ) ) {
		return "post";
	}

	return "field";
};

export const getUserCriteriaOptions = () => {
	return [
		{ label: __( 'user logged in', 'cf7skins-logic' ), value: "user_logged_in" },
		{ label: __( 'user role', 'cf7skins-logic' ), value: "user_role" },
	];
};

export const getPostCriteriaOptions = () => {
	return [
		{ label: __( 'post ID', 'cf7skins-logic' ), value: "post_id" },
		{ label: __( 'have meta key', 'cf7skins-logic' ), value: "have_meta_key" },
	];
};

export const getCriteriaValue = ( fieldName, fields, typeField ) => {
   //user
	if ( typeField === "user" ) {
		if ( fieldName === "user_logged_in" ) {
			return {
				type: "user_logged_in",
				values: [
					{ label: "false", value: 0 },
					{ label: "true", value: 1 },
				],
			};
		} else if ( fieldName === "user_role" ) {
			return {
				type: "user_role",
				values: Object.entries( CF7LOGIC_DATA.user_roles ).map( ( [ key, name ] ) => ({
					label: name,
					value: key,
				})),
			};
		}
	}

	// post
	if ( typeField === "post" ) {
		if ( fieldName === "post_id" ) {
			return { type: "post_id", values: "" };
		} else if ( fieldName === "have_meta_key" ) {
			return {
				type: "have_meta_key",
				values: CF7LOGIC_DATA.meta_keys.map( k => ({ label: k, value: k }) ),
			};
		}
	}
  
	// field
	const findCf7Object = (target, obj) =>
		searchTree( ( node, next, found ) => {
		if ( ! node ) {
			return;
		}
		return node.cf7Name == target ? found() : next();
	})( fields );
	
	const foundField = findCf7Object( fieldName, fields );

	if ( foundField ) {
		switch ( foundField.cf7sType ) {
			case "checkbox":
			case "radio":
				return { 
					type: foundField.cf7sType, 
					values: foundField.cf7Options
				};

			case "select":
				return {
					type: foundField.cf7sType,
					multiple: foundField.cf7Multiple,
					values: foundField.cf7Options,
				};

			case "acceptance":
				return {
					type: foundField.cf7sType,
					values: [
						{ label: __( 'unchecked', 'cf7skins-logic' ), value: 0 },
						{ label: __( 'checked', 'cf7skins-logic' ), value: 1 },
					],
				};

			case "file":
				return {
					type: foundField.cf7sType,
					values: [
						{ label: __( 'is empty', 'cf7skins-logic' ), value: 0 },
						{ label: __( 'is not empty', 'cf7skins-logic' ), value: 1 },
					],
				};

			case "text":
			default:
				return {
					type: foundField.cf7sType, 
					values: foundField.cf7Values
				};
		}
	}
};

export const cf7AllowedTags = () => {
	return [
		"checkbox",
		"quiz",
		"text",
		"email",
		"textarea",
		"select",
		"radio",
		"acceptance",
		"date",
		"tel",
		"url",
		"file",
		"number",
	];
};

export const getFieldTypeByName = ( fieldName, treeData ) => {
	const fields = window.cf7svisual.treeData || treeData;

	let fieldType = ''; // store field type

	const walkNode = ( node, parent = null ) => {

		if ( node.cf7Name === fieldName ) {
			fieldType = node.cf7sType;
			return; // exit loop
		}

		if ( !! node.children ) { 
			for ( let children of node.children ) {
				walkNode( children, node ); // recursive, walk children and pass the parent node
			};
		};
	};

	for ( let i = 0; i < treeData.length; ++i ) { // loop for each registered addon
		walkNode( treeData[ i ] );
	};

	return !! fieldType && fieldType;
};

export const getAllowedTagsComparator = ( fieldName, fields ) => {
	const fieldType = getFieldTypeByName( fieldName, fields );

	return fieldType === "file" || fieldType === "acceptance" ||
		fieldName === "user_logged_in" || fieldName === "have_meta_key"
		? false
			: true;
};

export const convertComparator = ( comparator ) => {
	// For acceptance there is no comparator
	return comparator ? comparator.replace( "&gt;", ">" ).replace( "&lt;", "<" ) : '';
};

export const covertLogicData = ( data, treeData ) => {
	const allFields = [];
};
