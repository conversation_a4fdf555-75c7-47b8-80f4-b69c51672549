<?php
/**
 * CF7 Skins Multi Front End Class.
 * 
 * @package		cf7skins
 * @subpackage	cf7skins-multi
 * <AUTHOR>
 * @since		0.1.0
 */

Class CF7_Skins_Multi_Front {

	var $form_id; 	 // CF7 form ID
	var $form;		 // CF7 form content
	var $part_regex; // Multi regex

	/**
	 * Sets properties and hooks.
	 * 
	 * @since 0.1.0
	 */
	function __construct() {
		$this->part_regex = '|<part\s+name\s*=\s*"([^"]*)"\s*\/>|i';
		add_action( 'wpcf7_contact_form', array( $this, 'tabify' ), 10, 1 );
		add_action( 'cf7skins_enqueue_styles', array( $this, 'enqueue_styles' ), 7, 1 ); // add styles after the CF7 Skins CSS framework
		add_filter( 'wpcf7_contact_form_properties', array( $this, 'set_form_id' ), 10, 2 );
		add_filter( 'cf7skins_form_classes', array( $this, 'multi_class' ) );
		add_filter( 'wpcf7_form_class_attr', array( $this, 'thanks_class' ), 10, 1 );
		add_filter( 'cf7s_logic_scripts', array( $this, 'logic_scripts' ), 10, 2 ); // @since 1.0.7
	}
	

	/**
	 * Add Multi class to the CF7 Skins form.
	 * 
	 * Only if style is selected AND have more than one multiple part.
	 * 
	 * @param $classes (string) cf7skins main class
	 * 
	 * @since 0.1.0
	 */
	function multi_class( $classes ) {
		
		if( $this->multi_enabled( $this->form ) ) // return if only one part was found
			return $classes . ' cf7multi';
		
		return $classes;
	}
	

	/**
	 * Add additional Multi classes to the Contact Form 7 form.
	 * 
	 * @param $classes (string) cf7skins main class
	 * 
	 * @since 0.0.1
	 */
	function thanks_class( $classes ) {

		$meta = get_post_meta ( $this->form_id, CF7SKINSMULTI_OPTIONS, true );

		// Add the cf7skinsmulti-thanks class
		if ( isset( $meta['thanks'] ) && ! empty( $meta['thanks'] ) )
			$classes = "$classes cf7m-thanks";
		
		// Remove duplicate classes and trim
		$classes = trim( implode( ' ', array_unique( explode( ' ', $classes ) ) ) );

		return $classes;
	}

	
	/**
	 * Check if current CF7 form has Multi tabs.
	 * 
	 * @param $form (HTML) the CF7 form content
	 * @return bool
	 * 
	 * @since 1.0.3
	 */
	function multi_enabled( $form ) {
		
		// Find multi part in the form
		preg_match_all( $this->part_regex, $form, $part_tags );
		
		// Return if only one part was found
		if ( count( $part_tags[0] ) > 1 )
			return true;
		
		return false;
	}
	
	
	/**Z
	 * Set this class form id.
	 * 
	 * This function does nothing to the current CF7 form, just add this class variable for further use
	 * 
	 * @param $properties (array) current contact form properties
	 * @param $cf7 (object) current contact form object
	 * 
	 * @since 0.1.0
	 */	
	function set_form_id( $properties, $cf7 ) {
		$this->form_id = $cf7->id();
		return $properties;
	}

	
	/**
	 * Enqueue plugin custom scripts and styles.
	 * 
	 * This will enqueue scripts/stylesheets even there is no selected style
	 * 
	 * @since 0.1.0
	 */
	function enqueue_scripts() {

		// Enqueue Multi scripts
		$dependencies = array(); // @since 1.0.7
		
		if ( method_exists( 'CF7_Skins_Contact', 'script_dependencies' ) )
			$dependencies = CF7_Skins_Contact::script_dependencies();

		// Enqueue this file to the footer to allow localize script being added while processing content.
		wp_enqueue_script( CF7SKINSMULTI_OPTIONS, CF7SKINSMULTI_URL . 'js/front.js', $dependencies, CF7SKINSMULTI_VERSION, true );

		// Enqueue stylesheets even there is no selected style
		// Adds default CF7 Skins Multi styles to Tabs, Progress Bar etc. before a specific CF7 Skins style is selected.
		$this->enqueue_styles();
	}
	
	
	/**
	 * Enqueue Multi styles.
	 * 
	 * If current contact form have selected style using 'cf7skins_enqueue_styles' hook with priority
	 * This stylesheet will be enqueued after CF7 Skins framework stylesheet and before selected style stylesheet
	 * 
	 * @since 0.1.0
	 */		
	function enqueue_styles() {
		// Add stylesheet dependencies @since 1.0.3
		$deps = method_exists( 'CF7_Skins_Contact', 'stylesheet_dependencies' ) ? CF7_Skins_Contact::stylesheet_dependencies() : array();
		wp_enqueue_style( 'cf7m-default', CF7SKINSMULTI_URL . 'css/framework/cf7m-default.css', $deps, CF7SKINSMULTI_VERSION );
	}

	
	/**
	 * Adds tabs to Contact Form 7 forms.
	 * 
	 * @param $cf7 (object) current contact form object
	 * 
	 * @since 0.1.0
	 */		
	function tabify( $cf7 ) {

		$form = $cf7->prop('form');
		$this->form = $form;
		$post_meta = get_post_meta ( $cf7->id() );

		// Get the parts tags
		preg_match_all( $this->part_regex, $form, $part_tags );

		// Return unmodified HTML if no tags was found
		if ( empty( $part_tags[ 0 ] ) ) {
			return $form;
		}

		// Return unmodified HTML if only one tag was found
		if ( count( $part_tags[ 0 ] ) == 1 ) {
			return $form;
		}
		
		// Enqueue scripts
		$this->enqueue_scripts();

		// Prepare template.
		$rand = mt_rand( 100000, 999999 );

		/**
		 * Multi Tabs (replace jquery.easytabs & jquery.ba-hashchange with pure js).
		 * 
		 * @since 2.5.4
		 */

		$etabs_template = '<div class="tab-container" data-form-id="'. $cf7->id() .'"><ul class="etabs">:li</ul><div class="epanels">:div</div></div>';

		$etabs_template = str_replace(  /* Remove etabs reference - NM */
			array( ':li', ':div' ),
			array( ":li_$rand", ":div_$rand" ),
			$etabs_template );

		// Prepare the content
		$tags = $part_tags[0];
		$titles = $part_tags[1];
		$contents = str_replace( $tags, ":separator_$rand", $form);
		$contents = array_values( array_filter( explode( ":separator_$rand", $contents ) ) );

		// Fill the template
		$form = $etabs_template;
		for ( $i = 0; $i < count( $titles ); $i++ ) {

			// Tab content is not set, set it to empty
			if ( ! isset( $contents[ $i ] ) ) {
				$contents[ $i ] = '';
			}

			// Sometime there may be an unwanted closing p tag, let's remove it first.
			if ( preg_match( '|<\/p>$|', trim( $contents[$i] ) ) && ! strstr( '<p>', $contents[ $i ] ) ) {
				$contents[ $i ] = str_replace( '</p>', '', $contents[ $i ] );
			}
			
			/**
			 * Fill the li and div.
			 * 
			 * @since 2.5.4
			 */
			$form = str_replace(
				array( ":li_$rand", ":div_$rand" ),
				array(
					sprintf(
						'<li class="tab"><a href="#" data-index="' . ( $i + 1 ) . '">%s</a></li>:li_%s',
						trim( $titles[ $i ] ),
						$rand
					),
					sprintf(
						'<div id="part-' . $cf7->id() . '-%s" class="panel-container">%s</div>:div_%s',
						$i + 1,
						trim( $contents[ $i ] ),
						$rand
					),
				),
				$form
			);
		}

		// Remove li_$rand and div_$rand
		$form = str_replace( array( ":li_$rand", ":div_$rand" ), '', $form );

		// Rewrite the new form
		$cf7->set_properties( array( 'form' => $form ) );

		// Setup localize data for Multi script @since 2.4.2
		// CF7 5.5 'wpcf7_contact_form' hook loops for the first ten/eleven found contact form
		// thus will creates localize script for it, even if the form is not there in the global $post content.
		// To avoid this, we need to check the form is inside main loop.
		if ( get_the_content() ) {
			$option = get_option( CF7SKINS_OPTIONS );
			$cf7skinsmulti_meta = get_post_meta ( $cf7->id(), CF7SKINSMULTI_OPTIONS, true );

			// Print JavaScript localize variables
			
			// Default Previous & Next buttons text- keep for backward compatiblity for old forms
			$cf7skinsmulti_meta['previous']	= __( '&laquo; Previous', 'cf7skins-multi' );
			$cf7skinsmulti_meta['next'] 	= __( 'Next &raquo;', 'cf7skins-multi' );

			$cf7skinsmulti_meta['scroll'] = isset( $option['multi_scroll'] ) ? $option['multi_scroll'] : true;
			
			// Add each tab cf7Name and cf7sLabel in exact order
			$cf7s_visual = get_post_meta( (int) $cf7->id(), 'cf7s_visual', true );
			$tabs = array();

			foreach( $cf7s_visual as $field ) {
				if ( $field->cf7sType ===  'multi-tab' ) { // only for Multi tab

					$tab = new stdClass; // set tab object
					$tab->cf7Name = $field->cf7Name;
					$tab->cf7sLabel = $field->cf7sLabel;

					$tabs[] = $tab; // add tab to array
				};
			};

			$cf7skinsmulti_meta['tabs'] = $tabs;

			wp_localize_script( CF7SKINSMULTI_OPTIONS, "cf7skinsmulti_{$cf7->id()}", $cf7skinsmulti_meta );
		}
	}

	
	/**
	 * Add custom logic script callback.
	 * 
	 * @param $scripts (array) CF7 Skins Logic scripts
	 * @param $cf7 (object) current contact form object
	 * 
	 * @since 1.0.7
	 */
	function logic_scripts( $scripts, $cf7 ) {
		$scripts['callbacks']['multi-tabs'] = 'cf7skinsmulti_logic_action';
		return $scripts;
	}
	
} new CF7_Skins_Multi_Front();
