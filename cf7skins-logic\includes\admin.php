<?php
/**
 * CF7 Skins Logic - Admin Class.
 *
 * Workflow: 
 *  
 * -- Field
 * -- -- Statements
 * -- -- -- Action
 * -- -- -- If
 * -- -- -- Field
 * -- -- -- Comparators
 * -- -- -- Value 1
 * -- -- -- Value 2
 *
 * @package cf7skins
 * @subpackage cf7skins-logic
 * 
 * @since 0.1.0
 */

 
class CF7_Skins_Logic_Admin extends CF7_Skins_Logic {
	
	var $statement;		// handles statement numbering
	var $criteria; 		// handles criteria numbering
	var $nonce;			// handles nonce for security
	var $messages;		// handles messages strings for translation @since 1.1
	
	
	/**
	 * Class constructor
	 * 
	 * @since 0.1.0
	 */	
	function __construct() {
		$this->nonce = 'cf7slogic';
		
		// Error/Warning Messages to users displayed on CF7 page
		$this->messages = array( // @since 1.1
			'select_field' 		=> __( 'Please select a field from the list.', 'cf7skins-logic' ),
			'select_another' 	=> __( 'The field has been added below, please select another field.', 'cf7skins-logic' ),
			'select_criteria' 	=> __( 'Please select one criteria from the dropdown list.', 'cf7skins-logic' ),
			'missing_field' 	=> __( '%1$s is missing or deleted, this field will be removed.', 'cf7skins-logic' ), // @since 1.1
			'missing_criteria' 	=> __( '%1$s is missing or deleted, please choose another.', 'cf7skins-logic' ), // @since 1.1
		);
		
		// Create the metabox for CF7 Skins
		if( CF7SKINSLOGIC_USE_PANEL ) {
			add_filter( 'wpcf7_editor_panels', array( &$this, 'logic_panel' ) );
		} else {
			add_action( 'wpcf7_add_meta_boxes', array( &$this, 'add_meta_boxes' ) );
			add_action( 'wpcf7_admin_footer', array( &$this, 'add_meta_boxes_42' ) );
		}
		
		// AJAX function
		add_action( 'wp_ajax_add_field', array( &$this, '_ajax_add_field' ) );
		add_action( 'wp_ajax_add_statement', array( &$this, '_ajax_add_statement' ) );
		add_action( 'wp_ajax_select_criteria', array( &$this, '_ajax_select_criteria' ) );
		add_action( 'wp_ajax_logic_update_field', array( &$this, '_ajax_update_field' ) ); // @since 1.1
		
		// Push the styles and scripts to the admin header
		add_action( 'cf7s_admin_enqueue_scripts', array( &$this, 'admin_enqueue_scripts' ) );
		
		// Add action while saving the contact form
		add_action( 'cf7skins_update', array( &$this, 'save_logic' ) );
		
		add_action( 'cf7skins_copy', array( $this, 'copy_logic' ) ); // @since 0.3
		add_filter( 'cf7s_visual_update_js_callbacks', array( $this, 'visual_update' ) ); // @since 1.1

		// add_logic_tab
		add_filter( 'cf7skins_tabs', array( $this, 'add_logic_tab' ) );
		add_action( 'admin_enqueue_scripts', array( $this, 'add_logic_scripts' ) );
		// add_action( 'wp_ajax_skins_logic_data', array( $this, 'save_logic_data' ) );
		add_filter( 'cf7svisual_localize_script' , array($this, 'add_logic_data') );
		add_action( 'cf7s_visual_before_update', array($this, 'save_logic_data'), 10, 2);
		add_action( 'admin_enqueue_scripts', array( $this, 'enqueue_translations_script' ), 13 );
	}
	
	/**
	 * Add logic tab.
	 *
	 * @param array $tabs
	 */
	public function add_logic_tab( $tabs ) {
		$tabs['logic'] = array(
			'name'  => 'logic',
			'order' => 2,
			'label' => __( 'Logic', 'cf7skins-logic' ),
			'help'  => __( 'Show or hide form fields dynamically. Drag and drop fields or tabs to apply conditional logic to them.', 'cf7skins-logic' ),
			'note'  => __( 'Drag and drop a dependent field. Select the field, user or post attribute that the logic is based upon. Add as many logic statements as you need.', 'cf7skins-logic' ),			
		);

		return $tabs;
	}

	/**
	 * Load admin logic script.
	 */
	public function add_logic_scripts() {

		global $wpdb, $post;

		if ( class_exists( 'CF7_Skins_Admin' ) && ! CF7_Skins_Admin::edit_page() ) {
			return;
		}

		if ( isset( $_GET['page'] ) && $_GET['page'] === 'wpcf7' && isset( $_GET['post'] ) && is_null( $post ) ) {
			$post_id = $_GET['post'];
		} elseif ( isset( $_GET['page'] ) && $_GET['page'] === 'wpcf7' && ! isset( $_GET['post'] ) ) {
			return;
		} elseif ( isset( $_GET['page'] ) && $_GET['page'] === 'wpcf7-new' && ! isset( $_GET['post'] ) ) {
				$post_id = 0;
		} else {
			$post_id = $post->ID;
		}
		
		$limit        = apply_filters( 'postmeta_form_limit', 90 );
		$sql          = "SELECT DISTINCT meta_key
			FROM $wpdb->postmeta
			WHERE meta_key NOT BETWEEN '_' AND '_z'
			HAVING meta_key NOT LIKE %s
			ORDER BY meta_key
			LIMIT %d";

		$keys         = $wpdb->get_col( $wpdb->prepare( $sql, $wpdb->esc_like( '_' ) . '%', $limit ) );

		$allowed_keys = array();
		if ( $keys ) {
			natcasesort( $keys );

			foreach ( $keys as $key ) {
				if ( is_protected_meta( $key, 'post' ) ) {
					continue;
				}

				$allowed_keys[] = $key;
			}
		}

		$roles      = wp_roles();
		$logic_data = get_post_meta( $post_id, CF7SKINSLOGIC_META, true );

		wp_enqueue_script( 'visual-logic', CF7SKINSLOGIC_URL . 'js/logic.js', array( 'cf7skins' ), time(), true );

		wp_localize_script(
			'visual-logic',
			'CF7LOGIC_DATA',
			array(
				'admin_ajax' => admin_url( 'admin-ajax.php' ),
				'nonce'      => wp_create_nonce( 'logic-nonce' ),
				'user_roles' => $roles->get_names(),
				'meta_keys'  => $allowed_keys,
				'logic_data' => $logic_data,
			)
		);
		wp_enqueue_style( 'visual-logic', CF7SKINSLOGIC_URL . 'css/logic.css', array(), time(), 'all' );
	}

	/**
	 * Add logic data to localize scripts.
	 * 
	 * @param array $scripts All localize data.
	 */
	public function add_logic_data( $scripts ) {
		$logic = array();
		if ( isset( $_GET['post'] ) ) {
			$logic = get_post_meta( (int) $_GET['post'], CF7SKINSLOGIC_META, true );
		}
		$scripts['logic'] = $logic;
		return $scripts;
	}

	/**
	 * Save logic.
	 */
	public function save_logic_data_old() {

		if ( ! wp_verify_nonce( $_POST['nonce'], 'logic-nonce' ) ) {
			wp_send_json_error(
				array(
					'error' => __( 'You are not allowed', 'cf7skins-logic' ),
				)
			);
		}

		$post_id = absint( $_POST['post_id'] );

		if ( ! $post_id ) {
			wp_send_json_error(
				array(
					'error' => __( 'First save contact form', 'cf7skins-logic' ),
				)
			);
		}

		$posted_data = $this->sanitize_logic( json_decode( wp_unslash( $_POST['data'] ), true ) );

		update_post_meta( $post_id, CF7SKINSLOGIC_META, $posted_data );
		wp_send_json_success(
			array(
				'post'      => $posted_data,
				'post_id'   => $post_id,
				'samitized' => '',
			)
		);
	}

	/**
	 * Save logic data.
	 */
	public function save_logic_data( $visual_data, $posted_data ) {

		$form_id = absint( $posted_data[ 'form_id' ] );

		if ( ! $form_id ) { // bail early if form ID is not set
			return;
		}

		$logic_posted_data = $this->sanitize_logic( json_decode( wp_unslash( $posted_data['logic'] ), true ) );
		update_post_meta( $form_id, CF7SKINSLOGIC_META, $logic_posted_data );
	}

	/**
	 * Clean logic data.
	 * 
	 * @param (array)	$raw_logic	logic data.
	 */
	public function sanitize_logic( $raw_logic ) {
		// add more checks TODO
		$logic = array();

		foreach( $raw_logic as $key => $fields ) {
			$logic[ $key ] = $fields;
			$logic[ $key ]['statements'] = array(); // set empty

			foreach( $fields['statements'] as $statement ) {
				
				// define empty if is not set
				$cf7sType = isset( $statement['cf7sType'] ) ? $statement['cf7sType'] : '';				

				switch ( $cf7sType ) {					
					case 'url':
						$statement['value1'] = esc_url( $statement['value1'] );
						break;

					case 'email':
						$statement['value1'] = sanitize_email( $statement['value1'] );
						break;
						
					case 'number':
						$statement['value1'] = (int) $statement['value1'];
						break;

					case 'text':
					default:
						if ( is_array( $statement['value1'] ) ) { // Value is an array for multiple select
							foreach( $statement['value1'] as $k => $val ) { // loop for each value
								$statement['value1'][ $k ] = sanitize_text_field( $val ); // sanitize
							}
						} else { // value is a string
							$statement['value1'] = sanitize_text_field( $statement['value1'] ); // sanitize
						}
						break;
				}

				$logic[ $key ]['statements'][] = $statement;
			}
		}

		return $logic;
	}

	/**
	 * Convert comparator.
	 * 
	 * @param string $value Value.
	 */
	public static function maybe_convert_comparator( $value ) {

		$value = str_replace( '&gt;', '>', $value );
		$value = str_replace( '&lt;', '<', $value );
		return $value;
	}

	/**
	 * Add logic tab and panel
	 * 
	 * @param $panels (array) CF7 panels
	 * 
	 * @since 0.1.0
	 */
	function logic_panel( $panels ) {
		$panels['logic'] = array(
			'title' => __( 'Logic', 'cf7skins-logic' ),
			'callback' => array( &$this, 'logic_panel_callback' )
		);
		
		return $panels;
	}
	
	
	/**
	 * Logic panel callback
	 * 
	 * @param (Object)	$cf7	current CF7 object
	 * 
	 * @since 0.1.0
	 */
	function logic_panel_callback( $cf7 ) {
		$this->meta_box( $cf7 );
	}
	
	
	/**
	 * Logic items of all tags from current form and registered addon tag.
	 * Each item is grouped by field.
	 * 
	 * @param (Object)	$cf7 	current CF7 object
	 * 
	 * @return (Array) form and registered items
	 * 
	 * @since 1.1
	 */	
	function logic_items( $cf7 ) {
		$form = $cf7->prop('form');
		$_shortcodes = $this->get_shortcodes( $form );
		$items = array();
		
		// Setup field (CF7 shortcode tag) for list items
		$items['fields']['name'] = __( 'Fields', 'cf7skins-logic' );

		foreach( $_shortcodes as $shortcode ) // arrange by name to avoid similar tag name
			$items['fields']['items'][ $shortcode['name'] ] = $shortcode;
		
		/**
		 * Add filters for select item 
		 * All registered items should match this structure:
		 * 		['item-key']['name']  = translated string of item key name
		 * 		['item-key']['items'] = array of available items
		 * 
		 * @param $items (array) list of available items
		 * @param $cf7 (object) current CF7 object
		 * @since 0.3
		 */
		return apply_filters( 'cf7skins_logic_items', $items, $cf7 );
	}
	
	
	/**
	 * Wrapper for logic criterias filter
	 * 
	 * @param (String/HTML) $form		CF7 form
	 * @param (String)		$field		selected logic field, default empty
	 * 
	 * @return (Array) default and add-on registered criterias
	 * 
	 * @since 1.1
	 */
	function logic_criterias( $form, $field = '' ) {

		$get_shortcodes = array();

		// Populate the contact form fields for create_select() options
		foreach ( $this->get_shortcodes( $form ) as $k => $shortcode ) {
			$get_shortcodes[ $shortcode['name'] ] = $shortcode['name'];
		}

		if ( $field ) { // delete current selected field
			unset( $get_shortcodes[ $field ] ); 
		}

		/**
		 * Set the logic criteria dropdown select
		 * @filter 'cf7skins_logic_criterias', allow other functions to add/modify the criteria
		 * @param (array) array of criteria type as the key and an array of its options.
		 * @since 0.0.1
		 */	
		return apply_filters( 'cf7skins_logic_criterias', array(
			'field' => array(
				'name'		=> __( 'Field', 'cf7skins-logic' ),
				'options'	=> $get_shortcodes
			),
			'user' => array(
				'name'		=> __( 'User', 'cf7skins-logic' ),
				'options'	=> array(
					'user_logged_in'	=> __( 'user logged in', 'cf7skins-logic' ),
					'user_role'			=> __( 'user role', 'cf7skins-logic' )
				),
			),
			'post' => array(
				'name'		=> __( 'Post', 'cf7skins-logic' ),
				'options'	=> array(
					'post_id'			=> __( 'post ID', 'cf7skins-logic' ),
					'have_meta_key'		=> __( 'have meta key', 'cf7skins-logic' )
				)
			),
		));
	}	
	
	
	/**
	 * Create field select dropdown
	 * 
	 * @param $cf7 (object) current post object
	 * 
	 * @return (String) dropdown select HTML
	 * 
	 * @since 1.1
	 */
	function select_field( $cf7 ) {
		$items = $this->logic_items( $cf7 );
		
		// Check if shortcode list <li> exists
		if ( ! $items ) {
			echo '<p>'. __( 'To use CF7 Skins Logic you will need to Select & Save a CF7 Skins Template.', 'cf7skins-logic' ) .'</p>';
			return;
		}
		
		// Modify items to support create_select() options parameter
		foreach ( $items as $key => $val ) {
			if ( isset( $val['items'] ) ) { // make sure tag item exists
				foreach ( $val['items'] as $k => $item ) {
					$items[$key]['options'][$k] = $k; // add options array
				}
				$items[$key]['data-item'] = $key; // for <option/> data attribute
				unset( $items[$key]['items'] ); // remove to reduce memory
			}
		}
		
		// Create <select/> 
		return $this->create_select( array(
			'name' 			=> 'logic-field',
			'id'			=> 'logic-field',
			'default'		=> __( 'Select Item', 'cf7skins-logic' ),
			'options'		=> $items,
			'attributes'	=> array( 'data-item' ),
		));
	}
	
	
	/**
	 * Create criteria select dropdown
	 * 
	 * @param (String/HTML) $form		CF7 form
	 * @param (String)		$selected	selected criteria, default is empty
	 * 
	 * @return (String) dropdown select HTML
	 * 
	 * @since 1.1
	 */	
	function select_criteria( $args ) {
		// Merge the user-selected arguments with the defaults.
		$args = wp_parse_args( (array) $args, array( 
			'field'			=> '',
			'statements'	=> 0,
			'form'			=> array(),
			'criteria'		=> '',
		));
		
		extract( $args, EXTR_SKIP );
		
		$criterias = $this->logic_criterias( $form, $field );
		
		// Insert <option/> data attribute
		foreach ( $criterias as $k => $val ) {
			$criterias[$k]['data-type'] = $k; // for <option/> data attribute
		}
		
		// Create <select/> 
		return $this->create_select( array(
			'class' 		=> 'criteria',
			'name' 			=> "logic[$field][statements][$statements][criteria]",
			'default'		=> __( 'Select criteria', 'cf7skins-logic' ),
			'options'		=> $criterias,
			'selected'		=> $criteria,
			'attributes'	=> array( 'data-type' ),
		));
	}
	
	
	/**
	 * Create the metabox
	 * 
	 * @param (Object)	$cf7	current CF7 object
	 * 
	 * @since 0.1.0
	 */
	function meta_box( $cf7 ) {
		?>
		<span class="spinner" style="display:none;"></span>
		
		<div id="logic-fields">
			<div class="dashicons dashicons-list-view" alt="f163"></div>&nbsp;
			<label for="logic-field" class="balloon" title="<?php _e( 'Select form field that the logic action will be applied to.', 'cf7skins-logic' ); ?>">
				<strong><?php _e( 'Select Item', 'cf7skins-logic' ); ?></strong>
			</label>
			
			<?php echo $this->select_field( $cf7 ); ?>
			
			<a id="add-field" class="button balloon" href="#" title="<?php _e( 'Add a form field that the logic action will be applied to.', 'cf7skins-logic' ); ?>">
				<span alt="f132" class="dashicons dashicons-plus"></span>&nbsp;<?php _e( 'Add Field', 'cf7skins-logic' ); ?>
			</a>
		</div>
		
		<hr />
		
		<div id="logic-elements">
			<?php $this->generate_logics( $cf7 ); ?>
		</div>
		<?php
	}
	
	
	/**
	 * Add field function
	 * 
	 * @param $args (array) 
	 * 
	 * @since 0.1.0
	 */
	function add_field( $args ) {
		
		// Merge the user-selected arguments with the defaults.
		$args = wp_parse_args( (array) $args, array( 
			'field'			=> '',
			'post_ID'		=> null,
			'form'			=> '',
			'item'			=> 'fields',
			'statements'	=> null,
			'num'			=> null	
		));
		
		extract( $args, EXTR_SKIP );
		
		$field = esc_attr( $field ); // sanitize the field
		$form = wp_unslash( $form ); // strip all backslashes
		
		// Get logic meta if already added or use default arguments if a newly created logic
		$meta = get_post_meta( $post_ID, CF7SKINSLOGIC_META, true ); // print_r( $meta );
		$_action = isset( $meta[$field]['action'] ) ? $meta[$field]['action'] : '';
		$_if = isset( $meta[$field]['if'] ) ? $meta[$field]['if'] : '';
		$_item = isset( $meta[$field]['item'] ) ? $meta[$field]['item'] : $item; // @since 0.3
		
		// Output the HTML block
		echo '<div class="field" data-field="'. $field .'">';
			echo '<span class="field-name">'. $field .'</span>';
			
			// Show/hide option
			echo $this->create_select( array( 
				'name' 		=> "logic[$field][action]",
				'options' 	=> array(
					'show' 	=>  __( 'Show this', 'cf7skins-logic' ),
					'hide' 	=>  __( 'Hide this', 'cf7skins-logic' )
				),
				'selected' 	=> $_action
			));
			
			echo '<span class="logic-if">'. __( 'if', 'cf7skins-logic' ) .'</span>';
			
			// If/all option
			echo $this->create_select( array( 
				'name' 		=> "logic[$field][if]",
				'options' 	=> array(
					'any' 	=>  __( 'any', 'cf7skins-logic' ),
					'all' 	=>  __( 'all', 'cf7skins-logic' )
				),
				'selected' 	=> $_if
			));
			
			// Item type @since 0.3
			echo $this->create_input( array( 
				'name'		=> "logic[$field][item]",
				'type'		=> 'hidden',
				'value'		=> $_item,
			));
			
			echo '<span class="logic-of">'. __( 'of the following statements are met', 'cf7skins-logic' ) .'</span>';
			
			echo '<a class="add-statement control balloon" href="#" title="'. __( 'Add a conditional logic statement.', 'cf7skins-logic' ) .'">'.
				__( 'Add Statement', 'cf7skins-logic' ) .'</a>';
			
			echo '<a class="control balloon control-right remove-field" href="#" title="'. __( 'Remove this Field.', 'cf7skins-logic' ) .'">
					<span class="dashicons dashicons-no"></span>
				</a>';
			
			echo '<div class="statements">';
	
				// Add statement and criteria
				if( $statements ) { // for previously created statements
					foreach( $statements as $k => $statement ) {
						echo '<div class="statement" data-num="'.$k.'">';
							$args = array( 
								'field'			=> $field,
								'statements'	=> $k,
								'form'			=> $form
							);
							$args = array_merge( $args, $statement );
							$this->add_statement( $args );
							
							$this->add_criteria( $args ); 
						echo '</div>';
					}

				} else { // for new statement
					echo '<div class="statement" data-num="'.$num.'">';
						$args = array( 
							'field'			=> $field,
							'statements'	=> $num,
							'form'			=> $form
						);

						$this->add_statement( $args );
						
					echo '</div>';
				}
				
			echo '</div>';
		echo '</div>';
	}
	
	
	/**
	 * Update current contact form post meta data for selected style or template
	 * 
	 * @param $cf7 is the contact form 7 object data
	 * 
	 * @since 0.1.0
	 */
	function add_statement( $args ) {
		
		// Merge the user-selected arguments with the defaults.
		$args = wp_parse_args( (array) $args, array( 
			'field'			=> '',
			'statements'	=> 0,
			'form'			=> array(),
			'action'		=> '',
			'criteria'		=> '',
			'comparator'	=> '',
			'value1'		=> '',
			'value2'		=> '',
		));
		
		extract( $args, EXTR_SKIP );
		?>
		<a href="#" class="control remove-statement balloon" title="<?php _e( 'Remove this Statement.', 'cf7skins-logic' ); ?>">
			<span class="dashicons dashicons-no-alt"></span>
		</a>
		
		<?php echo $this->select_criteria( $args ); ?>
		
		<?php
		// Check if tag or other option was deleted @since 1.1
		$option_exist = false;
		
		$criterias = $this->logic_criterias( $form, $field );
		
		foreach ( $criterias as $key => $values ) { // loop criteria
			foreach ( $values['options'] as $k => $v ) { // loop criteria options
				if ( $criteria === $k ) { // criteria exists
					$option_exist = true; 
				}
			}
		}

		// Show message if previous criteria is not existed in form
		if ( $criteria && ! $option_exist ) {
			echo '<span class="error-message"><span class="dashicons dashicons-warning"></span>&nbsp;'. 
				sprintf( $this->messages['missing_criteria'], $criteria ) .'</span>';
		}
	}
	
	
	/**
	 * Parse pipes within options
	 * 
	 * @param $options 	(Array) 	select, checkbox or radio options contains | (pipe)
	 * 
	 * @return array of options with the last exploded pipe as key
	 * 
	 * @since 1.1.2
	 */
	function parse_pipes( $options ) {
		
		// Setup CF7 pipes, see contact-form-7/includes/pipe.php
		$pipes = new WPCF7_Pipes( $options );
		
		// Get before pipe
		$befores = $pipes->collect_befores();
		
		// CF7 shows 'before' in front-end
		return array_combine( $befores, $befores );
	}
	
	
	/**
	 * Add a criteria with the comparator and value
	 * 
	 * @param $args (array) see default values below
	 * 
	 * @since 0.1.0
	 */
	function add_criteria( $args ) {
		
		// Merge the user-selected arguments with the defaults.
		$args = wp_parse_args( (array) $args, array( 
			'field'			=> '',
			'type'			=> '',
			'statements'	=> 0,
			'form'			=> array(),
			'criteria'		=> '',
			'comparator'	=> '',
			'value1'		=> '',
			'value2'		=> '',
		));
		
		extract( $args, EXTR_SKIP ); // extract arguments
		
		// HTML element name
		$type_name = "logic[$field][statements][$statements][type]";
		$comparator_name = "logic[$field][statements][$statements][comparator]";
		$value1_name = "logic[$field][statements][$statements][value1]";
		$value2_name = "logic[$field][statements][$statements][value2]";

		switch ( $type ):
			case 'field':
				foreach( $this->get_shortcodes( $form ) as $shortcode ) {
					if( $shortcode['name'] == $criteria ) {
						switch ( $shortcode['basetype'] ):
							case 'number': // for spinbox type
							case 'range':  // for slider type
								echo $this->create_select( array( 
									'name' 		=> $comparator_name,
									'options' 	=> array(
										'=' 	=>  __( '=', 'cf7skins-logic' ),
										'!=' 	=>  __( '!=', 'cf7skins-logic' ),
										'>' 	=>  __( '>', 'cf7skins-logic' ),
										'>='	=>  __( '>=', 'cf7skins-logic' ),
										'<' 	=>  __( '<', 'cf7skins-logic' ),
										'<=' 	=>  __( '<=', 'cf7skins-logic' ),
									),
									'selected' 	=> $comparator
								));
								
								echo $this->create_input( array( 
									'name'		=> $value1_name,
									'value'		=> $value1,
								));
								break;

							case 'radio':
								echo $this->create_select( array( 
									'name' 		=> $comparator_name,
									'options' 	=> array(
										'='	 =>  __( '=', 'cf7skins-logic' ),
										'!=' =>  __( '!=', 'cf7skins-logic' )
									),
									'selected' 	=> $comparator
								));

								echo $this->create_select( array( 
									'name'		=> $value1_name,
									'options'	=> $this->parse_pipes( $shortcode['raw_values'] ), // @since 1.1.2
									'selected'	=> $value1,
									'type'		=> $shortcode['basetype'],
								));
								break;

							case 'select':
								$select_options = array(
									'='	 =>  __( '=', 'cf7skins-logic' ),
									'!=' =>  __( '!=', 'cf7skins-logic' )
								);

								// Allow multiple is checked
								$allowed_multiple = false;
								if ( !! $shortcode['options'] ) {
									if ( in_array( 'multiple', $shortcode['options'] ) ) {										
										$allowed_multiple = true;
									}
								}

								// Add 'contains' feature if multiple selection allowed
								if ( $allowed_multiple ) {
									$select_options['contains'] = __( 'contains', 'cf7skins-logic' );
								}

								echo $this->create_select( array( 
									'name' 		=> $comparator_name,
									'options' 	=> $select_options,
									'selected' 	=> $comparator
								));

								if ( $allowed_multiple ) {
									echo $this->create_input( array( 
										'name'		=> $value1_name,
										'options'	=> $this->parse_pipes( $shortcode['raw_values'] ), // @since 1.1.2
										'value'		=> $value1,
										'type'		=> 'checkbox',
									));							
								} else {
									echo $this->create_select( array( 
										'name'		=> $value1_name,
										'options'	=> $this->parse_pipes( $shortcode['raw_values'] ), // @since 1.1.2
										'selected'	=> $value1,
										'type'		=> $shortcode['basetype'],
									));
								}
								break;
								
							case 'checkbox':
								echo $this->create_select( array( 
									'name' 		=> $comparator_name,
									'options' 	=> array(
										'='	 		=>  __( '=', 'cf7skins-logic' ),
										'!=' 		=>  __( '!=', 'cf7skins-logic' ),
										'contains' 	=>  __( 'contains', 'cf7skins-logic' )
									),
									'selected' 	=> $comparator
								));
									
								echo $this->create_input( array( 
									'name'		=> $value1_name,
									'options'	=> $this->parse_pipes( $shortcode['raw_values'] ), // @since 1.1.2
									'value'		=> $value1,
									'type'		=> $shortcode['basetype'],
								));
								break;
								
							case 'acceptance':
								echo $this->create_select( array( 
									'name' 		=> $value1_name,
									'options' 	=> array(
										'0' =>  __( 'unchecked', 'cf7skins-logic' ),
										'1' =>  __( 'checked', 'cf7skins-logic' )
									),
									'selected' 	=> $value1
								));
								break;
								
							case 'file':
								echo $this->create_select( array( 
									'name' 		=> $value1_name,
									'options' 	=> array(
										'0' =>  __( 'is empty', 'cf7skins-logic' ),
										'1' =>  __( 'is not empty', 'cf7skins-logic' )
									),
									'selected' 	=> $value1
								));
								break;
								
							case 'textarea':
								echo $this->create_select( array(
									'class'		=> "comparator",
									'name' 		=> $comparator_name,
									'options' 	=> array(
										'=' 			=>  __( '=', 'cf7skins-logic' ),
										'changed'		=>  __( 'changed', 'cf7skins-logic' ), // this includes typing, copy, paste, etc to input text or textarea as long as it is not empty
										'contains_text'	=>  __( 'contains', 'cf7skins-logic' )
									),
									'selected' 	=> $comparator
								));
								echo $this->create_input( array( 
									'name'		=> $value1_name,
									'value'		=> $value1,
									'class'		=> 'changed' === $comparator ? 'hidden' : '',
								));
								break;
								
							case 'text':
							default:
								echo $this->create_select( array( 
									'class'		=> "comparator",
									'name' 		=> $comparator_name,
									'options' 	=> array(
										'=' 			=>  __( '=', 'cf7skins-logic' ),
										'!=' 			=>  __( '!=', 'cf7skins-logic' ),
										'>' 			=>  __( '>', 'cf7skins-logic' ),
										'>='			=>  __( '>=', 'cf7skins-logic' ),
										'<' 			=>  __( '<', 'cf7skins-logic' ),
										'<=' 			=>  __( '<=', 'cf7skins-logic' ),
										'changed'		=>  __( 'changed', 'cf7skins-logic' ), // this includes typing, copy, paste, etc to input text or textarea as long as it is not empty
										'contains_text'	=>  __( 'contains', 'cf7skins-logic' ),
										),
									'selected' 	=> $comparator
								));
								
								echo $this->create_input( array( 
									'name'		=> $value1_name,
									'value'		=> $value1,
									'class'		=> 'changed' === $comparator ? 'hidden' : '',
								));
								break;
						endswitch;
					}
				}
				break;

			case 'user':
				switch ( $criteria ):
					case 'user_logged_in':
						echo $this->create_select( array( 
							'name' 		=> $value1_name,
							'options' 	=> array(
								'0' =>  __( 'false', 'cf7skins-logic' ),
								'1' =>  __( 'true', 'cf7skins-logic' )
							),
							'selected' 	=> $value1
						));
						break;

					case 'user_role':
						echo $this->create_select( array( 
							'name' 		=> $comparator_name,
							'options' 	=> array(
								'='	 =>  __( '=', 'cf7skins-logic' ),
								'!=' =>  __( '!=', 'cf7skins-logic' )
							),
							'selected' 	=> $comparator
						));

						global $wp_roles;
						$roles = array();
						foreach( $wp_roles->roles as $k => $role )
							$roles[$k] = $role['name'];
						
						echo $this->create_input( array( 
							'name'		=> $value1_name,
							'options'	=> $roles,
							'value'		=> $value1,
							'type'		=> 'checkbox',
						));

						break;

					default:
						$comparators['='] = '';
						break;
				endswitch;	
				break;
				
			case 'post':
				switch ( $criteria ):
					case 'post_id':
						echo $this->create_select( array( 
							'name' 		=> $comparator_name,
							'options' 	=> array(
								'='	 =>  __( '=', 'cf7skins-logic' ),
								'!=' =>  __( '!=', 'cf7skins-logic' )
							),
							'selected' 	=> $comparator
						));
						
						echo $this->create_input( array( 
							'name'	=> $value1_name,
							'value'	=> $value1,
							'type'	=> 'number',
						));
						break;

					case 'have_meta_key':
						// Use wp-admin\includes\template.php line 667 function meta_form( $post = null )
						global $wpdb;
						$limit = apply_filters( 'postmeta_form_limit', 90 );
						$sql = "SELECT DISTINCT meta_key
							FROM $wpdb->postmeta
							WHERE meta_key NOT BETWEEN '_' AND '_z'
							HAVING meta_key NOT LIKE %s
							ORDER BY meta_key
							LIMIT %d";
						$keys = $wpdb->get_col( $wpdb->prepare( $sql, $wpdb->esc_like( '_' ) . '%', $limit ) );
						if ( $keys ) {
							natcasesort( $keys );
							$allowed_keys = array();
							foreach ( $keys as $key ) {
								if ( is_protected_meta( $key, 'post' ) )
									continue;
								
								$allowed_keys[esc_attr($key)] = esc_html($key);
							}

							echo $this->create_select( array( 
								'name' 		=> $value1_name,
								'options' 	=> $allowed_keys,
								'selected' 	=> $value1
							));
						}
					
						break;

					default:
						$comparators['='] = '';
						break;
				endswitch;
				break;
			
			default:
				_e( 'Invalid', 'cf7skins-logic' );
		endswitch;
		
		echo $this->create_input( array( 
			'name'		=> $type_name,
			'type'		=> 'hidden',
			'value'		=> $type,
		));
	}
	
	
	/**
	 * Add field function via WP AJAX
	 * 
	 * @since 0.1.0
	 */
	function _ajax_add_field() {
		
		// Check if we have the same nonce for security reason and selected field exists
		if ( ! wp_verify_nonce( $_POST['nonce'], $this->nonce ) && ! isset( $_POST['field'] ) )
			die();

		// Setup the arguments/parameters to pass for creating the field
		$args = (array) $_POST;
		$args['field'] = esc_attr( $args['field'] );
		$args['num'] = (int) $args['statements'];
		$args['item'] = esc_attr( $args['item'] ); // @since 0.3
		$args['statements'] = null;

		// Add the field
		$this->add_field( $args );

		exit;
	}
	
	
	/**
	 * Add statements via WP AJAX
	 * 
	 * @since 0.1.0
	 */
	function _ajax_add_statement() {
		
		// Check if we have the same nonce for security reason and selected field exists
		if ( ! wp_verify_nonce( $_POST['nonce'], $this->nonce ) && ! isset( $_POST['field'] ) )
			die();

		// Setup the arguments/parameters to pass for creating the field
		$args = array();
		$args['field'] = esc_attr( $_POST['field'] );
		$args['statements'] = (int) $_POST['statements'];
		$args['form'] = wp_unslash( $_POST['form'] );

		// Add the statement
		echo '<div class="statement" data-num="'.$args['statements'].'">';
			$this->add_statement( $args );
		echo '</div>';
		exit;
	}
	
	
	/**
	 * Update current contact form post meta data for selected style or template
	 * 
	 * @param $cf7 is the contact form 7 object data
	 * 
	 * @since 0.2.0
	 */
	function _ajax_select_criteria() {
		
		// Check if we have the same nonce for security reason
		if ( ! wp_verify_nonce( $_POST['nonce'], $this->nonce ) )
			die();

		// Setup the arguments/parameters to pass for creating the field
		$args = array( 
			'field'			=> esc_attr( $_POST['field'] ),
			'statements'	=> (int) $_POST['statements'],
			'criteria'		=> esc_attr( $_POST['criteria'] ),
			'type'			=> esc_attr( $_POST['type'] ),
			'post_ID'		=> (int) $_POST['post_ID'],
			'form'			=> wp_unslash( $_POST['form'] )
		);		
		
		// Add the criteria
		$this->add_criteria( $args );

		exit;
	}
	
	
	/**
	 * Update Logic field dropdown after save Visual
	 * Update Logic criteria dropdown after save Visual
	 * 
	 * @since 1.1
	 */
	function _ajax_update_field() {
		
		// Check if we have the same nonce for security reason
		if ( ! wp_verify_nonce( $_POST['nonce'], $this->nonce ) || ! isset( $_POST['post_ID'] ) )
			die();

		$cf7 = WPCF7_ContactForm::get_instance( (int) $_POST['post_ID'] ); // get CF7 object
		$cf7->set_properties( // manual set CF7 form value @since vis-0.6.5
			array( 'form' => wp_unslash( $_POST['form'] ) ) // remove extra backslases
		);

		$output = array();
		$output['field'] = $this->select_field( $cf7 ); // output new logic field <select />
		$output['criteria'] = $this->select_criteria( array( // output new logic criteria <select />
			'form'	=> $cf7->prop('form'),
		) );

		echo json_encode( $output );
		exit;
	}
	
	
	/**
	 * Update current logic meta while saving the CF7
	 * 
	 * @param $cf7 (object) current CF7 object
	 * 
	 * @since 0.1.0
	 */
	function save_logic( $cf7 ) {
		
		$post_id = $cf7->id(); // get the form post ID
		
		// Return if there is no logic post data and 
		// Delete the post meta if users delete all the field/statements
		if( ! isset( $_POST['logic'] ) ) {
			delete_post_meta( $post_id, CF7SKINSLOGIC_META );
			return;
		}
		
		$data = array(); $i = 0;
		
		// Loop through the $_POST data, remove empty value	
		foreach( $_POST['logic'] as $field => $logic ) {
			
			// Sanitize the logic "action" and "if"
			$data[$field]['action'] = esc_attr( $logic['action'] );
			$data[$field]['if'] = esc_attr( $logic['if'] );
			$data[$field]['item'] = $logic['item'] ? esc_attr( $logic['item'] ) : 'fields';
			
			foreach( $logic['statements'] as $statement ) {
				
				// Remove empty statement, if there is no field criteria selected.
				if( ! empty( $statement['criteria'] ) ) { 
					$data[$field]['statements'][$i] = $statement; // re-arrange the statement index
					$i++;
				}
			}
		}
		
		update_post_meta( $post_id, CF7SKINSLOGIC_META, $data ); // update the post meta
	}
	
	/**
	 * Update visual logic data for new created form
	 * 
	 * @param $cf7 (object) current CF7 object
	 * 
	 * @since 2.7.3 alpha
	 */
	function save_new_visual_logic( $cf7 ) {
		
		// Return if there is no hidden logic input
		if( ! isset( $_POST['cf7s-logic'] ) ) {
			return;
		}
		
		$logic_posted_data = $this->sanitize_logic( json_decode( wp_unslash( $_POST['cf7s-logic'] ), true ) );
		update_post_meta( $cf7->id(), CF7SKINSLOGIC_META, $logic_posted_data ); // update the post meta
	}

	/**
	 * Enqueue logic styles and scripts
	 * 
	 * @since 0.1.0
	 */
	function admin_enqueue_scripts() {

		wp_enqueue_style( 'cf7s-logic-admin',
			CF7SKINSLOGIC_URL . 'css/admin.css',
			array( 'contact-form-7-admin', 'cf7s-admin' ), CF7SKINSLOGIC_VERSION, 'all' );
			
		wp_enqueue_script( 'cf7s-logic-admin',
			CF7SKINSLOGIC_URL . 'js/jquery.admin.js',
			array( 'jquery' ), CF7SKINSLOGIC_VERSION );
			
		wp_localize_script( 'cf7s-logic-admin', 'cf7slogic', array(
			'ajaxurl'	=> admin_url( 'admin-ajax.php' ),
			'nonce'		=> wp_create_nonce( $this->nonce ),
			'messages'	=> $this->messages,
		));
	}
	
	
	/**
	 * Create the metabox
	 * 
	 * @param $post_id (int) current CF7 post ID
	 * 
	 * @since 0.1.0
	 */	
	function add_meta_boxes( $post_id ) {
		add_meta_box( 'cf7slogic', __( 'Logic', 'cf7skins' ),
			array( &$this, 'meta_box' ), null, 'mail', 'core' );
	}	
	
	
	/**
	 * Custom metabox added in the CF7 Footer for version 4.2
	 *
	 * @param $cf7 (object) current CF7 object
	 
	 * @since 1.0.1
	 */
	function add_meta_boxes_42( $cf7 ) {
		if ( version_compare( WPCF7_VERSION, '4.2' ) >= 0 ) {
			
			// Create the wrapper and expand/collapse pointer
			echo '<div class="wrap">';
				echo '<div id="cf7skins-42" class="postbox">';
				echo '<div title="'. __( 'Click to toggle', 'cf7skins-logic' ) .'" class="handlediv"><br></div>';
					echo '<h3 class="hndle"><span>'. __( 'Logic', 'cf7skins-logic' ) .'</span></h3>';
					echo '<div class="inside">';
						echo '<div id="cf7s" class="cf7-42">';
							$this->meta_box( $cf7 );  // add the metabox
						echo '</div>';
					echo '</div>';
				echo '</div>';
			echo '</div>';
		}
	}
	
	
	/**
	 * Generate the logic field, statements and criterias.
	 * Don't show field if Cf7 tag is deleted from textarea.
	 * Statement with deleted CF7 tag will be set automatically 
	 * to 'Select criteria', see add_statement(), 
	 * After next save, the empty statement will be deleted during save_logic(),
	 * so we don't need to do anything with statements here.
	 * 
	 * @param $cf7 (object) current CF7 object
	 * 
	 * @since 0.1.0
	 */
	function generate_logics( $cf7 ) {
		
		// Only if logic meta exists
		if( ! $meta = get_post_meta( $cf7->id(), CF7SKINSLOGIC_META, true ) )
			return;
			
		$items = $this->logic_items( $cf7 ); // get all logic items @since 1.1
		
		$tags = array(); // create array of items tag name
		
		foreach ( $items as $group => $array ) {
			if ( isset( $array['items'] ) ) {
				foreach ( $array['items'] as $tag => $item ) {
					$tags[] = $tag;
				}
			}
		}
		
		// Add the field, statements and criterias
		foreach( $meta as $field => $logic ) {
			$field = esc_attr( $field );
			
			if ( in_array( $field, $tags ) ) { // only if tag/field exists in form @since 1.1
				if ( isset( $logic['statements'] ) ) { // only if statement exists
					$this->add_field( array(
						'field' => $field, 
						'post_ID' => $cf7->id(), 
						'form' => $cf7->prop('form'), 
						'statements' => $logic['statements'],
					));
				}
			}
		}
	}
	
	
	/**
	 * Create user input element
	 * 
	 * @param $args (array) see below for default
	 * 
	 * @since 0.1.0
	 */
	function create_input( $args ) {
		$html = '';
		
		$args = wp_parse_args( (array) $args, array( // merge the user-selected arguments with the defaults.
			'name'			=> '',
			'value'			=> '',
			'type'			=> 'text',
			'class'			=> '',
			'options'		=> array(),
			'attributes'	=> array()
		));

		$attr = array(); $attributes = '';
		
		foreach ( $args['attributes'] as $data_type => $data_value ) // loop through the custom attributes and explode
			$attr[] = $data_type .'="'. $data_value[$k] .'"';
		
		if ( $attr )
			$attributes = implode( ' ', $attr );
		
		switch ( $args['type'] ) :
		
			case 'checkbox' :
				foreach( $args['options'] as $k => $v ) {
					$checked = checked( is_array( $args['value'] ) && in_array( $k, $args['value'] ), true, false ); // check if current value exists and is an array				
					$html .= "<label><input type='checkbox' $checked name='{$args['name']}[]' value='$k' />$v</label>&nbsp;";
				}
			break;
			
			case 'radio' :
				foreach( $args['options'] as $k => $v ) {
					$checked = checked( $args['value'], $k, false ); // check if current value exists and is an array
					$html .= "<label><input type='radio' $checked name='{$args['name']}' value='$k' />$v</label>&nbsp;";
				}
			break;
			
			case 'text' :
			default :
				$html .= '<input '. $attributes .'type="'. $args['type'] .'" class="'. $args['class'] .'" value="'. $args['value'] .'" name="'. $args['name'] .'" />';
			break;

		endswitch;
	
		return $html;
	}
	
	
	/**
	 * Create a select dropdown box
	 * 
	 * @param $args (array) see below for default
	 * 
	 * @since 0.1.0
	 */
	function create_select( $args ) {
		
		$args = wp_parse_args( (array) $args, array( // merge the user-selected arguments with the defaults.
			'id'			=> '',
			'name'			=> '',
			'options'		=> array(),
			'selected'		=> null,
			'default'		=> null,
			'class'			=> null,
			'attributes'	=> array()
		));
		
		$html = '<select class="'. $args['class'] .'" id="'. $args['id'] .'" name="'. $args['name'] .'">';
			if( $args['default'] ) {
				$html .= '<option value="">'. $args['default'] .'</option>';
			}
			$data_attributes = $args['attributes'];
			$attributes = array();
			
			// Option group, see cf7skins_logic_criterias filter for array format @since 1.1
			foreach ( $args['options'] as $k => $val ) {
				
				// Loop through the custom data attributes
				foreach( $data_attributes as $attr )
					if ( isset( $val[$attr] ) ) {
						$attributes[$attr] = $val[$attr]; 
					}
				$args['attributes'] = $attributes; // overwrite
				
				if ( is_array( $val ) ) {
					$html .= '<optgroup label="'. $val['name'] .'">'; // add group
					$args['options'] = isset( $val['options'] ) ? $val['options'] : array() ; // overwrite
					$html .= $this->create_select_options( $args ); // create options
				} else {
					$html .= $this->create_select_options( $args ); // create options
					break;
				}
			}
		$html .= '</select>';
		
		return $html;
	}
	
	
	/**
	 * Create select options
	 * 
	 * @param $args (array) see below for default
	 * 
	 * @return (String/HTML) select options
	 * 
	 * @since 1.1
	 */
	function create_select_options( $args ) {
		$args = wp_parse_args( (array) $args, array( // merge the user-selected arguments with the defaults.
			'options'		=> array(),
			'selected'		=> null,
			'attributes'	=> array()
		));
		
		$html = '';

		foreach ( $args['options'] as $k => $v ) {
			$attr = array(); 
			$attributes = '';
			
			// Loop through the custom attributes and explode
			foreach( $args['attributes'] as $data => $attribute )
				$attr[] = $data .'="'. $attribute .'"';
			
			if( $attr )
				$attributes = implode( ' ', $attr ) . ' '; // split and add space at the end
				
			$html .= '<option '. $attributes. selected( $args['selected'], $k, false ) .' value="'. $k .'">'. $v .'</option>';
		}

		return $html;
	}
	
	
	/**
	 * Copy/duplicate logic form
	 * 
	 * @since 0.3
	 */
	function copy_logic( $cf7 ) {
		$meta = get_post_meta( $cf7->copy_id, CF7SKINSLOGIC_META, true ); // get original logic
		update_post_meta( $cf7->id(), CF7SKINSLOGIC_META, $meta ); // do copy
	}
	
	
	/**
	 * Update Logic field list after Visual AJAX save by running
	 * JavaScript callback function 'update_logic_field'.
	 * See /js/jquery.admin.js file.
	 * 
	 * @since 1.1
	 */
	function visual_update( $callbacks ) {
		// We are using JavaScript namespace/class CF7S_Logic_Admin
		$callbacks[] = array( 'CF7S_Logic_Admin' => 'update_logic_field' );
		return $callbacks;
	}
	
	/**
	 * Add translation script.
	 * 
	 * Should be enqueued after CF7_Skins_Admin_Visual::enqueue_scripts()
	 * to have right order: wp-i18n, visual, window.
	 * 
	 * @since 2.0.2
	 */
	function enqueue_translations_script() {
		if ( method_exists( 'CF7_Skins_Admin_Visual', 'print_translation_scripts' ) ) {
			CF7_Skins_Admin_Visual::print_translation_scripts( 'visual-logic', 'cf7skins-logic', CF7SKINSLOGIC_PATH );
		}
	}

} // end class


/**
 * Logic admin panel will be available only for users with edit capability
 * http://contactform7.com/restricting-access-to-the-administration-panel/
 * 
 * @since 0.3
 */
if ( current_user_can( 'wpcf7_edit_contact_forms' ) )
	new CF7_Skins_Logic_Admin();
