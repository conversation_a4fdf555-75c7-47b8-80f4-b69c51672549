# Blank WordPress Pot
# Copyright 2014 ...
# This file is distributed under the GNU General Public License v3 or later.
msgid ""
msgstr ""
"Project-Id-Version: Contact Form 7 Skins Visual 0.7.3\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-07-05T18:36:16+02:00\n"
"PO-Revision-Date: \n"
"Last-Translator: Your Name <<EMAIL>>\n"
"Language-Team: Your Team <<EMAIL>>\n"
"Language: id\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=n != 1;\n"
"X-Textdomain-Support: yesX-Generator: Poedit 1.6.4\n"
"X-Poedit-SourceCharset: UTF-8\n"
"X-Poedit-KeywordsList: __\n"
"X-Poedit-Basepath: ../src/visual\n"
"X-Generator: Poedit 3.4.4\n"
"X-Poedit-SearchPath-0: .\n"

#: cf7skins.js:326 form.js:47219 visual.js:30573
msgid "Acceptance (confirm)"
msgstr "Penerimaan (konfirmasi)"

#: cf7skins.js:334 form.js:44272 form.js:47227 visual.js:30581
msgid "Make this checkbox checked by default"
msgstr "Buat kotak centangan ini di centang secara bawaan"

#: cf7skins.js:339 form.js:44278 form.js:47232 visual.js:30586
msgid "Make this work inversely"
msgstr "Buat ini bekerja secara terbalik"

#: cf7skins.js:344 form.js:44284 form.js:47237 visual.js:30591
msgid "Make this checkbox optional"
msgstr "Buat kotak centangan ini dicentang secara bawaan"

#: cf7skins.js:358 form.js:47251 visual.js:30605
msgid "Checkbox (option)"
msgstr "Kotak centang (pilihan)"

#: cf7skins.js:366 cf7skins.js:549 form.js:44398 form.js:47259 form.js:47442
#: visual.js:30613 visual.js:30796
msgid "Put a label first, a checkbox last"
msgstr "Buat label pertama, kotak centangan terakhir"

#: cf7skins.js:371 cf7skins.js:554 form.js:44404 form.js:47264 form.js:47447
#: visual.js:30618 visual.js:30801
msgid "Wrap each item with label element"
msgstr "Bungkus setiap item dengan elemen label"

#: cf7skins.js:376 form.js:44412 form.js:47269 visual.js:30623
msgid "Make checkboxes exclusive"
msgstr "Buat kotak centang ekslusif"

#: cf7skins.js:397 form.js:47290 visual.js:30644
msgid "Date"
msgstr "Tanggal"

#: cf7skins.js:404 cf7skins.js:493 cf7skins.js:628 cf7skins.js:651
#: cf7skins.js:680 cf7skins.js:703 form.js:47297 form.js:47386 form.js:47521
#: form.js:47544 form.js:47573 form.js:47596 visual.js:30651 visual.js:30740
#: visual.js:30875 visual.js:30898 visual.js:30927 visual.js:30950
msgid "Default value"
msgstr "Nilai bawaan"

#: cf7skins.js:409 cf7skins.js:442 cf7skins.js:498 cf7skins.js:633
#: cf7skins.js:656 cf7skins.js:685 cf7skins.js:708 form.js:44162 form.js:47302
#: form.js:47335 form.js:47391 form.js:47526 form.js:47549 form.js:47578
#: form.js:47601 visual.js:30656 visual.js:30689 visual.js:30745
#: visual.js:30880 visual.js:30903 visual.js:30932 visual.js:30955
msgid "Use this text as the placeholder of the field"
msgstr "Gunakan teks ini sebagai wadah isian ruas"

#: cf7skins.js:413 cf7skins.js:502 form.js:47306 form.js:47395 visual.js:30660
#: visual.js:30749
msgid "Range - min"
msgstr "Rentang - min"

#: cf7skins.js:417 cf7skins.js:506 form.js:47310 form.js:47399 visual.js:30664
#: visual.js:30753
msgid "Range - max"
msgstr "Rentang - max"

#: cf7skins.js:430 form.js:47323 visual.js:30677
msgid "Email"
msgstr "Surel"

#: cf7skins.js:447 cf7skins.js:661 cf7skins.js:713 form.js:47340 form.js:47554
#: form.js:47606 visual.js:30694 visual.js:30908 visual.js:30960
msgid "Akismet - this field requires author's email address"
msgstr "Akismet - ruas ini membutuhkan alamat surel pengarang"

#: cf7skins.js:459 form.js:47352 visual.js:30706
msgid "File (upload)"
msgstr "Berkas (upload)"

#: cf7skins.js:466 form.js:44503 form.js:47359 visual.js:30713
msgid "File size limit (bytes)"
msgstr "Batas ukuran berkas"

#: cf7skins.js:470 form.js:44525 form.js:47363 visual.js:30717
msgid "Acceptable file types"
msgstr "Tipe berkas diizinkan"

#: cf7skins.js:481 form.js:47374 visual.js:30728
msgid "Number"
msgstr "Nomor"

#: cf7skins.js:521 form.js:47414 visual.js:30768
msgid "Quiz"
msgstr "Kuis"

#: cf7skins.js:529 form.js:47422 visual.js:30776
msgid "Question 1"
msgstr "Pertanyaan 1"

#: cf7skins.js:530 form.js:47423 visual.js:30777
msgid "Answer 1"
msgstr "Jawaban 1"

#: cf7skins.js:532 form.js:47425 visual.js:30779
msgid "Question 2"
msgstr "Pertanyaan 2"

#: cf7skins.js:533 form.js:47426 visual.js:30780
msgid "Answer 2"
msgstr "Jawaban 2"

#: cf7skins.js:541 form.js:47434 visual.js:30788
msgid "Radio Button (option)"
msgstr "Tombol Radio (pilih)"

#: cf7skins.js:560 cf7skins.js:594 form.js:47453 form.js:47487 visual.js:30807
#: visual.js:30841
msgid "Option 1"
msgstr "Pilihan 1"

#: cf7skins.js:564 cf7skins.js:597 form.js:47457 form.js:47490 visual.js:30811
#: visual.js:30844
msgid "Option 2"
msgstr "Pilihan 2"

#: cf7skins.js:575 form.js:47468 visual.js:30822
msgid "Select (dropdown)"
msgstr "Pilih (Tarik turun)"

#: cf7skins.js:583 form.js:44386 form.js:47476 visual.js:30830
msgid "Allow multiple selections"
msgstr "Ijinkan banyak pilihan"

#: cf7skins.js:588 form.js:44392 form.js:47481 visual.js:30835
msgid "Insert a blank item as the first option"
msgstr "Masukkan item kosong sebagai pilihan pertama"

#: cf7skins.js:608 form.js:47501 visual.js:30855
msgid "Submit"
msgstr "Ajukan"

#: cf7skins.js:621 form.js:47514 visual.js:30868
msgid "Telephone"
msgstr "Telepon"

#: cf7skins.js:644 form.js:47537 visual.js:30891
msgid "Text (short text)"
msgstr "Teks ( singkat )"

#: cf7skins.js:673 form.js:47566 visual.js:30920
msgid "Textarea (long text)"
msgstr "Area teks (panjang)"

#: cf7skins.js:696 form.js:47589 visual.js:30943
msgid "URL (website link)"
msgstr "URL (tautan situs)"

#: cf7skins.js:739 form.js:47632 visual.js:30986
msgid "Fieldset (with legend)"
msgstr "Fieldset (dengan legend)"

#: cf7skins.js:742 form.js:47635 visual.js:30989
msgid "Legend .."
msgstr "Legend .."

#: cf7skins.js:748 form.js:47641 visual.js:30995
msgid "List - ol"
msgstr "List - ol"

#: cf7skins.js:757 form.js:47650 visual.js:31004
msgid "List Item - li"
msgstr "List Item - li"

#: cf7skins.js:766 form.js:47659 visual.js:31013
msgid "Paragraph - p"
msgstr "Paragrap - p"

#: cf7skins.js:986 form.js:47879 visual.js:31233
msgid "Only one reCAPTCHA per form allowed."
msgstr "Hanya satu reCAPTCHA per formulir diijinkan."

#: cf7skins.js:1000 form.js:47893 visual.js:31247
msgid "Only one submit for each form."
msgstr "Hanya satu tombol ajukan yang diijinkan setiap formulir."

#: cf7skins.js:1011 form.js:47904 visual.js:31258
msgid "Root node can not be dragged to other node."
msgstr ""

#: cf7skins.js:1143 form.js:48036 visual.js:31390
msgid "Only one %s allowed in a form."
msgstr "Hanya satu %s yang diijinkan di sebuah formulir."

#: cf7skins.js:1148 form.js:48041 visual.js:31395
msgid "Node has recaptcha children. Only one recaptcha allowed in a form."
msgstr ""
"Ruas memiliki reCAPTCHA sebagai anakan. Hanya satu reCAPTCHA diijinkan untuk "
"sebuah formulir."

#: cf7skins.js:1151 form.js:48044 visual.js:31398
msgid "Node has submit children. Only one submit allowed in a form."
msgstr ""
"Ruas memiliki tombol ajukan sebagai anakan. Hanya satu tombol yang diijinkan "
"untuk sebuah formulir."

#: cf7skins.js:1323 form.js:44125 form.js:48216 visual.js:31570
msgid "Legend"
msgstr "Lagenda"

#: cf7skins.js:1329 form.js:48222 visual.js:31576
msgid "Your Name (required)"
msgstr "Nama Anda (dibutuhkan)"

#: cf7skins.js:1335 form.js:48228 visual.js:31582
msgid "Email Address (required)"
msgstr "Alamat Surel (dibutuhkan)"

#: cf7skins.js:1341 form.js:48234 visual.js:31588
msgid "Your Message"
msgstr "Pesan Anda"

#: cf7skins.js:1346 form.js:48239 visual.js:31593
msgid "* Required"
msgstr "* Dibutuhkan"

#: cf7skins.js:1349 form.js:48242 visual.js:31596
msgid "Send"
msgstr "Kirim"

#: cf7skins.js:1380 form.js:48273 visual.js:31627
msgid "Can not save! window.cf7svisual or post ID does not exist."
msgstr "Tidak bias menyimpan! window.cf7svisual atau nomor pos tidak ada."

#: cf7skins.js:1465 form.js:48358 visual.js:31712
msgid "Dismiss this notice."
msgstr "Tutup pemberitahuan ini."

#: cf7skins.js:1469 form.js:48362 visual.js:31716
msgid "Visual saved!"
msgstr "Visual disimpan!"

#: cf7skins.js:1485 form.js:48378 visual.js:31732
msgid "Namespace %s is undefined!"
msgstr "Namespace %s belum ditentukan!"

#: cf7skins.js:1493 form.js:48386 visual.js:31740
msgid "Function %1$s.%2$s is undefined!"
msgstr "Fungsi %1$s.%2$s belum ditentukan!"

#: cf7skins.js:1501 form.js:48394 visual.js:31748
msgid "Function %s is undefined!"
msgstr "Function %s belum ditentukan!"

#: form.js:43625
msgid "Save Visual"
msgstr "Simpan Visual"

#: form.js:44044
msgid "Field Type"
msgstr "Tipe Field"

#: form.js:44057
msgid "Spinbox"
msgstr "Kotak Geser"

#: form.js:44059
msgid "Slider"
msgstr "Gelincir"

#: form.js:44068
msgid "Required"
msgstr "Diharuskan"

#: form.js:44081
msgid "Name"
msgstr "Nama"

#: form.js:44103
msgid "Condition"
msgstr "Kondisi"

#: form.js:44125
msgid "Label"
msgstr "Label"

#: form.js:44147
msgid "Default Value"
msgstr "Nilai Bawaan"

#: form.js:44181
msgid "This field requires author's name"
msgstr "Ruas ini membutuhkan nama pengarang"

#: form.js:44188
msgid "This field requires author's email address"
msgstr "Ruas ini membutuhkan alamat surel pengarang"

#: form.js:44195
msgid "This field requires author's URL"
msgstr "Ruas ini membutuhkan URL pengarang"

#: form.js:44206
msgid "Range"
msgstr "Rentang"

#: form.js:44212
msgid "Min"
msgstr "Min"

#: form.js:44219
msgid "Max"
msgstr "Max"

#: form.js:44237
msgid "Content"
msgstr "Konten"

#: form.js:44249
msgid "Allowed tags:"
msgstr "Tanda-tanda yang diijinkan:"

#: form.js:44264 form.js:44306
msgid "Options"
msgstr "Pilihan"

#: form.js:44314
msgid "Default"
msgstr "Bawaan"

#: form.js:44314
msgid "Answer"
msgstr "Jawaban"

#: form.js:44380
msgid "Add Other as last option"
msgstr "Tambahkan Lainnya sebagai pilihan terakhir"

#: form.js:44420
msgid "Other"
msgstr "Lainnya"

#: form.js:44548
msgid "Size"
msgstr "Ukuran"

#: form.js:44557
msgid "Normal"
msgstr "Normal"

#: form.js:44564
msgid "Compact"
msgstr "Padat"

#: form.js:44568
msgid "Theme"
msgstr "Thema"

#: form.js:44577
msgid "Light"
msgstr "Terang"

#: form.js:44584
msgid "Dark"
msgstr "Gelap"

#: form.js:44597
msgid "Id Attribute"
msgstr "Atribut Id"

#: form.js:44619
msgid "Class Attribute"
msgstr "Atribut Kelas"

#: form.js:44743
msgid "Save"
msgstr "Simpan"

#: form.js:44743 visual.js:28915 visual.js:28974
msgid "Done"
msgstr "Selesai"

#: form.js:44935
msgid "Drag/Move"
msgstr "Geser/Pindah"

#: form.js:44951
msgid "Collapse"
msgstr "Sembunyi"

#: form.js:44951
msgid "Expand"
msgstr "Buka"

#: form.js:45450
msgid "edit"
msgstr "ubah"

#: form.js:45460
msgid "duplicate"
msgstr "gandakan"

#: form.js:45470
msgid "delete"
msgstr "hapus"

#: form.js:45482
msgid "Add-on options"
msgstr "Pilihan add-on"

#: form.js:45781
msgid "Dismiss this notice"
msgstr "Abaikan peringatan ini"

#: form.js:46136
msgid ""
"Forms are easier to follow along with when questions are on separate pages."
msgstr ""

#: form.js:46140
msgid "CF7 Skins Multi makes separate pages possible."
msgstr ""

#: form.js:46140
msgid ""
"Your form is less likely to be completed if it’s too long. Separate it using"
msgstr ""

#: form.js:46144 form.js:46148 form.js:46156
msgid "CF7 Skins Multi."
msgstr ""

#: form.js:46144
msgid "Progress bars make long forms feel less daunting. Add this in using "
msgstr ""

#: form.js:46148
msgid "Emphasize privacy policy acceptance by putting them on a second page."
msgstr ""

#: form.js:46152
msgid "CF7 Skins Multi"
msgstr ""

#: form.js:46152
msgid " makes this easy to do."
msgstr ""

#: form.js:46152
msgid ""
"Your form is more likely to be completed if it isn’t cluttered. Separate "
"information onto different pages using"
msgstr ""

#: form.js:46157
msgid ""
"Put name and email address fields beside each other easily using CF7 Skins "
"Ready."
msgstr ""

#: form.js:46161
msgid "Read more about CF7 Skins Ready."
msgstr ""

#: form.js:46161
msgid ""
"Don’t forget that your form fields need to be mobile optimized, especially "
"if fields are beside each other."
msgstr ""

#: form.js:46165
msgid "CF7 Skins Ready handles this for you."
msgstr ""

#: form.js:46165
msgid "Make your form easier to read by grouping together similar fields."
msgstr ""

#: form.js:46169 form.js:46189 form.js:46201
msgid "CF7 Skins Ready makes this easy to do."
msgstr ""

#: form.js:46169
msgid ""
"Visually grouping similar fields together improves the flow of your form. A "
"good way to do this is by drawing a box around the group using"
msgstr ""

#: form.js:46173 form.js:46177 form.js:46197
msgid "CF7 Skins Ready."
msgstr ""

#: form.js:46173
msgid "Separate cluttered form content into 2 columns or more using"
msgstr ""

#: form.js:46177
msgid "Looking to arrange your form fields into 2 or more columns?"
msgstr ""

#: form.js:46181
msgid "CF7 Skins Ready "
msgstr ""

#: form.js:46181
msgid "makes this easy to do."
msgstr ""

#: form.js:46181
msgid ""
"Increase the chances of your form being completed by aligning the fields. "
"Use the grid structure in"
msgstr ""

#: form.js:46185
msgid "CF7 Skins Ready"
msgstr ""

#: form.js:46185
msgid "to get a head start."
msgstr ""

#: form.js:46185
msgid "More complex form layouts should follow a grid system."
msgstr ""

#: form.js:46189
msgid ""
"You can align fields, put them side-by-side, and more with our easy-to-use"
msgstr ""

#: form.js:46193
msgid "CF7 Skins Ready Add-on."
msgstr ""

#: form.js:46193
msgid ""
"Have a yes/no radio button field? Display them horizontally on the same line "
"using"
msgstr ""

#: form.js:46197
msgid ""
"Very good, good, neutral? For a survey form, put checkboxes or radio buttons "
"on the same horizontal line."
msgstr ""

#: form.js:46202
msgid ""
"It is more professional looking to have a form style that matches your site."
msgstr ""

#: form.js:46206
msgid "Find a better fit in our CF7 Skins Pro Styles."
msgstr ""

#: form.js:46206
msgid "Having more templates means you save time creating forms from scratch."
msgstr ""

#: form.js:46210
msgid "Get more templates to choose from."
msgstr ""

#: form.js:46210
msgid "For custom style options, you can use CSS to adjust the form’s style."
msgstr ""

#: form.js:46213
msgid "To get help, our premium email support team is available."
msgstr ""

#: form.js:46213
msgid "Ensure error-free forms by using templates and the visual editor."
msgstr ""

#: form.js:46216
msgid "Get more templates with the CF7 Skins Pro Add-on."
msgstr ""

#: form.js:46217
msgid "Show specific fields to customers based on their responses with"
msgstr ""

#: form.js:46220 form.js:46226
msgid "CF7 Skins Logic."
msgstr ""

#: form.js:46220
msgid ""
"You may not need to show all of the possible fields to all of your customers."
msgstr ""

#: form.js:46223
msgid "Keep forms short & relevant with CF7 Skins Logic."
msgstr ""

#: form.js:46223
msgid "You can dynamically show and hide form fields using"
msgstr ""

#: form.js:46226
msgid "Dynamic form fields are possibly when you add logic to the fields."
msgstr ""

#: form.js:46229
msgid "CF7 Skins Logic makes dynamic forms possible."
msgstr ""

#: form.js:46230
msgid ""
"Feeling frustrated? Get 1-1 personalized, real-human responses to your tough "
"questions with any of our"
msgstr ""

#: form.js:46233
msgid "CF7 Skins Add-ons."
msgstr ""

#: form.js:46233
msgid ""
"Make sure your form is set up correctly the first time by talking to our "
"trained support staff, available with our"
msgstr ""

#: form.js:46236
msgid "CF7 Skins Pro Add-on."
msgstr ""

#: form.js:46236
msgid ""
"The WordPress support community is available to answer questions. Or, save "
"time searching & ask us directly with our "
msgstr ""

#: form.js:46239 form.js:46242
msgid "Premium Email support."
msgstr ""

#: form.js:46239
msgid ""
"Double check your mail settings to avoid problems sending & receiving forms. "
"Set it up correctly from the start with our"
msgstr ""

#: form.js:46266
msgid "Tip"
msgstr ""

#: form.js:46377
msgid "FIELDS (CF7 TAGS)"
msgstr "RUAS (TAG CF7)"

#: form.js:46379
msgid ""
"Drag and drop or click a Contact Form 7 Tag to add it to the visual form "
"editor."
msgstr ""
"Seret dan lepas atau klik Tag Contact Form 7 untuk menambahkannya ke editor "
"formulir visual."

#: form.js:46379
msgid ""
"To see how Fields are used, select a template it's a great way to get "
"started quickly."
msgstr ""
"Untuk melihat bagaimana Fields digunakan, pilih templat itu cara yang bagus "
"untuk memulai dengan cepat."

#: form.js:46379
msgid "Any field content can be changed by clicking Edit on the field."
msgstr ""
"Konten field apa pun dapat diubah dengan mengeklik Edit pada field tersebut."

#: form.js:46394
msgid "FIELDS (CF7 SKINS ITEMS)"
msgstr "RUAS (ITEM CF7 SKINS)"

#: form.js:46396
msgid "Use CF7 Skins Fields for the layout of your form."
msgstr "Gunakan CF7 Skins Fields untuk tata letak formulir Anda."

#: form.js:46396
msgid ""
"Fieldsets, legends, and lists are used to group related fields, and allow "
"for more detailed styling."
msgstr ""
"Fieldset, legend dan list digunakan untuk mengelompokkan ruas yang "
"berhubungan, dan memberikan penggaya yang terinci."

#: form.js:46521 form.js:46526 visual.js:28902 visual.js:28903
msgid "Form Options"
msgstr "Opsi Formulir"

#: form.js:46522 visual.js:28900
msgid "Configure this forms options"
msgstr "Konfigurasikan opsi formulir ini"

#: form.js:46535 form.js:46540 visual.js:28870 visual.js:28955
msgid "Visual Data"
msgstr "Data Visual"

#: form.js:46536
msgid "Copy & paste visual form data"
msgstr "Salin & tempel data formulir visual"

#: form.js:46803
msgid "Error, the result is not an object or null."
msgstr "Salah, hasilnya bukan merupakan sebuah object atau null."

#: form.js:46810
msgid "Error: treeData, form or callbacks property is not available."
msgstr "Salah: property treeData, form ataupun callbacks tidak tersedia."

#: form.js:47001
msgid "Error, templateTree is not an array!"
msgstr "Salah, templateTree bukanlah sebuah array!"

#: form.js:47103
msgid ""
"Select a template that closely matches your needs then switch to the CF7 "
"Skins Form  tab to add, duplicate or remove fields to match your "
"requirements. Any field content can be changed by clicking Edit on the field."
msgstr ""

#: form.js:47104
msgid ""
"Templates are pre-created forms that automatically create the form’s "
"structure and content for you. Each template works as an easy to follow "
"guide."
msgstr ""

#: visual.js:28856
msgid "Save Form"
msgstr "Simpan Formulir"

#: visual.js:28868
msgid "Copy & paste visual data"
msgstr "Salin & tempel data visual"

#: visual.js:29739
msgid "Unable to add the item."
msgstr "Tidak bias menambah item itu."

#: visual.js:29775
msgid "There is no edit field available for this item."
msgstr "Tidak ada pengubahan yang tersedia untuk item ini."

#: visual.js:29890
msgid "Unable to duplicate!"
msgstr "Tidak bisa menggandakan!"

#: visual.js:29960
msgid "Unable to change the item."
msgstr "Tidak dapat mengubah item ini."

#: visual.js:30165
msgid "Visual Object Data is not valid ( JSON parse error )"
msgstr "Data Obyek Visual tidak sah ( kesalahan penguraian JSON )"

#: visual.js:30172
msgid "Visual data is empty."
msgstr "Data Visual tidak ada."

#: visual.js:30190
msgid "Visual data is not valid."
msgstr "Data Visual tidak sah."

#~ msgid "See paragraph permitted phrasing content specification"
#~ msgstr "Lihat spesifikasi konten yang diijikan untuk paragraf"

#~ msgid "Visual Object Data is not valid ( Invalid Cf7sType )"
#~ msgstr "Data Obyek Visual tidak sah ( Cf7sType tidak sah )"

#~ msgid ""
#~ "Drag and drop or click a Contact Form 7 Tag to add it to the visual form "
#~ "editor. To see how Fields are used, select a template it's a great way to "
#~ "get started quickly. Any field content can be changed by clicking Edit on "
#~ "the field."
#~ msgstr ""
#~ "Geser dan letakkan atau klik di Contact Form 7 untuk menambah ke editor "
#~ "formulir visual. Untuk melihat bagaiman Ruas digunakan, pilihlah sebuah "
#~ "templat yang merupakan cara cepat untuk memulai. Segala konten ruas dapat "
#~ "diubah dengan melakukan klik Ubah di ruas tersebut."
